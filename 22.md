### 系统模式（TDD/FDD）配置(0xF001)

**是否立即生效**：否

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_SYS_MODE_ACK(0xF002)`

**功能描述**：
此消息用于设置系统模式（TDD/FDD），基站收到此配置，记录启动模式。客户端下发reboot指令或者重新上下电给基站，基站会根据配置的模式启动系统。当`sysMode=2`时，系统根据硬件一个GPIO（PIN5）管脚的输入信号决定启动TDD还是FDD，PINI5悬空时启动TDD，PIN5接地时启动FDD，仅V3系列板卡支持此功能。仅TDD和FDD共版版本才支持此设置，非共版版本基站会回复失败。

**消息结构：`O_FL_LMT_TO_ENB_SYS_MODE_CFG`**

| 参数名称          | 数据类型 | 取值范围                                  | 含义     |
| :---------------- | :------- | :---------------------------------------- | :------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                                 | 消息头 (`0xF001`) |
| `sysMode`         | `U32`    | 0: TDD， 1: FDD, 2: 硬件上区分启动是TDD还FDD | 协议栈模式 |

---
### 扫频频点配置(0xF009)

**是否立即生效**：是

**重启是否保留配置**：是。

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_SYS_ARFCN_ACK (0xF004)`

**功能描述**：
TDD模式下，此接口用于客户端在基站IDLE态时开始SCAN公网LTE小区参数的流程。无需配置Band ID，基站会根据频点自动计算。基站支持RX和SINNIFER 2个端口扫频，基站默认版本是RX口，通过`O_FL_LMT_TO_ENB_REM_ANT_CFG`消息可配置扫频端口。`wholeBandRem`字段指示是否开启全频段扫频，若开启全频段扫频(`wholeBandRem=1`)，板卡在扫完配置的频点后，继续搜索公网小区SIB5中配置的其他邻区频点。基站单板本身支持的扫频范围是（50MHZ~4GHz），但若整机系统中RX或者SINNIFFER口外接了限制接收频率的硬件（比如滤波器），则配置频点时应仅限于属于该频段的频点，且配置`wholeBandRem`为0。
*注：TDD模式下，空口同步、频偏校准和自配置流程会修改扫频频点配置。*

FDD模式下，此接口用于客户端在基站IDLE态时开始SCAN公网LTE小区参数的流程。无需配置Band ID，基站会根据频点自动计算。FDD模式只支持用SNF端口扫频，基站默认版本是SNF口。`wholeBandRem`字段指示是否开启全频段扫频，若开启全频段扫频(`wholeBandRem=1`)，板卡在扫完配置的频点后，继续搜索公网小区SIB5中配置的其他邻区频点。
*注：FDD模式下，服务小区参数配置和频偏校准流程会修改扫频频点配置。*

**消息结构：`O_FL_LMT_TO_ENB_REM_CFG`**

| 参数名称          | 数据类型 | 取值范围 | 含义           |
| :---------------- | :------- | :------- | :------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头（0xF009） |
| `wholeBandRem`    | `U32`    | 0：不开启；1：开启 | 是否开启全频段扫频 |
| `sysEarfcnNum`    | `U32`    | 1~10     | 扫频频点数目     |
| `sysEarfcn[10]`   | `U32`    | 频点数组 | 频点，如38400等  |

---
### 扫频端口配置(0xF07D)

**是否立即生效**：是

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_REM_ANT_CFG_ACK(0xF07E)`

**功能描述**：
此接口仅用于配置TDD扫频端口，目前支持RX和SINNIFER 2个端口模式。

**消息结构：`O_FL_LMT_TO_ENB_REM_ANT_CFG`**

| 参数名称          | 数据类型 | 取值范围 | 含义     |
| :---------------- | :------- | :------- | :------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF07D) |
| `RxorSnf`         | `U32`    | 0：Rx  1：SNF | 端口类型 |

---
### 基站重启配置(0xF00B)

**是否立即生效**：是

**重启是否保留配置**：否

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_REBOOT_ACK (0xF00C)`

**功能描述**：
此接口用于客户端指示基站执行reboot操作。基站收到此消息，先回复ACK，再执行reboot。基站处于任何状态都会处理该消息。

**消息结构：`O_FL_LMT_TO_ENB_REBOOT_CFG`**

| 参数名称          | 数据类型 | 取值范围                          | 含义                                                         |
| :---------------- | :------- | :-------------------------------- | :----------------------------------------------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                               | 消息头`0xF00B`                                                 |
| `SelfActiveCfg`   | `U32`    | 0：reboot后自动激活小区  1：reboot后不自激活小区 | 指示基站重启后是否采用现有参数配置自动激活小区，该字段只有在定位版本中生效，围栏版本不需要判断该字节。 |

---
### 小区激活去激活配置(0xF00D)

**是否立即生效**：是

**重启是否保留配置**：否

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_SET_ADMIN_STATE_ACK(0xF00E)`

**功能描述**：
在基站IDLE状态下，可通过此消息指示基站采用当前小区配置参数激活小区，如果`workAdminState`配置为1，TDD基站则不进行同步流程，直接激活小区，如果`workAdminState`配置为2，TDD基站先执行同步流程，同步成功后再激活小区，如果同步失败，基站仍然回到IDLE状态。
在基站激活状态下，通过配置`workAdminState`为0，基站则会执行去激活小区的操作，进入IDLE状态。

**消息结构：`O_FL_LMT_TO_ENB_SET_ADMIN_STATE_CFG`**

| 参数名称          | 数据类型 | 取值范围                                       | 含义 |
| :---------------- | :------- | :--------------------------------------------- | :--- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                                            | 消息头(0xF00D) |
| `workAdminState`  | `U32`    | 0:去激活小区 1:采用当前配置激活小区，如果是TDD不执行同步 2:激活小区&同步，仅TDD支持 | /    |

---
### 接收增益配置(0xF013)

**是否立即生效**：是

**重启是否保留配置**：可配置

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_SYS_RxGAIN_ACK(0xF014)`

**功能描述**：
该接口用于配置基站9361寄存器的接收增益，表示将接收到的来自UE的信号放大多少倍。接收增益配置值说明参考《[2.4 接收增益](#_接收增益)》。

**消息结构：`O_FL_LMT_TO_ENB_SYS_RxGAIN_CFG`**

| 参数名称          | 数据类型 | 取值范围           | 含义                                                         |
| :---------------- | :------- | :----------------- | :----------------------------------------------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                | 消息头(0xF013)                                               |
| `Rxgain`          | `U32`    | 0~127(dB)          | 接收增益                                                     |
| `RxGainSaveFlag`  | `U8`     | 0: not save, 1: save | 配置值是否保存到配置，重启之后也保留                         |
| `RxOrSnfFlag`     | `U8`     | 0：rx  1：snf     | 配置该增益是修改rx口增益还是snf口增益 注：仅FDD有效 对于TDD，该字段无意义，基站不做判断。 |
| `Res[2]`          | `U8`     | 0                  | 保留字节                                                     |

---
### 发射功率衰减配置(0xF015)

**是否立即生效**：是

**重启是否保留配置**：可配置

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_SYS_PWR1_DEREASE_ACK(0xF016)`

**功能描述**：
该接口用于配置基站发送通道的衰减值，用于客户校准整机输出功率。衰减值每加4，基站输出功率增加1dB衰减。无衰减时，即衰减值为0x00时，基站输出功率范围在-1dbm~-2dbm，每块单板会有差异。

**基站实际输出功率 = 零衰减功率 - 衰减值（Pwr1Derease\*0.25）**

例如：基站输出功率为-1dB，当衰减值设置为0x28，输出功率为-11dBm。

**消息结构：`O_FL_LMT_TO_ENB_SYS_PWR1_DEREASE_CFG`**

| 参数名称          | 数据类型 | 取值范围   | 含义                                                         |
| :---------------- | :------- | :--------- | :----------------------------------------------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`        | 消息头(0xF015)                                               |
| `Pwr1Derease`     | `U32`    | `0x00~0xFF` | 功率衰减，每步长代表0.25dB                                     |
| `IsSave`          | `U8`     | 1: 重启保留配置  0：设备重启不保留配置 | 配置值是否保存到配置，重启之后也保留                         |
| `Res [3]`         | `U8`     | 0          | 保留字节                                                     |

---
### UE重定向信息配置(0xF017)

**是否立即生效**：是

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_REDIRECT_INFO_ACK (0xF018)`

**功能描述**：
此接口用于配置基站发送给UE的释放消息中是否携带重定向参数，默认不携带重定向参数。

**消息结构：`O_FL_LMT_TO_ENB_REDIRECT_INFO_CFG`**

| 参数名称          | 数据类型 | 取值范围      | 含义         |
| :---------------- | :------- | :------------ | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`           | 消息头(0xF017) |
| `OnOff`           | `U32`    | 0：打开  1：关闭 | 重定向开关   |
| `Earfcn`          | `U32`    | 0~65535       | 重定向频点   |
| `RedirectType`    | `U32`    | 0：4G  1：3G  2：2G | 重定向类型   |

---
### 基站IP配置(0xF01B)

**是否立即生效**：否

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_IP_CFG_ACK (0xF01C)`

**功能描述**：
该接口用于修改基站的IP配置。版本默认基站地址是"************#*************#***********#"。

**消息结构：`O_FL_LMT_TO_ENB_IP_CFG`**

| 参数名称          | 数据类型 | 取值范围                                                 | 含义       |
| :---------------- | :------- | :------------------------------------------------------- | :--------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                                                      | 消息头(0xF01B) |
| `eNBIPStr[52]`    | `U8`     | IP配置字符串，以'\\0'结束。例如："************#*************#***********#" | 设置基站的IP |

---
### 设置单板时间(0xF01F)

**是否立即生效**：是

**重启是否保留配置**：否

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_SET_SYS_TMR_ACK (0xF020)`

**功能描述**：
该接口用于设置单板系统时间。

**消息结构：`O_FL_LMT_TO_ENB_SET_SYS_TMR`**

| 参数名称          | 数据类型 | 取值范围                        | 含义         |
| :---------------- | :------- | :------------------------------ | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                             | 消息头(0xF01F) |
| `Time[20]`        | `U8`     | 字符串，如"2019.04.29-12:13:50"，以'\\0'结尾。 | 单板系统时间设置 |

---
### QRxLevMin配置(0xF021)

**是否立即生效**：重启生效

**重启是否保留配置**：是

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_SET_QRXLEVMIN_ACK(0xF022)`

**功能描述**：
该接口用于配置最小接收电平。

**消息结构：`O_FL_LMT_TO_ENB_SET_QRXLEVMIN`**

| 参数名称          | 数据类型 | 取值范围     | 含义     |
| :---------------- | :------- | :----------- | :------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`          | 消息头(0xF021) |
| `s8QrxlevMin`     | `S8`     | (-70 ~ -22) | /        |
| `Reserved1[3]`    | `U8`     |              | 保留字节 |

---
### 基站TDD/FDD同步方式配置(0xF023)

**是否立即生效**：是

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_REM_MODE_CFG_ACK (0xF024)`

**功能描述**：
此接口用于设置基站的同步方式，目前仅支持空口和GPS同步。

**消息结构：`O_FL_LMT_TO_ENB_REM_MODE_CFG`**

| 参数名称          | 数据类型 | 取值范围             | 含义                                     |
| :---------------- | :------- | :------------------- | :--------------------------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                  | 消息头(0xF023)                           |
| `Remmode`         | `U32`    | 0：空口同步；  1：GPS同步 | TDD模式支持空口和GPS同步，FDD仅支持GPS，用于频率同步。 |

---
### 客户端IP配置(0xF025)

**是否立即生效**：否

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_LMTIP_CFG_ACK (0xF026)`

**功能描述**：
该接口用于配置客户端IP配置，版本默认客户端地址是"************#3345"。
注意：该接口中的端口字段，单板在UDP模式下，也使用该端口值进行监听。端口默认3345，若更改，建议配置3350---3399。

**消息结构：`O_FL_LMT_TO_ENB_LMTIP_CFG`**

| 参数名称          | 数据类型 | 取值范围        | 含义                                 |
| :---------------- | :------- | :-------------- | :----------------------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`             | 消息头(0xF025)                       |
| `LMTIPStr[32]`    | `U8`     | "************#3345" | 设置主控板的IP和端口,字符串，以'\\0'结束 |

---
### GPS同步模式下的pp1s偏移量配置(0xF029)

**是否立即生效**：否，需要重新进行GPS同步生效。

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_GPS_PP1S_ACK(0xF02A)`

**功能描述**：
在TDD选择GPS同步模式下，此接口用于设置同步偏移量，在中国，一般band39,band40需要进行GPS同步偏移量调节,一般-700微秒（OffsetTime）左右数据帧头偏移（正值说明时域相对原始值向后移动，负值说明是时域对应原始值向前移动），具体各个BAND的偏移量以实际测量为准。
接口中设置的`Gpspps1s = OffsetTime * （Gpspps1sToBW/微秒）`
`OffsetTime`：运营商网络此BAND相对于GPS的偏移量，单位微秒；
`Gpspps1sToBW/微秒`：相关带宽下每微秒的偏移值，带宽是指本基带板的带宽；

**1微秒的偏移情况下，Gpspps1s与带宽对应关系如下：**

| Bandwidth         | （5M） | （10M） | （20M） |
| :---------------- | :----- | :------ | :------ |
| Gpspps1sToBW/微秒 | 7.68   | 15.36   | 30.72   |

例如：基站配置20M带宽，BAND40偏移-700微妙，则接口中配置的`Gpspps1s=-700*30.72`。

**消息结构：`O_FL_LMT_TO_ENB_GPS_PP1S_CFG`**

| 参数名称          | 数据类型 | 取值范围 | 含义           |
| :---------------- | :------- | :------- | :------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF029) |
| `Gpspps1s`        | `S32`    | `/`      | Gps pps1s偏移量 |

---
### 上电自动激活小区配置(0xF03B)

**是否立即生效**：否

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_SELF_ACTIVE_CFG_PWR_ON_ACK (0xF03C)`

**功能描述**：
上电自激活配置：用于配置基站上电启动时是否执行自动激活小区的流程，默认版本中上电不自动激活小区，进入IDLE状态。
Reboot配置：Wl模式下，用于配置除带宽改变时，reboot是否执行自动激活小区的流程，默认版本中reboot自动激活小区，进入active状态。

**消息结构：`O_FL_LMT_TO_ENB_SELF_ACTIVE_CFG_PWR_ON`**

| 参数名称            | 数据类型 | 取值范围                          | 含义                                                         |
| :------------------ | :------- | :-------------------------------- | :----------------------------------------------------------- |
| `WrmsgHeaderInfo`   | `wrMsgHeader` | `/`                               | 消息头(0xF03B)                                               |
| `SelfActiveCfg`     | `U32`    | 0：上电自动激活小区  1：上电不自动激活小区 | /                                                            |
| `rebootSelfActiveCfg` | `U32`    | 0：reboot自动激活小区  1：reboot不自动激活小区 | 仅WL版本有效                                                 |

---
### TDD子帧配置(0xF049)

**是否立即生效**：否

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_TDD_SUBFRAME_ASSIGNMENT_SET_ACK (0xF04A)`

**功能描述**：
此消息接口用于配置TDD小区的子帧配比，小区去激活状态下配置立即生效。

**消息结构：`O_FL_LMT_TO_ENB_TDD_SUBFRAME_ASSIGNMENT_SET`**

| 参数名称                 | 数据类型 | 取值范围  | 含义         |
| :----------------------- | :------- | :-------- | :----------- |
| `WrmsgHeaderInfo`        | `wrMsgHeader` | `/`       | 消息头(0xF049) |
| `u8TddSfAssignment`      | `U8`     | 1：sa1  2：sa2 | TDD子帧配比  |
| `u8TddSpecialSfPatterns` | `U8`     | 5：ssp5  7：ssp7 | TDD特殊子帧配比 |
| `Res[2]`                 | `U8`     | `/`       | 保留字节     |

---
### GPS经纬度信息复位配置(0xF06D)

**是否立即生效**：是

**重启是否保留配置**：否

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_GPS_INFO_RESET_ACK (0xF06E)`

**功能描述**：
基站启动时，会自动获取GPS经纬度信息，获取的经纬度信息可通过4.9章节的《[GPS经纬高度查询](#_Gps经纬高度查询)》接口查询，一旦获取到经纬度信息，基站会保存此次获取的值，下次重启将不再重复获取，因此如果基站移动了位置，请使用此接口清除上一次获取的经纬度信息。

**消息结构：`O_FL_LMT_TO_ENB_GPS_INFO_RESET`**

| 参数名称          | 数据类型 | 取值范围 | 含义         |
| :---------------- | :------- | :------- | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF06D) |

---
### 辅PLMN列表配置(0xF060)

**是否立即生效**：否

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_SECONDARY_PLMNS_SET_ACK (0xF061)`

**功能描述**：
此接口用于配置基站广播SIB1中PLMN LIST字段中的非主PLMN。

**消息结构：`O_FL_LMT_TO_ENB_SECONDARY_PLMNS_SET`**

| 参数名称            | 数据类型 | 取值范围                                 | 含义         |
| :------------------ | :------- | :--------------------------------------- | :----------- |
| `WrmsgHeaderInfo`   | `wrMsgHeader` | `/`                                      | 消息头(0xF060) |
| `u8SecPLMNNum`      | `U8`     | `1~5`                                    | 辅PLMN的数目 |
| `u8SecPLMNList[5][7]` | `U8`     | 字符数组，以结束符结束eg: "46000" "46001" | 辅PLMN列表   |

---
### 启动小区自配置请求(0xF04F)

**是否立即生效**：是

**重启是否保留配置**：否

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_SELFCFG_CELLPARA_REQ_ACK (0xF050)`

**功能描述**：
此接口用于指示基站在IDLE态开始小区自配置流程，小区自配置流程图参考《[小区自配置流程](#_小区自配置流程)》。基站收到此消息，根据自配置频点列表搜索公网频点和小区信息，基站会根据扫频结果，选择本小区参数自动建立小区。如果整机设备支持全频段，可以配置`SelfBand`为`0xFF`，基站将会对自配置频点列表中所有频点以及公网广播消息SIB5中的频点进行全频段扫频。

**消息结构：`O_FL_LMT_TO_ENB_SELFCFG_CELLPARA_REQ`**

| 参数名称          | 数据类型 | 取值范围                                  | 含义           |
| :---------------- | :------- | :---------------------------------------- | :------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                                       | 消息头(0xF04F) |
| `SelfBand`        | `U8`     | 38,39,40,41（TDD频段） 255                 | 指定自配置的频段 |
| `Res[3]`          | `U8`     | 0                                         | 保留字节       |

**应答消息结构：`O_FL_ENB_TO_LMT_SELFCFG_CELLPARA_REQ_ACK`**

| 参数名称          | 数据类型 | 取值范围                                     | 含义     |
| :---------------- | :------- | :------------------------------------------- | :------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                                          | 消息头 (0xF050) |
| `CfgResult`       | `U32`    | 0:成功  1:配置失败  2:自配置后台频点列表中未含指定频段的频点 | 配置结果 |

---
### 小区自配置后台频点添加/删除(0xF051)

**是否立即生效**：是

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_SELFCFG_ARFCN_CFG_REQ_ACK(0xF052)`

**功能描述**：
此接口用于配置小区自配置功能的扫频频点列表。

**消息结构：`O_FL_LMT_TO_ENB_SELFCFG_ARFCN_CFG_REQ`**

| 参数名称          | 数据类型 | 取值范围    | 含义     |
| :---------------- | :------- | :---------- | :------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`         | 消息头(0xF051) |
| `Cfgtype`         | `U32`    | 0:增加后台频点  1:删除后台频点 | /        |
| `EarfcnValue`     | `U32`    | `0~65535`   | 频点值   |

**`O_FL_ENB_TO_LMT_SELFCFG_ARFCN_CFG_REQ_ACK`**

| 参数名称          | 数据类型 | 取值范围                                             | 含义   |
| :---------------- | :------- | :--------------------------------------------------- | :----- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                                                  | 消息头  (0xF052) |
| `CfgResult`       | `U32`    | 0:操作成功；  1:失败，添加频点重复；  2:失败，添加频点溢出；  3:失败，删除不存在的频点  4:失败，频点值无效 | 配置结果 |

---
### UE NAS REJECT CAUSE配置(0xF057)

**是否立即生效**：是

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_TAU_ATTACH_REJECT_CAUSE_CFG_ACK(0xF058)`

**功能描述**：
此接口用于配置基站把接入UE踢回公网时，回复UE的TAU REJECT或者ATTACH REJECT消息中的reject cause值，基站默认使用cause#15，一般此值不需要修改。

**消息结构：`O_FL_LMT_TO_ENB_TAU_ATTACH_REJECT_CAUSE_CFG`**

| 参数名称          | 数据类型 | 取值范围                                     | 含义                                                         |
| :---------------- | :------- | :------------------------------------------- | :----------------------------------------------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                                          | 消息头(0xF057)                                               |
| `RejectCause`     | `U32`    | 0：#cause15 （追踪区不允许接入） 1：#cause12 (追踪区无合适小区)  2: #cause3 （无效终端） 3：#cause13  4：#cause22 | 回复UE的TAU REJECT或者ATTACH REJECT消息中的reject cause值      |

---
### 频偏配置(0xF059)

**是否立即生效**：重启生效

**重启是否保留配置**：是

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_FREQ_OFFSET_CFG_ACK (0xF05A)`

**功能描述**：
设置频偏。

**消息结构：`O_FL_LMT_TO_ENB_FREQ_OFFSET_CFG`**

| 参数名称          | 数据类型 | 取值范围     | 含义 |
| :---------------- | :------- | :----------- | :--- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`          | 消息头(0xF059) |
| `FreqOffset`      | `U32`    | `0-4294967295` | 频偏 |

---
### 异频同步接口配置(0xF05E)

**是否立即生效**：是

**重启是否保留配置**：是（SNF端口和时偏保留，频点有场景保留有场景不保留）

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_SYNCARFCN_CFG_ACK(0xF05F)`

**功能描述**：
该接口用于tdd空口异频同步，该接口只是个配置接口，配置完该接口要配置`O_FL_LMT_TO_ENB_SYS_ARFCN_CFG`接口，该功能才生效；如果SNF接口一旦配置，要改成同频同步需要用本接口的RX接口配置及同频频点配置回来才能继续使用同频同步功能（注意时偏也要配置回来）。

注：配置的频点，自动建小区保留，不自动建小区不保留，需要客户设计后台时，只要上电/reboot不自动建小区，需要重新配置该接口，再配合`O_FL_LMT_TO_ENB_SYS_ARFCN_CFG`接口使用；reboot后自动建小区，如果第一次同步不成功也要重新配置该接口再配合`O_FL_LMT_TO_ENB_SYS_ARFCN_CFG`接口使用。

**消息结构：`O_FL_LMT_TO_ENB_SYNCARFCN_CFG`**

| 参数名称          | 数据类型 | 取值范围 | 含义                                       |
| :---------------- | :------- | :------- | :----------------------------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF05E)                             |
| `UsingSnfSwitch`  | `U8`     | `/`      | 0: 使用Rx同步  1：使用SNF同步  2: 取消异频同步 |
| `Reserved1[3]`    | `U8`     | `/`      | 保留字节                                   |
| `SyncArfcn`       | `U32`    | `/`      | 同步频点                                   |
| `TimeOffsetPresent` | `U8`     | `/`      | 0：不需要配置  1：需要配置                 |
| `Reserved2[3]`    | `U8`     | `/`      | 保留字节                                   |
| `S32`             | `TimeOffsetValue` | `/`      | 异频同步时偏值（不同band间）               |

---
### TAC配置(0xF069)

**是否立即生效**：是

**重启是否保留配置**：否

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_TAC_MODIFY_REQ_ACK (0xF06A)`

**功能描述**：
此接口用于修改基站的当前TAC值。

**消息结构：`O_FL_LMT_TO_ENB_TAC_MODIFY_REQ`**

| 参数名称    | 数据类型 | 取值范围  | 含义 |
| :---------- | :------- | :-------- | :--- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`       | 消息头(0xF069) |
| `TacValue`  | `U32`    | `0~65535` | /    |

---
### 动态修改小区参数(0xF080)

**是否立即生效**：是

**重启是否保留配置**：否

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_SYS_ARFCN_MOD_ACK (0xF081)`

**功能描述**：
此接口用于在小区激活态下，即时修改小区参数，但是此时修改的小区参数重启或者断电之后不会保存。如果当前小区没有激活，会返回配置失败。

**消息结构：`O_FL_LMT_TO_ENB_SYS_ARFCN_MOD`**

| 参数名称          | 数据类型 | 取值范围                              | 含义         |
| :---------------- | :------- | :------------------------------------ | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                                   | 消息头  (0xF080) |
| `ulEarfcn`        | `U32`    | TDD: 255  FDD:实际频点，同sysBand对应 | 上行频点     |
| `dlEarfcn`        | `U32`    | `0~65535`                             | 下行频点     |
| `PLMN[7 ]`        | `U8`     | 字符数组，以结束符结束eg:"46000"     | plmn         |
| `Band`            | `U8`     | 需要跟上面的频点参数匹配              | 频段         |
| `CellId`          | `U32`    | `0~65535`                             | 小区Id       |
| `UePMax`          | `U32`    | `0~23dBm`                             | 终端最大发射功率 |

---
### NTP服务器IP配置(0xF075)

**是否立即生效**：否

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_NTP_SERVER_IP_CFG_ACK (0xF076)`

**功能描述**：
此接口用于设置基站NTP时间同步的NTP服务器IP，系统启动时会自动进行NTP时间同步。

**消息结构：`O_FL_LMT_TO_ENB_NTP_SERVER_IP_CFG`**

| 参数名称          | 数据类型 | 取值范围          | 含义             |
| :---------------- | :------- | :---------------- | :--------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`               | 消息头(0xF075)   |
| `ntpServerIp[20]` | `U8`     | 字符数组，以结束符结束 | Ntp服务器ip地址 |

---
### 定点重启配置(0xF086)

**是否立即生效**：否

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_TIME_TO_RESET_CFG_ACK (0xF087)`

**功能描述**：
此接口用于配置基站是否开启定点重启功能，基站系统采用NTP同步方式获取系统格林威治时间，如果开启此功能，请设置正确的NTP服务器IP。版本发布默认此功能关闭。

**消息结构：`O_FL_LMT_TO_ENB_TIME_TO_RESET_CFG`**

| 参数名称          | 数据类型 | 取值范围          | 含义     |
| :---------------- | :------- | :---------------- | :------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`               | 消息头(0xF086) |
| `ResetSwitch`     | `U8`     | 0：关闭  1：打开 | 定点重启开关 |
| `Res[3]`          | `U8`     | `0`               | 保留字节 |
| `ResetTime[12]`   | `S8`     | 字符串            | 重启时间配置 |

---
### 开启IMEI捕获功能配置(0xF08A)

**是否立即生效**：是

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_IMEI_REQUEST_CFG_ACK (0xF08B)`

**功能描述**：
此接口用于配置基站是否开启获取UE的IMEI功能，由于IMEI的获取，基站会对接入的UE先释放让其重新接入，会影响高速抓号的成功率，因此版本默认是关闭功能，客户可根据自己的需要决定是否开启此功能。根据测试，获取UE IMEI相对于IMSI的比例大概是10~15%。

**消息结构：`O_FL_LMT_TO_ENB_IMEI_REQUEST_CFG`**

| 参数名称          | 数据类型 | 取值范围    | 含义             |
| :---------------- | :------- | :---------- | :--------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`         | 消息头(0xF08A)   |
| `ImeiEnable`      | `U8`     | 0：关闭  1：开启 | 是否开启IMEI获取功能 |
| `Res[3]`          | `U8`     | `0`         | 保留字节         |

---
### IMSI加密密钥配置(0xF08C)

**是否立即生效**：是

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_IMSI_TRANS_KENC_CFG_ACK (0xF08D)`

**功能描述**：
（无详细功能描述）

**消息结构：`O_FL_LMT_TO_ENB_IMSI_TRANS_KENC_CFG_ACK`**

| 参数名称          | 数据类型 | 取值范围   | 含义       |
| :---------------- | :------- | :--------- | :--------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`        | 消息头(0xF08C) |
| `ImsiKenc[16]`    | `U8`     | 十进制数字数组 | IMSI加密密钥 |

---
### UE重定向模式动态黑名单配置(0xF08E)

**是否立即生效**：是

**重启是否保留配置**：否

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_UE_REDIRECT_IMSI_LIST_CFG_ACK (0xF08F)`

**功能描述**：
本接口用于重定向模式下，配置IMSI黑名单，单次配置最多20，累次配置总数达到1000，删除时间节点配置靠前的IMSI。
`ClearImsiListFlag`取1并且`AddImsiNum`非零同时配置，先清空列表再添加。

**消息结构：`O_FL_LMT_TO_ENB_UE_REDIRECT_IMSI_LIST_CFG`**

| 参数名称          | 数据类型 | 取值范围                   | 含义                                     |
| :---------------- | :------- | :------------------------- | :--------------------------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                        | 消息头定义(0xF08E)                       |
| `ClearImsiListFlag` | `U8`     | `0,1`                      | 0:保留当前所有配置  1:清空所有IMSI配置列表 |
| `AddImsiNum`      | `U8`     | `0.. C_MAX_UE_REDIRECT_IMSI_ADD_NUM` | 增加的IMSI条数                           |
| `ImsiStr[C_MAX_UE_REDIRECT_IMSI_ADD_NUM][C_MAX_IMSI_LEN]` | `U8`     | `/`                        | 字符串数组                               |
| `Res[2]`          | `U8`     | `/`                        | 预留                                     |

---
### FDD GPS同步不成功重新同步配置(0xF090)

**是否立即生效**：是

**重启是否保留配置**：否

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_FDD_GPS_RESYNC_CFG_ACK (0xF091)`

**功能描述**：
该接口用于FDD 模式下（FDD的GPS同步功能只用于校准频偏，小区激活态下有效，同小区激活顺序无关）：
当通过`O_FL_LMT_TO_ENB_REM_MODE_CFG（0xF023）`开启FDD gps同步功能时，
此消息接口用于当gps同步不成功时，重新配置gps去做同步，不需要重新建立小区。

**消息结构：`O_FL_LMT_TO_ENB_FDD_GPS_RESYNC_CFG`**

| 参数名称          | 数据类型 | 取值范围 | 含义         |
| :---------------- | :------- | :------- | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头定义(0xF090) |

---
### 上行功控Alpha系数配置(0xF092)

**是否立即生效**：否

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_UL_POWER_CONTROL_ALPHA_CFG_ACK(0xF093)`

**功能描述**：
此消息接口用于当FDD开启配置TDD/FDD小区的SIB2中的上行功控系数，小区去激活状态下配置立即生效。
本接口配置TDD出厂配置默认值为70，FDD默认出厂值为80。在空旷地带测试抓号，建议该值配置成80。
注：配置的（0，40，50，60，70，80，90，100）分别对应sib2信息alpha值的（0，1，2，3，4，5，6，7）。

**消息结构：`O_FL_LMT_TO_ENB_UL_POWER_CONTROL_ALPHA_CFG`**

| 参数名称          | 数据类型 | 取值范围                 | 含义         |
| :---------------- | :------- | :----------------------- | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                      | 消息头定义(0xF092) |
| `UlPowerAlpha`    | `U8`     | `（0，40，50，60，70，80，90，100）` | Sib2中上行功控系数 |
| `Res[3]`          | `U8`     | `/`                      | 预留         |

---
### GPS芯片选择gps或北斗配置(0xF097)

**是否立即生效**：否

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_GPS_OR_BEIDOU_CFG_ACK (0xF098)`

**功能描述**：
此消息接口用于配置gps芯片选择gps或者北斗，配置完重启生效。

**消息结构：`O_FL_LMT_TO_ENB_GPS_OR_BEIDOU_CFG`**

| 参数名称          | 数据类型 | 取值范围   | 含义     |
| :---------------- | :------- | :--------- | :------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`        | 消息头(0xF097) |
| `u8FlagInUse`     | `U8`     | `0：GPS  1：北斗` | /        |
| `Res[3]`          | `U8`     | `/`        | 保留字节 |

---
### 单板管脚电平输出控制(0xF09F)

**是否立即生效**：是

**重启是否保留配置**：否

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_PINX_SWITCH_CFG_ACK (0xF0A0)`

**功能描述**：
此接口用于控制V3单板PIN1管脚的电平输出，V5板IO6管脚输出控制，以及V6板IO5管脚输出控制。
单板自主判断当前板卡类型，若是V3则该接口控制PIN1输出，若是V5板卡，控制IO6电平输出，若是V6板卡，控制IO5电平输出。
V3对应板卡上扩展IO1里的IO1（J7-IO1）；V5对应板卡上IO6；V6对应板卡上IO5。

**消息结构：`O_FL_LMT_TO_ENB_PINX_SWITCH_CFG`**

| 参数名称          | 数据类型 | 取值范围    | 含义         |
| :---------------- | :------- | :---------- | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`         | 消息头(0xF09F) |
| `PinxSwitch`      | `U8`     | `0,1`       | 0: 输出低电平 1：输出高电平 |
| `Reserved[3]`     | `U8`     | `/`         | 预留         |

---
### Band功率衰减关系表配置(0xF0A7)

**是否立即生效**：是

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_MULTI_BAND_POWERDEREASE_ACK (0xF0A8)`

**功能描述**：
该接口用于配置不同band的小区时，基站根据band，查询关系表，配置衰减值。
不支持增量配置。若建小区的band无法在表中查找到对应衰减值，则使用默认衰减值（由`O_FL_ENB_TO_LMT_SYS_PWR1_DEREASE(0xF015)`配置）。
出厂默认关闭该功能。

**消息结构：`O_FL_LMT_TO_ENB_MULTI_BAND_POWERDEREASE_CFG`**

| 参数名称          | 数据类型 | 取值范围 | 含义                             |
| :---------------- | :------- | :------- | :------------------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF0A7)                   |
| `NumElem`         | `U8`     | `0..32`  | 0: 关闭功能  >0:配置相关关系及关系个数 |
| `Reserved[3]`     | `U8`     | `/`      | 预留                             |
| `BandPwrdereaseMap[32]` | `bandPwrdereaseMap` | `/`      | Band和衰减值对应关系             |

`bandPwrdereaseMap`

| 参数名称     | 数据类型 | 取值范围 | 含义   |
| :----------- | :------- | :------- | :----- |
| `band`       | `U8`     | `1..255` | 频带值 |
| `Pwrderease` | `U8`     | `0..255` | 衰减值 |
| `Reserved[2]` | `U8`     | `/`      | 预留   |

---
### Band接收增益关系表配置(0xF0C8)

**是否立即生效**：是

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_MULTI_BAND_RXGAIN_CFG_ACK (0xF0C9)`

**功能描述**：
该接口用于配置不同band的小区时，基站根据band，查询关系表，配置接收增益值。
不支持增量配置。若建小区的band无法在表中查找到对应接收增益值，默认输出接收增益值0xFF。
出厂默认关闭该功能。

**消息结构：`O_FL_LMT_TO_ENB_MULTI_BAND_RXGAIN_CFG`**

| 参数名称          | 数据类型 | 取值范围 | 含义                             |
| :---------------- | :------- | :------- | :------------------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF0C8)                   |
| `NumElem`         | `U8`     | `0..32`  | 0: 关闭功能  >0:配置相关关系及关系个数 |
| `Reserved[3]`     | `U8`     | `/`      | 预留                             |
| `BandRxgainMap[32]` | `bandRxgainMap` | `/`      | Band和增益值对应关系             |

`bandRxgainMap`

| 参数名称     | 数据类型 | 取值范围 | 含义     |
| :----------- | :------- | :------- | :------- |
| `band`       | `U8`     | `1..127` | 频带值   |
| `RxGain`     | `U8`     | `0..127` | 接收增益值 |
| `Reserved[2]` | `U8`     | `/`      | 预留     |

---
### MSG4功率抬升配置(0xF0AF)

**是否立即生效**：否

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMB_MSG4_POWER_BOOST_CFG_ACK (0xF0B0)`

**功能描述**：
此接口用于配置MSG4功率是否抬升，TDD生效。默认出厂设置为不抬升，在不同环境下测试时，如出现接入成功率不理想，可以配置抬升MSG4功率。

**消息结构：`O_FL_LMT_TO_ENB_MSG4_POWER_BOOST_CFG`**

| 参数名称          | 数据类型 | 取值范围          | 含义                 |
| :---------------- | :------- | :---------------- | :------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`               | 消息头(0xF0AF)       |
| `Msg4PowerBoost`  | `U32`    | `0，1`            | 0：不抬升（出厂默认值）  1：抬升 |

---
### GPS固件彻底复位配置(0xF0D2)

**是否立即生效**：否

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_LMT_TO_ENB_GPS_SOFTWARE_RENOVATE_CFG_ACK(0xF0D3)`

**功能描述**：
此接口主要用于当gps信号很好的场景下反复无法同步成功，搜不到星的场景，可以尝试将GPS固件彻底复位，复位时间比较长，等收到响应后，重启生效。

**消息结构：`O_FL_LMT_TO_ENB_GPS_SOFTWARE_RENOVATE_CFG`**

| 参数名称          | 数据类型 | 取值范围 | 含义         |
| :---------------- | :------- | :------- | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF0D2) |

---
### 设置基站测量UE配置(0xF006)

**是否立即生效**：是

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_MEAS_UE_ACK(0xF007)`

**功能描述**：
此接口用于配置UE的测量模式，版本默认发布`u8WorkMode`为0（持续侦码模式）,各个模式的功能说明以及相关配置流程，详见第3章流程说明《[基站测量UE](#_基站测量UE)》。

**消息结构：`O_FL_LMT_TO_ENB_MEAS_UE_CFG`**

| 参数名称          | 数据类型 | 取值范围 | 含义 |
| :---------------- | :------- | :------- | :--- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF006) |
| `stUeMeasCfg`     | `wrUeMeasCfg` | `/`      | /    |

`wrUeMeasCfg`

| 参数名称          | 数据类型 | 取值范围                                                 | 含义                                                         |
| :---------------- | :------- | :------------------------------------------------------- | :----------------------------------------------------------- |
| `u8WorkMode`      | `U8`     | `取值范围:0,1,3,4,6,7,8  默认0`                            | 0: 持续侦码模式  1: 周期侦码模式  3: 管控模式  4: 重定向模式  6: 老化模式  7: 手动载波轮循模式  8: 自动载波轮循模式 |
| `u8RedirectSubMode` | `U8`     | `[0…5]  u8WorkMode仅当取4（重定向模式）有效。`           | 0: 名单中的用户执行重定向；名单外的全部踢回公网  1: 名单中的用户踢回公网；名单外的全部重定向  2: 名单中的用户执行重定向；名单外的全部吸附在本站  3: 名单中的用户吸附在本站;名单外的全部重定向  4: 所有目标重定向  5： 名单中的踢回公网，名单外的重定向，基站自动更新名单。1分钟周期对半清除名单 |
| `u16CapturePeriod` | `U16`    | `1~65535`                                                | Capture reperiod，只在周期侦码模式、手动载波轮循模式和自动载波轮循模式下使用 |
| `u8ControlSubMode` | `U8`     | `只在管控模式下有效  0：黑名单子模式；1：白名单子模式  2: 全黑子模式` | 配置管控模式的子模式                                         |
| `Res[3]`          | `U8`     | `/`                                                      | 保留字节                                                     |
| `imsiRedirectTime` | `U16`    | `1~180`                                                  | 重定向限制时间,单位:秒                                       |
| `imsiAgingTime`   | `U16`    | `1~300`                                                  | IMSI老化时间,单位:秒,按照老化模式设计,该值必须大于重定向限制时间 |
| `rollCarrierLst[64]` | `U8`     | 字符串，以'\\0'结束                                       | 手动轮循载波列表或自动轮循扫频频点列表。  仅手动载波轮循模式和自动载波轮循下使用。  手动载波轮循模式下用于配置轮循载波列表，格式："载波号,载波持续时间,载波号,载波持续时间,...载波号,载波持续时间\\0"，最多支持8个载波。  自动载波轮循模式下用于配置轮循扫频频点列表，格式："扫频频点,扫频频点,...扫频频点\\0"，最多支持10个扫频频点。 |
| `autoRollCarrierCfg` | `wrAutoRollCarrierCfg` | `/`                                                      | 自动载波轮循配置参数，仅自动载波轮循模式下使用               |

`wrAutoRollCarrierCfg`

| 参数名称          | 数据类型 | 取值范围          | 含义     |
| :---------------- | :------- | :---------------- | :------- |
| `rollCarrierPeriod` | `U32`    | `1~86400`         | 轮循周期，单位：秒 |
| `s8QrxlevMin`     | `S16`    | `(-70 ~ -22)`     | 最小接入电平 |
| `rsrpThreshold`   | `S16`    | `(-140 ~ -44)`    | RSRP阈值 |
| `u8MaxScanNum`    | `U8`     | `取值范围:1,2,3,4,5,6,7,8  默认3` | 最大轮询频点数 |
| `Res[3]`          | `U8`     | `/`               | 预留     |

---
### 设置同步失败后激活小区方式(0xF0CC)

**是否立即生效**：是

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_BUILD_CELL_CFG_ACK (0xF0CD)`

**功能描述**：
（无详细功能描述）

**消息结构：`O_FL_LMT_TO_ENB_BUILD_CELL_CFG`**

| 参数名称          | 数据类型 | 取值范围                 | 含义                             |
| :---------------- | :------- | :----------------------- | :------------------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                      | 消息头(0xF0CC)                   |
| `buildFlag`       | `U32`    | `0,1`                    | 0: 同步失败不建小区；  1：同步失败强制激活小区。（GPS同步失败1次，空口同步失败3次以上） |

---
### 设置GPS失步后重启时间(0xF0CE)

**是否立即生效**：是

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_GPS_LOSS_REBOOT_TMR_CFG_ACK (0xF0CF)`

**功能描述**：
（无详细功能描述）

**消息结构：`O_FL_LMT_TO_ENB_GPS_LOSS_REBOOT_TMR_CFG`**

| 参数名称          | 数据类型 | 取值范围  | 含义     |
| :---------------- | :------- | :-------- | :------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`       | 消息头(0xF0CE) |
| `value`           | `U32`    | `0-65535` | 单位：分钟 |

---
### GPS有源/无源设置(0xF0C6)

**是否立即生效**：是

**重启是否保留配置**：否

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_GPS_SRC_SEL_ACK (0xF0C7)`

**功能描述**：
（无详细功能描述）

**消息结构：`O_FL_LMT_TO_ENB_GPS_SRC_SEL`**

| 参数名称          | 数据类型 | 取值范围 | 含义       |
| :---------------- | :------- | :------- | :--------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF0C6) |
| `gpsSel`          | `U8`     | `0-1`    | 0:无源  1:有源  默认有源 |
| `Res[3]`          | `U8`     | `/`      | /          |

---
### FTP 上传下载配置(0xF0D0)

**是否立即生效**：是

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_FTP_GET_PUT_CFG_ACK (0xF0D1)`

**功能描述**：
使用该接口，可以通过FTP下发管控黑白名单，黑名单文件命名格式`black.txt`,白名单文件名格式`white.txt`,文件中每个imis一行，文件中不要有空行，最多支持100000个imsi。
`isCfgFtp`:
0:FTP相关配置仅本次下发有效，重启后不生效
1:FTP相关配置保存在本地，下次不需要再次配置
`actionType`：
1:通过FTP配置管控黑白名单
2:通过FTP上传当前黑白名单配置文件

**消息结构：`O_FL_LMT_TO_ENB_FTP_GET_PUT_CFG`**

| 参数名称          | 数据类型 | 取值范围 | 含义       |
| :---------------- | :------- | :------- | :--------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF0D0) |
| `ftpCfg`          | `ftpGetPutCfg` | `/`      | 上传下载相关配置 |

`ftpGetPutCfg`：

| 参数名称          | 数据类型 | 取值范围   | 含义                                                         |
| :---------------- | :------- | :--------- | :----------------------------------------------------------- |
| `isCfgFtp`        | `U8`     | `{0,1}`    | 1:配置 0不配置                                               |
| `ftpServerIp[16]` | `U8`     | 字符串     | 服务器IP，"************"                                     |
| `Res[3]`          | `U8`     | `/`        | 预留                                                         |
| `ftpServerPort`   | `U32`    | 无符号整形 | 服务器端口                                                   |
| `ftpLoginName[20]` | `U8`     | 字符串     | 用户名"kkk"                                                  |
| `ftpPassword[10]` | `U8`     | 字符串     | 密码"123456"                                                 |
| `ftpServerFilePath[66]` | `U8`     | 字符串     | 上传文件放置目录，字符串，以'\\0'结尾。  例如：  欲放置文件于FTP服务器根目录下的filePath文件夹，完整的路径为："/filePath/" |
| `actionType`      | `U8`     | 无符号整形类型 | 1：文件下载到单板  2：文件以压缩包上传服务器                 |
| `FileNum`         | `U8`     | 无符号整形类型 | `actionType=1`有效                                           |
| `Filename[82]`    | `U8`     | 字符串     | `actionType=1`且`FileNum>0`有效，文件名，如"a.txt;b.txt;c.txt"，分号为英文半角 |

**响应：`O_FL_ENB_TO_LMT_FTP_GET_PUT_CFG_ACK (0xF0D1)`**

| 参数名称      | 数据类型 | 取值范围   | 含义                                       |
| :------------ | :------- | :--------- | :----------------------------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`        | 消息头(0xF0D1)                             |
| `resultNum`   | `U32`    | `{0…8}`    | 操作错误码：  0：下载操作参数错误  1：下载操作开始传输  2：下载操作传输成功  3：下载操作传输失败  4：上传操作参数错误  5：上传操作开始传输  6：上传操作传输成功  7：上传操作传输失败  8：磁盘空间不足 |

---
### 异频频点列表配置(0xF0E2)

**是否立即生效**：否

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_EUTRA_FREQ_CFG_ACK (0xF0E3)`

**功能描述**：
此接口用于添加小区SIB5中异频信息，填写下行频点号。

**消息结构：`O_FL_LMT_TO_ENB_EUTRA_FREQ_CFG`**

| 参数名称          | 数据类型 | 取值范围 | 含义         |
| :---------------- | :------- | :------- | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF0E2) |
| `EutraArfcnNumber` | `U32`    | `1~4`    | 配置下行频点数量 |
| `EutraArfcn[C_MAX_EUTRA_ARFCN]` | `U32`    | 下行频点号 | /            |

---
### 频偏校准开关配置(0xF0DA)

**是否立即生效**：是

**重启是否保留配置**：是

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_FREQ_OFFSET_ADJ_CFG_ACK(0xF0DB)`

**功能描述**：
基站收到此条消息，设置校准开关打开，重启基站，基站重启以后会开始频偏校准过程。
频偏校准过程相关流程如下：
1. 使用`O_FL_LMT_TO_ENB_SELFCFG_ARFCN_CFG_REQ（0xF051）`预先配置基站扫频的基础频点（此配置重启和上下电都会保留配置）。`O_FL_LMT_TO_ENB_SELFCFG_ARFCN_QUERY (0xF04D)`接口用于查询配置频点列表。
2. 然后客户端下发频偏校准开关（0xF0DA）,打开频偏校准功能；
3. 基站收到0xF0DA消息会上报状态："频偏校准开始"（0xF019），然后重启基站。
4. 基站重启以后则开始频偏校准流程，上报状态："频偏校准进行中"（0xF019）。校准过程中会有扫频状态和扫频结果上报。
5. 频偏校准结束，上报状态："频偏校准结束"（0xF019），同时上报频偏校准结果（0xF0DC），复位校准开关。
6. 校准结束以后，需要客户端下发重启基站的指令，基站重启以后才能进行正常工作的状态。

**消息结构：`O_FL_LMT_TO_ENB_FRQ_OFFSET_ADJ_CFG`**

| 参数名称          | 数据类型 | 取值范围 | 含义     |
| :---------------- | :------- | :------- | :------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF0DA) |
| `FreqOffsetSwitch` | `U8`     | `0,1`    | 0:关闭 1：打开 |
| `Res[3]`          | `U8`     | `/`      | 保留字节 |

---
### AGC配置(0xF079)

**是否立即生效**：是

**重启是否保留配置**：是

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_AGC_SET_ACK (0xF07A)`

**功能描述**：
FDD有效。

**消息结构：`O_FL_LMT_TO_ENB_AGC_SET`**

| 参数名称          | 数据类型 | 取值范围    | 含义      |
| :---------------- | :------- | :---------- | :-------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`         | 消息头(0xF079) |
| `AgcFlag`         | `U32`    | `0：不开启；  1：开启` | 是否开启AGC |

---
### 轮循载波信息配置(0xF0F0)

**是否立即生效**：是

**重启是否保留配置**：是

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_ROLL_CARRIER_CFG_ACK(0xF0F1)`

**功能描述**：
（无详细功能描述）

**消息结构：`O_FL_LMT_TO_ENB_ROLL_CARRIER_CFG`**

| 参数名称          | 数据类型 | 取值范围 | 含义       |
| :---------------- | :------- | :------- | :--------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF0F0) |
| `rollCarrierCfgNum` | `U8`     | `0-8`    | 需要配置的轮循载波数，0表示无载波配置 |
| `Res[3]`          | `U8`     | `/`      | 预留       |
| `stRollCarrierCfg[8]` | `wrRollCarrierCfg` | `/`      | 手动轮循配置参数 |

`wrRollCarrierCfg`

| 参数名称          | 数据类型 | 取值范围                              | 含义                                                         |
| :---------------- | :------- | :------------------------------------ | :----------------------------------------------------------- |
| `u32BoardNum`     | `U32`    | `/`                                   | 基带板编号，客户自定义                                       |
| `u8CarrierNum`    | `U32`    | `0~7`                                 | 载波号                                                       |
| `ulEarfcn`        | `U32`    | TDD: 255  FDD:实际频点              | 上行频点                                                     |
| `dlEarfcn`        | `U32`    | `0~65535`                             | 下行频点                                                     |
| `PLMN[7 ]`        | `U8`     | 字符数组，以结束符结束eg:“46000”     | PLMN                                                         |
| `Band`            | `U8`     | 需要跟上面的频点参数匹配              | 频段                                                         |
| `CellId`          | `U32`    | `0~65535`                             | 小区Id                                                       |
| `Pwr1Derease`     | `U32`    | `0~20`                                | 配置0则为F015配置与F0A7生效值，配置1则在上面两条消息的基础上-1Db,2则为-2Db,19则为-19Db,20则输出功率为最小255 |
| `s8QrxlevMin`     | `S8`     | `(- 70 ~ - 22)`                       | 最小接入电平                                                 |
| `Res[3]`          | `U8`     | `/`                                   | 预留                                                         |

---
### 发射功率衰减偏移配置(0xF0F4)

**是否立即生效**：是

**重启是否保留配置**：是

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_PWR1_DEREASE_CFG_ACK(0xF0F5)`

**功能描述**：
（无详细功能描述）

**消息结构：`O_FL_LMT_TO_ENB_PWR1_DEREASE_CFG`**

| 参数名称          | 数据类型 | 取值范围 | 含义                                                         |
| :---------------- | :------- | :------- | :----------------------------------------------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF0F4)                                               |
| `pwr1Derease`     | `U8`     | `0~20`   | 配置0则为F015配置与F0A7生效值，配置1则在上面两条消息的基础上-1Db,2则为-2Db,19则为-19Db,20则输出功率为最小255 |
| `Res[3]`          | `U8`     | `/`      | 预留                                                         |

---
### Fdd共建站重定向信息配置(0xF0E6)

**是否立即生效**：是

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_FDD_REDIRECTION_CFG_ACK(0xF0E7)`

**功能描述**：
此接口用于联通和电信共建站时，用来配置联通和电信终端重定向频点。参数上下电后参数保存，该接口不能和`O_FL_LMT_TO_ENB_REDIRECT_INFO_CFG`接口同时使用。

**消息结构：`O_FL_LMT_TO_ENB_FDD_REDIRECTION_CFG`**

| 参数名称          | 数据类型 | 取值范围    | 含义           |
| :---------------- | :------- | :---------- | :------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`         | 消息头(0xF0E6) |
| `OnOff`           | `U32`    | `0：打开  1：关闭` | 重定向开关     |
| `UnicomEarfcn`    | `U32`    | `0~65535`   | 4G联通重定向频点 |
| `TelecomEarfcn`   | `U32`    | `0~65535`   | 4G电信重定向频点 |

---
### 采集用户信息上报(0xF005)

**功能描述**：
小区激活以后，基站采集接入用户信息并立即上报给客户端，只有开启IMEI捕获功能的时，才会上报IMEI。

**消息结构：`O_FL_ENB_TO_LMT_UE_INFO_RPT` (主控板用户接口 - 围栏版本)**

| 参数名称          | 数据类型 | 取值范围   | 含义         |
| :---------------- | :------- | :--------- | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`        | 消息头(0xF005) |
| `UeIdType`        | `U32`    | `0:IMSI  1:IMEI  2:BOTH` | UE ID类型    |
| `IMSI[C_MAX_IMSI_LEN ]` | `U8`     | 字符串格式 | IMSI         |
| `IMEI[C_MAX_IMEI_LEN ]` | `U8`     | 字符串格式 | IMEI         |
| `RSSI`            | `U8`     | `/`        | 采集用户的RSSI |
| `STMSIPresent`    | `U8`     | `/`        | S-TMSI是否有效 |
| `S-TMSI[5]`       | `U8`     | 十进制     | 采集用户的S-TMSI |
| `ConnectedUeNum`  | `U8`     | `0-255`    | 当前在线的用户数 |
| `TimingAdv`       | `U16`    | `0-1282`   | 时间提前量   |
| `UeAccessType`    | `U8`     | `0: TAU  1: ATTACH` | UE接入类型   |
| `Res2[3]`         | `U8`     | `/`        | 保留字节     |

**消息结构：`O_FL_ENB_TO_LMT_UE_INFO_RPT` (非主控板用户接口——围栏版本)**

| 参数名称          | 数据类型 | 取值范围   | 含义         |
| :---------------- | :------- | :--------- | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`        | 消息头(0xF005) |
| `UeIdType`        | `U32`    | `0:IMSI  1:IMEI  2:BOTH` | UE ID类型    |
| `IMSI[C_MAX_IMSI_LEN ]` | `U8`     | 字符串格式 | IMSI         |
| `IMEI[C_MAX_IMEI_LEN ]` | `U8`     | 字符串格式 | IMEI         |
| `RSSI`            | `U8`     | `/`        | 采集用户的RSSI |
| `STMSIPresent`    | `U8`     | `/`        | S-TMSI是否有效 |
| `S-TMSI[5]`       | `U8`     | 十进制     | 采集用户的S-TMSI |
| `ConnectedUeNum`  | `U8`     | `0-255`    | 当前在线的用户数 |
| `TimingAdv`       | `U16`    | `0-1282`   | 时间提前量   |
| `UeAccessType`    | `U8`     | `0: TAU  1: ATTACH` | UE接入类型   |
| `Res2[3]`         | `U8`     | `/`        | 保留字节     |
| `year`            | `U16`    | 十进制     | 年           |
| `month`           | `U8`     | 十进制     | 月           |
| `day`             | `U8`     | 十进制     | 日           |
| `hour`            | `U8`     | 十进制     | 时           |
| `min`             | `U8`     | 十进制     | 分           |
| `sec`             | `U8`     | 十进制     | 秒           |
| `Res3`            | `U8`     | `/`        | 保留字节     |
| `seqNum`          | `U32`    | 十进制     | 序列号（针对该条消息的编号） |

**消息结构：`O_FL_LMT_TO_ENB_UE_INFO_RPT_ACK` (非主控板用户ACK接口)**

| 参数名称          | 数据类型 | 取值范围 | 含义         |
| :---------------- | :------- | :------- | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF096) |
| `seqNum`          | `U32`    | 十进制   | 序列号：等于`O_FL_ENB_TO_LMT_UE_INFO_RPT`消息中`seqNum`字段 |

---
### 扫频/同步小区信息上报(0xF00A)

**功能描述**：
TDD同步过程中，通过此消息上报尝试同步的小区信息(`collectionTypeFlag=1`)。
扫频完成后，通过此消息上报扫频的结果信息(`collectionTypeFlag=0`)。
由于此消息体长度比较大，对于数组的信息，基站会按照实际Num填充上报信息，请客户端解析时，根据相应数组的Num解析数组信息。

**常量：**

| 名称                     | 参数值 | 说明           |
| :----------------------- | :----- | :------------- |
| `C_MAX_REM_ARFCN_NUM`    | `10`   | 最大的扫频频点数目 |
| `C_MAX_INTRA_NEIGH_NUM`  | `32`   | 小区的同频邻区数目，来自SIB4 |
| `C_MAX_COLLTECTION_INTRA_CELL_NUM` | `8`    | 扫频上报最大的小区数目 |
| `MAX_INTER_FREQ_LST`     | `/`    | 小区的异频点个数，来自SIB5 |
| `MAX_INTER_FREQ_NGH`     | `/`    | 小区的每个异频点对应的小区信息，来自SIB5 |

**消息结构：`O_FL_ENB_TO_LMT_REM_INFO_RPT`**

| 参数名称          | 数据类型 | 取值范围 | 含义       |
| :---------------- | :------- | :------- | :--------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头0xF00A |
| `collectionCellNum` | `U16`    | `1~8`    | 采集的小区数目 |
| `collectionTypeFlag` | `U16`    | `0：扫频小区  1：同步小区` | 扫频信息标识/同步信息标识 |
| `stCollCellInfo  [C_MAX_COLLTECTION_INTRA_CELL_NUM]` | `wrFLCollectionCellInfo` | `/`      | 小区信息   |

`wrFLCollectionCellInfo`：

| 参数名称          | 数据类型 | 取值范围 | 含义         |
| :---------------- | :------- | :------- | :----------- |
| `stCellInfo`      | `wrFLCellInfo` | `/`      | 小区的基本信息 |
| `IntraFreqNeighCellNum` | `U32`    | `0~ C_MAX_INTRA_NEIGH_NUM` | 小区的同频邻区个数 |
| `stIntraFreqNeighCellInfo[C_MAX_INTRA_NEIGH_NUM]` | `wrIntraFreqNeighCellInfo` | `/`      | 小区的同频邻区信息 |
| `InterFreqNum`    | `U32`    | `0~ MAX_INTER_FREQ_LST` | 小区的异频个数 |
| `stInterFreqLstInfo[MAX_INTER_FREQ_LST]` | `stFlLteIntreFreqLst` | `/`      | 小区的异频信息 |

`wrFLCellInfo`：

| 参数名称          | 数据类型 | 取值范围                                                 | 含义                                                         |
| :---------------- | :------- | :------------------------------------------------------- | :----------------------------------------------------------- |
| `dlEarfcn`        | `U32`    | `0~65535`                                                | 下行频点                                                     |
| `PCI`             | `U16`    | `0~ 503`                                                 | *PhysCellId* is used to indicate the physical layer identity of the cell |
| `TAC`             | `U16`    | `0~65535`                                                | TrackingAreaCode is used to identify a tracking area within the scope of a PLMN |
| `PLMN`            | `U16`    | `0~255`                                                  | 仅支持5位PLMN                                                |
| `TddSfAssignment` | `U16`    | `/`                                                      | TDD子帧配置值                                                |
| `CellId`          | `U32`    | `0x00000000  ~0x0FFFFFFF`                                | *CellIdentity* is used to unambiguously identify a cell within a PLMN |
| `Priority`        | `U32`    | `1~7`                                                    | 本小区频点优先级                                             |
| `RSRP`            | `U8`     | `0~97`                                                   | 下行参考信号强度                                             |
| `RSRQ`            | `U8`     | `0~33`                                                   | LTE参考信号接收质量                                          |
| `Bandwidth`       | `U8`     | `6, 15, 25, 50, 75, 100`                                 | 小区工作带宽                                                 |
| `TddSpecialSfPatterns` | `U8`     | `/`                                                      | TDD特殊子帧配置                                              |

`wrIntraFreqNeighCellInfo`：

| 参数名称          | 数据类型 | 取值范围                                                 | 含义                                                         |
| :---------------- | :------- | :------------------------------------------------------- | :----------------------------------------------------------- |
| `dlEarfcn`        | `U32`    | `0~65535`                                                | 下行频点                                                     |
| `PCI`             | `U16`    | `0~ 503`                                                 | *PhysCellId* is used to indicate the physical layer identity of the cell |
| `QoffsetCell`     | `U16`    | `0~31`                                                   | indicate a cell specific offset.取值依次对应dB如下所示：  ENUMERATED  {dB-24=0, dB-22, dB-20, dB-18, dB-16, dB-14,  dB-12, dB-10, dB-8, dB-6, dB-5, dB-4, dB-3, dB-2, dB-1, dB0, dB1, dB2, dB3, dB4, dB5,dB6, dB8, dB10, dB12, dB14, dB16, dB18, dB20, dB22, dB24} |

`stFlLteIntreFreqLst`：

| 参数名称          | 数据类型 | 取值范围                                                 | 含义                                                         |
| :---------------- | :------- | :------------------------------------------------------- | :----------------------------------------------------------- |
| `dlEarfcn`        | `U32`    | `0~65535`                                                | 下行频点                                                     |
| `cellReselectPriotry` | `U8`     | `0~7`                                                    | 值越小优先级越低，如果是255，则优先级低于任何优先级，比0还低。 |
| `Q_offsetFreq`    | `U8`     | `0~31`                                                   | indicate a freq specific offset.取值依次对应dB如下所示：  ENUMERATED  {dB-24=0, dB-22, dB-20, dB-18, dB-16, dB-14,  dB-12, dB-10, dB-8, dB-6, dB-5, dB-4, dB-3, dB-2, dB-1, dB0, dB1, dB2, dB3, dB4, dB5,dB6, dB8, dB10, dB12, dB14, dB16, dB18, dB20, dB22, dB24} |
| `measBandWidth`   | `U16`    | `0~5`                                                    | indicate the maximum allowed measurement bandwidth on a carrier frequency;取值依次带宽如下所示  ENUMERATED {mbw6=0, mbw15=1, mbw25=2, mbw50=3, mbw75=4, mbw100=5} |
| `interFreqNghNum` | `U32`    | `0~ MAX_INTER_FREQ_NGH`                                  | 邻区数目                                                     |
| `stInterFreqNeighCell[MAX_INTER_FREQ_NGH]` | `wrFLInterNeighCellInfo` | `/`                                                      | 邻区信息                                                     |

`wrFLInterNeighCellInfo`：

| 参数名称          | 数据类型 | 取值范围                                                 | 含义                                                         |
| :---------------- | :------- | :------------------------------------------------------- | :----------------------------------------------------------- |
| `PCI`             | `U16`    | `0~ 503`                                                 | *PhysCellId* is used to indicate the physical layer identity of the cell |
| `QoffsetCell`     | `U16`    | `0~31`                                                   | indicate a cell specific offset.取值依次对应dB如下所示：  ENUMERATED  {dB-24=0, dB-22, dB-20, dB-18, dB-16, dB-14,  dB-12, dB-10, dB-8, dB-6, dB-5, dB-4, dB-3, dB-2, dB-1, dB0, dB1, dB2, dB3, dB4, dB5,dB6, dB8, dB10, dB12, dB14, dB16, dB18, dB20, dB22, dB24} |

---
### 基站执行状态实时上报(0xF019)

**功能描述**：
此消息用于基站实时上报操作流程的执行结果，具体信息参见上报值。

**消息结构：`O_FL_ENB_TO_LMT_ENB_STATE_IND`**

| 参数名称          | 数据类型 | 取值范围   | 含义      |
| :---------------- | :------- | :--------- | :-------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`        | 消息头(0xF019) |
| `CellStateInd`    | `U32`    | `WR_FL_ENB_STATE` | `wrFLEnbToLmtEnbStateInd` |

`WR_FL_ENB_STATE`：

| 值     | 含义       |
| :----- | :--------- |
| `0`    | 空口同步成功 |
| `1`    | 空口同步失败 |
| `2`    | GPS同步成功 |
| `3`    | GPS同步失败 |
| `4`    | 扫频成功   |
| `5`    | 扫频失败   |
| `6`    | 小区激活成功 |
| `7`    | 小区激活失败 |
| `8`    | 小区去激活 |
| `9`    | 空口同步中 |
| `10`   | GPS同步中 |
| `11`   | 扫频中     |
| `12`   | 小区激活中 |
| `13`   | 小区去激活中 |
| `14`   | 频偏校准开始 |
| `15`   | 频偏校准进行中 |
| `16`   | 频偏校准结束 |
| `0xFFFF` | 无效状态   |

---
### 告警指示上报(0xF05B)

**功能描述**：
此消息用于基站上报一些运行异常的告警信息，目前支持的告警包括失步、高低温（基带板温度）告警。

**消息结构：`O_FL_ENB_TO_LMT_ALARMING_TYPE_IND`**

| 参数名称     | 数据类型 | 取值范围    | 含义                 |
| :----------- | :------- | :---------- | :------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`         | 消息头(0xF05B)       |
| `AlarmingType` | `U32`    | `0,1,5`     | 0：基带板高温告警`>=70`度  1：失步告警  5：基带板低温告警`<= -20` |
| `AlarmingFlag` | `U32`    | `0,1`       | 0：产生告警指示  1：取消告警指示 |

---
### 频偏校准结果上报(0xF0DC)

**功能描述**：
频偏校准结果上报。

**消息结构：`O_FL_ENB_TO_LMT_FRQ_OFFSET_ADJ_RESULT_IND`**

| 参数名称          | 数据类型 | 取值范围 | 含义                                                         |
| :---------------- | :------- | :------- | :----------------------------------------------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF0DC)                                               |
| `FreqOffsetAdjResult` | `U8`     | `0,1`    | 0:成功 1：失败                                               |
| `Res[3]`          | `U8`     | `/`      | 保留字节                                                     |
| `FreqOffsetValue` | `S32`    | `-200~200Hz` | 仅`FreqOffsetAdjResult`为0时有效，代表当前单板的频偏值，频偏校准精度为绝对值200以内。 |

---
### 自配置结果上报(0xF064)

**功能描述**：
自配置结果上报，TDD有效。

**消息结构：`O_FL_ENB_TO_LMT_SELFCFG_PARA_RPT`**

| 参数名称          | 数据类型 | 取值范围                 | 含义         |
| :---------------- | :------- | :----------------------- | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                      | 消息头(0xF064) |
| `dlAfrcn`         | `U32`    | `0-65535`                | 下行频点     |
| `pci`             | `U16`    | `0-503`                  | PCI          |
| `Tac`             | `U16`    | `0-65535`                | TAC          |
| `sfassign`        | `U16`    | `/`                      | TDD子帧配置值 |
| `specsfassign`    | `U16`    | `/`                      | TDD特殊子帧配置值 |
| `cellid`          | `U32`    | `0x00000000-0x0FFFFFFF`  | Cell ID      |
| `Bandwidth`       | `U8`     | `6,15,25,50,75,100`      | 带宽         |
| `sysBand`         | `U8`     | `1-255`                  | 频段         |
| `Plmn[6]`         | `U8`     | 字符数组，以结束符结束eg:"46000" | PLMN         |

---
### 基站基本信息查询(0xF02B)

**功能描述**：
此消息用于客户端查询一些基站的基本信息，比如版本号，MAC地址，SN等。

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_BASE_INFO_QUERY(0xF02B)`

**应答消息（eNB -> LMT）**：`O_FL_ENB_TO_LMT_BASE_INFO_QUERY_ACK(0xF02C)`

**消息结构：`O_FL_LMT_TO_ENB_BASE_INFO_QUERY`**

| 参数名称          | 数据类型 | 取值范围                          | 含义     |
| :---------------- | :------- | :-------------------------------- | :------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                               | 消息头(0xF02B) |
| `u32EnbBaseInfoType` | `U32`    | 0: 设备型号  1：硬件版本  2：软件版本  3：SN号  4：MAC地址  5：uboot版本号  6：板卡温度 | 查询信息的类型 |

**消息结构：`O_FL_ENB_TO_LMT_BASE_INFO_QUERY_ACK`**

| 参数名称          | 数据类型 | 取值范围                          | 含义     |
| :---------------- | :------- | :-------------------------------- | :------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                               | 消息头(0xF02C) |
| `u32EnbBaseInfoType` | `U32`    | 0: 设备型号  1：硬件版本  2：软件版本  3：序列号  4：MAC地址  5：uboot版本号  6：板卡温度 | 查询信息的类型 |
| `u8EnbbaseInfo[100]` | `U8`     | 字符串，以'\\0'结束                | 信息上报 |

---
### 服务小区配置参数查询(0xF027)

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_GET_ARFCN(0xF027)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_ARFCN_IND(0xF028)`

**消息结构：`O_FL_ENB_TO_LMT_ARFCN_IND`**

| 参数名称          | 数据类型 | 取值范围                 | 含义         |
| :---------------- | :------- | :----------------------- | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                      | 消息头  (0xF028) |
| `stServingCellCfgInfo` | 结构体   | 参见4.7.1中`ServingCellCfgInfo` | 小区信息配置 |

---
### 基站同步信息查询(0xF02D)

**功能描述**：
此消息用于客户端查询基站当前的同步方式和同步状态。

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_SYNC_INFO_QUERY(0xF02D)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_SYNC_INFO_QUERY_ACK(0xF02E)`

**消息结构：`O_FL_ENB_TO_LMT_SYNC_INFO_QUERY_ACK`**

| 参数名称          | 数据类型 | 取值范围                             | 含义     |
| :---------------- | :------- | :----------------------------------- | :------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                                  | 消息头(0xF02E) |
| `u16SyncMode`     | `U16`    | 0：空口同步（FDD此值代表 GPS同步disable）  1：GPS同步 | 同步类型 |
| `u16SyncState`    | `U16`    | 0：GPS同步成功；1：空口同步成功，2：未同步  3:GPS失步  4：空口失步 | 同步状态 |

---
### 小区状态信息查询(0xF02F)

**功能描述**：
此消息用于查询基站的小区状态信息。

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_CELL_STATE_INFO_QUERY(0xF02F)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_CELL_STATE_INFO_QUERY_ACK (0xF030)`

**消息结构：`O_FL_ENB_TO_LMT_CELL_STATE_INFO_QUERY_ACK`**

| 参数名称          | 数据类型 | 取值范围                                                 | 含义     |
| :---------------- | :------- | :------------------------------------------------------- | :------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                                                      | 消息头(0xF030) |
| `u32CellState`    | `U32`    | 0：小区空闲态  1：同步或扫频中  2：小区激活中  3：小区已激活  4：小区去激活中  5： 同步成功，REM处于ON状态  6: 同步中  7: 频偏校准进行中 | 小区状态 |

---
### GPS经纬高度查询(0xF05C)

**功能描述**：
基站启动时，会自动获取GPS经纬度信息，客户端可通过此查询接口获取的经纬度信息。

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_GPS_LOCATION_QUERY (0xF05C)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_GPS_LOCATION_QUERY_ACK(0xF05D)`

**消息结构：`O_FL_ENB_TO_LMT_GPS_LOCATION_QUERY_ACK`**

| 参数名称     | 数据类型 | 取值范围   | 含义                                |
| :----------- | :------- | :--------- | :---------------------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`        | 消息头(0xF05D)                      |
| `Paraoff1`   | `U32`    | `/`        | 保留字节                            |
| `Longitude`  | `F64`    | `64位浮点数` | 经度（正值为东经，负值为西经）      |
| `Latitude`   | `F64`    | `64位浮点数` | 维度（正值为北纬，负值为南纬）      |
| `Altitude`   | `F64`    | `64位浮点数` | 高度                                |
| `RateOfPro`  | `U32`    | `0~100`    | GPS经纬高度获取进度,百分比的值，例如：  50对应50% |
| `Paraoff2`   | `U32`    | `/`        | 保留字节                            |

---
### 异频同步接口配置(0xF05E)

**是否立即生效**：是

**重启是否保留配置**：是（SNF端口和时偏保留，频点有场景保留有场景不保留）

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_SYNCARFCN_CFG_ACK(0xF05F)`

**功能描述**：
该接口用于tdd空口异频同步，该接口只是个配置接口，配置完该接口要配置`O_FL_LMT_TO_ENB_SYS_ARFCN_CFG`接口，该功能才生效；如果SNF接口一旦配置，要改成同频同步需要用本接口的RX接口配置及同频频点配置回来才能继续使用同频同步功能（注意时偏也要配置回来）。

注：配置的频点，自动建小区保留，不自动建小区不保留，需要客户设计后台时，只要上电/reboot不自动建小区，需要重新配置该接口，再配合`O_FL_LMT_TO_ENB_SYS_ARFCN_CFG`接口使用；reboot后自动建小区，如果第一次同步不成功也要重新配置该接口再配合`O_FL_LMT_TO_ENB_SYS_ARFCN_CFG`接口使用。

**消息结构：`O_FL_LMT_TO_ENB_SYNCARFCN_CFG`**

| 参数名称          | 数据类型 | 取值范围 | 含义                                       |
| :---------------- | :------- | :------- | :----------------------------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF05E)                             |
| `UsingSnfSwitch`  | `U8`     | `/`      | 0: 使用Rx同步  1：使用SNF同步  2: 取消异频同步 |
| `Reserved1[3]`    | `U8`     | `/`      | 保留字节                                   |
| `SyncArfcn`       | `U32`    | `/`      | 同步频点                                   |
| `TimeOffsetPresent` | `U8`     | `/`      | 0：不需要配置  1：需要配置                 |
| `Reserved2[3]`    | `U8`     | `/`      | 保留字节                                   |
| `S32`             | `TimeOffsetValue` | `/`      | 异频同步时偏值（不同band间）               |

---
### 辅PLMN列表配置(0xF060)

**是否立即生效**：否

**重启是否保留配置**：是

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_SECONDARY_PLMNS_SET_ACK (0xF061)`

**功能描述**：
此接口用于配置基站广播SIB1 中PLMN LIST字段中的非主PLMN。

**消息结构：`O_FL_LMT_TO_ENB_SECONDARY_PLMNS_SET`**

| 参数名称          | 数据类型 | 取值范围                                 | 含义         |
| :---------------- | :------- | :--------------------------------------- | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                                      | 消息头(0xF060) |
| `u8SecPLMNNum`    | `U8`     | `1~5`                                    | 辅PLMN的数目 |
| `u8SecPLMNList[5][7]` | `U8`     | 字符数组，以结束符结束eg: "46000" "46001" | 辅PLMN列表   |

---
### GPS经纬度信息复位配置(0xF06D)

**是否立即生效**：是

**重启是否保留配置**：否

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_GPS_INFO_RESET_ACK (0xF06E)`

**功能描述**：
基站启动时，会自动获取GPS经纬度信息，获取的经纬度信息可通过4.9章节的《[GPS经纬高度查询](#_Gps经纬高度查询)》接口查询，一旦获取到经纬度信息，基站会保存此次获取的值，下次重启将不再重复获取，因此如果基站移动了位置，请使用此接口清除上一次获取的经纬度信息。

**消息结构：`O_FL_LMT_TO_ENB_GPS_INFO_RESET`**

| 参数名称          | 数据类型 | 取值范围 | 含义         |
| :---------------- | :------- | :------- | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF06D) |

---
### 基站版本升级配置(0xF06F)

**是否立即生效**：是

**重启是否保留配置**：否

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_UPDATE_SOFT_VERSION_CFG_ACK (0x070)`

**功能描述**：
此接口用于客户端配置升级基站版本使用，需要客户端建立FTP服务器。

**消息结构：`O_FL_LMT_TO_ENB_UPDATE_SOFT_VERSION_CFG`**

| 参数名称          | 数据类型 | 取值范围                                                 | 含义                           |
| :---------------- | :------- | :------------------------------------------------------- | :----------------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                                                      | 消息头(0xF06F)                 |
| `updateType`      | `U8`     | `0：基站软件版本;  1：uboot版本;  2: 同时升级基站软件版本和uboot版本` | 升级版本类型                   |
| `enbSoftFileName[102]` | `U8`     | 字符串，以'\\0'结束                                       | 字符串，基站软件版本名字：如  "BaiStation128D_ FDD_R002C0000G01B005.IMG"; |
| `isReservedCfg`   | `U8`     | `0：不保留  1：保留`                                     | 是否保留配置（仅对基站软件系统有效） |
| `enbSoftMD5 [36]` | `U8`     | 字符串，以'\\0'结束                                       | 针对升级基站软件计算的md5值,32字节长度。 |
| `uBootFileName[40]` | `U8`     | 字符串，以'\\0'结束                                       | 该字段为boot文件名，如："u-boot-t2200-nand-1.0.15.img" |
| `ubootMD5 [36]`   | `U8`     | 字符串，以'\\0'结束                                       | 针对升级文件计算的md5值,32字节长度。 |
| `isCfgFtpServer`  | `U8`     | `0：不配置  1：配置`                                     | 是否重新配置FTP服务器地址      |
| `FtpServerIp[16]` | `U8`     | 字符串，以'\\0'结束                                       | FTP服务器IP, eg: "************"， |
| `Reserved[3]`     | `U8`     | `/`                                                      | 保留字节                       |
| `FtpServerPort`   | `U32`    | `/`                                                      | FTP服务器端口号，eg: 21        |
| `FtpLoginNam[20]` | `U8`     | 字符串，以'\\0'结束                                       | Ftp用户名，eg: "kkk "           |
| `FtpPassword[10]` | `U8`     | 字符串，以'\\0'结束                                       | Ftp登录密码, eg: "123456 "     |
| `FtpServerFilePath[66]` | `U8`     | 字符串，以'\\0'结束                                       | 待升级文件所在FTP服务器路径，默认根目录。路径以/结尾。  eg：待升级文件位于FTP服务器根目录下的filePath文件夹，完整的路径为："/filePath/" |

**响应：`O_FL_ENB_TO_LMT_UPDATE_SOFT_VERSION_CFG_ACK`**

| 参数名称          | 数据类型 | 取值范围 | 含义               |
| :---------------- | :------- | :------- | :----------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF070)     |
| `VerType`         | `U8`     | `0: 基站软件版本  1: uboot` | 指示升级的版本类型 |
| `StatusInd`       | `U8`     | `0..255` | 指示执行状态(UpgradeStatusId) |
| `Reserved[2]`     | `U8`     | `/`      | 预留               |

`UpgradeStatusId`:

| 值       | 含义           |
| :------- | :------------- |
| `0`      | 参数校验错误   |
| `1`      | 升级冲突，有升级执行中 |
| `……`     | 预留           |
| `21`     | 开始传输升级文件 |
| `22`     | 传输成功       |
| `23`     | 传输失败       |
| `24`     | 校验md5成功    |
| `25`     | 校验md5失败    |
| `26`     | 开始执行升级   |
| `27`     | 升级成功       |
| `28`     | 升级失败       |
| `29`     | 内部错误，升级失败 |
| `30`     | 升级成功，开始重启基站 |

---
### 获取基站log(0xF071)

**功能描述**：
（无详细功能描述）

**消息结构：`O_FL_LMT_TO_ENB_GET_ENB_LOG`**

| 参数名称          | 数据类型 | 取值范围                                                 | 含义         |
| :---------------- | :------- | :------------------------------------------------------- | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                                                      | 消息头(0xF071) |
| `isCfgFtpServer`  | `U8`     | `0`                                                      | 0:不配置（使用取值范围，如下）  1:配置 |
| `FtpServerIp[16]` | `U8`     | 字符串，以'\\0'结束。                                     | FTP服务器IP, eg："************" |
| `Res [3]`         | `U8`     | `/`                                                      | 保留字节     |
| `FtpServerPort`   | `U32`    | `/`                                                      | FTP服务器端口号，例如21 |
| `FtpLoginNam[20]` | `U8`     | 字符串，以'\\0'结束                                       | Ftp用户名， "KKK" |
| `FtpPassword[10]` | `U8`     | 字符串，以'\\0'结束                                       | Ftp登录密码，"123456 " |
| `FtpServerFilePath[66]` | `U8`     | 字符串，以'\\0'结束                                       | 上传文件放置目录,不支持中文目录名,目录以/结尾。  Eg: 欲放置文件于FTP服务器根目录下的filePath文件夹，完整的路径为："/filePath/" |

**响应：`O_FL_ENB_TO_LMT_GET_ENB_LOG_ACK`**

| 参数名称    | 数据类型 | 取值范围   | 含义     |
| :---------- | :------- | :--------- | :------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`        | 消息头(0xF072) |
| `CfgResult` | `U8`     | `0:成功  >0:错误编号` | 配置结果 |
| `failCause[23]` | `U8`     | 字符串，以'\\0'结束 | 指示失败的原因 |

---
### 接收增益和发射功率查询(0xF031)

**功能描述**：
（无详细功能描述）

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_RXGAIN_POWER_DEREASE_QUERY(0xF031)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_RXGAIN_POWER_DEREASE_QUERY_ACK (0xF032)`

**消息结构：`O_FL_ENB_TO_LMT_RXGAIN_POWER_DEREASE_QUERY_ACK`**

| 参数名称          | 数据类型 | 取值范围   | 含义                                                         |
| :---------------- | :------- | :--------- | :----------------------------------------------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`        | 消息头(0xF032)                                               |
| `u8RxGainValueFromReg` | `U8`     | `0~127`    | 寄存器中的值，实际生效的值（FDD模式下仅在建立完小区查询，该值有效） |
| `u8RxGainValueFromMib` | `U8`     | `0~127`    | 数据库中的保存值，重启保留生效的值,                            |
| `u8PowerDereaseValueFromReg` | `U8`     | `0~255`    | 寄存器中的值，实际生效的值（FDD模式下仅在建立完小区查询，该值有效） |
| `u8PowerDereaseValueFromMib` | `U8`     | `0~255`    | 数据库中的保存值，重启保留生效的值                             |
| `u8AgcFlag`       | `U8`     | `0：关闭  1：打开` | FDD AGC开关                                                  |
| `u8SnfRxGainValueFromReg` | `U8`     | `0~127`    | 只在FDD模式下有效，寄存器中的值，实际生效的值,该值只有在扫频完成后，建立小区前查询有效 |
| `u8SnfRxGainValueFromMib` | `U8`     | `0~127`    | eeprom中的保存值，重启保留生效的值                             |
| `Res[1]`          | `U8`     | `/`        | 保留字段                                                     |
| `pwrDecreDelta`   | `S32`    | `-10~10dB` | `u8PowerDereaseValueFromReg = u8PowerDereaseValueFromMib +4*delta`，  仅DW版本有效 |

---
### 重定向配置查询(0xF03F)

**功能描述**：
（无详细功能描述）

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_REDIRECT_INFO_CFG_QUERY(0xF03F)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_REDIRECT_INFO_CFG_QUERY_ACK (0xF040)`

**消息结构：`O_FL_ENB_TO_LMT_REDIRECT_INFO_CFG_QUERY_ACK`**

| 参数名称          | 数据类型 | 取值范围 | 含义       |
| :---------------- | :------- | :------- | :--------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF040) |
| `OnOff`           | `U32`    | `0：打开  1：关闭` | 重定向开关 |
| `Earfcn`          | `U32`    | `0~65535` | 重定向频点 |
| `RedirectType`    | `U32`    | `0：4G  1：3G  2：2G` | 重定向类型 |

---
### 上电小区自激活配置查询(0xF041)

**功能描述**：
（无详细功能描述）

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_SELF_ACTIVE_CFG_PWR_ON_QUERY (0xF041)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_SELF_ACTIVE_CFG_PWR_ON_QUERY_ACK (0xF042)`

**消息结构：`O_FL_ENB_TO_LMT_SELF_ACTIVE_CFG_PWR_ON_QUERY_ACK`**

| 参数名称          | 数据类型 | 取值范围             | 含义         |
| :---------------- | :------- | :------------------- | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                  | 消息头(0xF042) |
| `SelfActiveCfg`   | `U32`    | `0：上电自激活  1：上电不自激活` | 基站上电是否采用当前配置自动激活小区 |

---
### TDD子帧配置和上行功控系数查询(0xF04B)

**功能描述**：
（无详细功能描述）

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_TDD_SUBFRAME_ASSIGNMENT_QUERY(0xF04B)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_TDD_SUBFRAME_ASSIGNMENT_QUERY_ACK(0xF04C)`

**消息结构：`O_FL_ENB_TO_LMT_TDD_SUBFRAME_ASSIGNMENT_QUERY_ACK`**

| 参数名称          | 数据类型 | 取值范围                             | 含义                     |
| :---------------- | :------- | :----------------------------------- | :----------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                                  | 消息头(0xF04C)           |
| `u8TddSfAssignment` | `U8`     | `1：sa1  2：sa2`                     | TDD子帧配比（fdd该值为255） |
| `u8TddSpecialSfPatterns` | `U8`     | `5：ssp5  7：ssp7`                   | TDD特殊子帧配比（fdd该值为255） |
| `u8UlAlpha`       | `U8`     | `（0，40，50，60，70，80，90，100）` | Sib2中上行功控系数       |
| `Res[1]`          | `U8`     | `/`                                  | 保留字节                 |

---
### UE NAS REJECT CAUSE配置查询(0xF06B)

**功能描述**：
（无详细功能描述）

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_TAU_ATTACH_REJECT_CAUSE_QUERY (0xF06B)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_TAU_ATTACH_REJECT_CAUSE_QUERY_ACK (0xF06C)`

**消息结构：`O_FL_ENB_TO_LMT_TAU_ATTACH_REJECT_CAUSE_QUERY_ACK`**

| 参数名称    | 数据类型 | 取值范围                                                 | 含义                                       |
| :---------- | :------- | :------------------------------------------------------- | :----------------------------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                                                      | 消息头(0xF06C)                             |
| `RejectCause` | `U32`    | `0：#cause15 （追踪区不允许接入）  1：#cause12 (追踪区无合适小区)  2: #cause3 （无效终端）  3：#cause13  4：#cause22` | 回复UE的TAU REJECT或者ATTACH REJECT消息中的reject cause值 |

---
### GPS同步模式下的pp1s偏移量查询(0xF073)

**功能描述**：
（无详细功能描述）

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_GPS1PPS_QUERY (0xF073)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_GSP1PPS_QUERY_ACK (0xF074)`

**消息结构：`O_FL_ENB_TO_LMT_GSP1PPS_QUERY_ACK`**

| 参数名称          | 数据类型 | 取值范围 | 含义 |
| :---------------- | :------- | :------- | :--- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF074) |
| `Gpspps1s`        | `S32`    | `/`      | /    |

---
### 频点自配置后台频点列表查询(0xF04D)

**功能描述**：
（无详细功能描述）

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_SELFCFG_ARFCN_QUERY (0xF04D)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_SELFCFG_ARFCN_QUERY_ACK (0xF04E)`

**消息结构：`O_FL_ENB_TO_LMT_SELFCFG_ARFCN_QUERY_ACK`**

| 参数名称          | 数据类型 | 取值范围 | 含义     |
| :---------------- | :------- | :------- | :------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF04E) |
| `DefaultArfcnNum` | `U32`    | `/`      | /        |
| `ArfcnValue[C_MAX_DEFAULT_ARFCN_NUM]` | `U32`    | `/`      | 频点值列表 |

---
### 选频配置查询(0xF088)

**功能描述**：
（无详细功能描述）

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_SELECT_FREQ_CFG_QUERY (0xF088)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_SELECT_FREQ_CFG_QUERY_ACK (0xF089)`

**消息结构：`O_FL_ENB_TO_LMT_SELECT_FREQ_CFG_QUERY_ACK`**

| 参数名称          | 数据类型 | 取值范围                  | 含义         |
| :---------------- | :------- | :------------------------ | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                       | 消息头(0xF089) |
| `PinBandRelaNum`  | `U32`    | `1~15`                    | 指定结构体数组元素个数。 |
| `pinBandRelaMap[15]` | `PinBandRelation` | `/`                       | 管脚频带关系表 |

`PinBandRelation`：

| 参数名称   | 数据类型 | 取值范围                      | 含义         |
| :--------- | :------- | :---------------------------- | :----------- |
| `pinValue` | `U8`     | `V2 Board：{0..3}  V3 Board：{1..15}` | 管脚取值范围，低位有效。 |
| `BandVal1` | `U8`     | `V2 Board：{0..3}  V3 Board：{1..15}` | 频带取值范围1..44;  0:不配置。 |
| `BandVal2` | `U8`     | `V2 Board：{0..3}  V3 Board：{1..15}` | 频带取值范围1..44;  0:不配置。 |
| `BandVal3` | `U8`     | `V2 Board：{0..3}  V3 Board：{1..15}` | 频带取值范围1..44  0:不配置。 |

---
### 辅PLMN 列表查询(0xF062)

**功能描述**：
（无详细功能描述）

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_SECONDARY_PLMNS_QUERY (0xF062)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_SECONDARY_PLMNS_QUERY_ACK (0xF063)`

**消息结构：`O_FL_ENB_TO_LMT_SECONDARY_PLMNS_QUERY_ACK`**

| 参数名称          | 数据类型 | 取值范围                                 | 含义         |
| :---------------- | :------- | :--------------------------------------- | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                                      | 消息头(0xF063) |
| `u8SecPLMNNum`    | `U8`     | `0`                                      | 辅PLMN的数目 |
| `u8SecPLMNList[5][7]` | `U8`     | 字符数组，以结束符结束eg:  “46000”  “46001” | 辅PLMN列表   |

---
### IMSI黑白名单查询(0xF043)

**功能描述**：
（无详细功能描述）

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_CONTROL_LIST_QUERY (0xF043)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_CONTROL_LIST _QUERY_ACK (0xF044)`

**消息结构：`O_FL_ENB_TO_LMT_CONTROL_LIST_QUERY`**

| 参数名称          | 数据类型 | 取值范围       | 含义     |
| :---------------- | :------- | :------------- | :------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`            | 消息头(0xF043) |
| `ControlListType` | `U8`     | `0：查询黑名单  1：查询白名单` | 名单类型 |
| `Res[3]`          | `U8`     | `/`            | 补充字节 |

**响应：`O_FL_ENB_TO_LMT_CONTROL_LIST _QUERY_ACK`**

| 参数名称          | 数据类型 | 取值范围                     | 含义         |
| :---------------- | :------- | :--------------------------- | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                          | 消息头(0xF044) |
| `ControlListProperty` | `U16`    | `0:黑名单  1:白名单`         | 名单类型     |
| `ControlListUENum` | `U16`    | `0~ C_MAX_CONTROL_LIST_UE_NUM` | 名单中含有的UE数目 |
| `ControlListUEId  [C_MAX_CONTROL_LIST_UE_NUM]  [C_MAX_IMSI_LEN]` | `U8`     | IMSI字符串，以结束符结束     | IMSI字符串，如：  "460011111111111"  非有效UE ID为'\\0'。(该字段为固定长度) |

---
### UE测量配置查询(0xF03D)

**功能描述**：
（无详细功能描述）

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_MEAS_UE_CFG_QUERY (0xF03D)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_MEAS_UE_CFG_QUERY_ACK(0xF03E)`

**消息结构：`O_FL_ENB_TO_LMT_MEAS_UE_CFG_QUERY_ACK`**

| 参数名称          | 数据类型 | 取值范围         | 含义 |
| :---------------- | :------- | :--------------- | :--- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`              | 消息头(0xF03E) |
| `stUeMeasCfg`     | `wrUeMeasCfg` | 参见第4章wrUeMeasCfg | /    |

---
### IMSI文件上传配置查询(0xF094)

**功能描述**：
（无详细功能描述）

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_UPLOAD_IMSI_FILE_CFG_QUERY(0xF094)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_UPLOAD_IMSI_FILE_CFG_QUERY_ACK(0xF095)`

**消息结构：`O_FL_ENB_TO_LMT_UPLOAD_IMSI_FILE_CFG_QUERY_ACK`**

| 参数定义    | 数据类型 | 取值范围 | 含义                                             |
| :---------- | :------- | :------- | :----------------------------------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头定义(0xF095)                               |
| `UploadImsiFileCfg` | `UploadImsiFileCfg` | `/`      | 见4.7.28节 “上传IMSI文件配置”中的定义；  说明：`isCfgFtpServer`字段在查询接口中无意义。 |

---
### NTP同步状态查询(0xF09B)

**功能描述**：
（无详细功能描述）

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_NTP_SYNC_STATE_QUERY(0xF09B)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_NTP_SYNC_STATE_QUERY_ACK(0xF09C)`

**消息结构：`O_FL_ENB_TO_LMT_NTP_SYNC_STATE_QUERY_ACK`**

| 参数定义   | 数据类型 | 取值范围        | 含义 |
| :--------- | :------- | :-------------- | :--- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`             | 消息头定义(0xF09C) |
| `u8NtpSyncState` | `U8`     | `0：Not Sync  1：Sync Succ` | /    |
| `u8Res[3]` | `U8`     | `/`             | 空余字节 |

---
### GPS芯片选择gps或北斗配置查询(0xF099)

**功能描述**：
（无详细功能描述）

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_GPS_OR_BEIDOU_CFG_QUERY(0xF099)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_GPS_OR_BEIDOU_CFG_QUERY_ACK (0xF09A)`

**消息结构：`O_FL_ENB_TO_LMT_GPS_OR_BEIDOU_CFG_QUERY_ACK`**

| 参数名称    | 数据类型 | 取值范围       | 含义 |
| :---------- | :------- | :------------- | :--- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`            | 消息头(0xF09A) |
| `u32FlagInUse` | `U32`    | `0：GPS  1：北斗` | /    |

---
### Band功率衰减关系表查询(0xF0A9)

**功能描述**：
该接口用于查询band和衰减对应关系表。返回的结构体和配置的结构体一致。

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_ MULTI_BAND_POWERDEREASE_QUERY(0xF0A9)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_MULTI_BAND_ POWERDEREASE_ QUERY_ACK (0xF0AA)`

**消息结构：`O_FL_ENB_TO_LMT_MULTI_BAND_POWERDEREASE_QUERY`**

| 参数名称          | 数据类型 | 取值范围 | 含义                             |
| :---------------- | :------- | :------- | :------------------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF0AA)                   |
| `NumElem`         | `U8`     | `0..32`  | 0: 关闭功能  >0:配置相关关系及关系个数 |
| `Reserved[3]`     | `U8`     | `/`      | 预留                             |
| `BandPwrdereaseMap[32]` | `bandPwrdereaseMap` | `/`      | Band和衰减值对应关系             |

`bandPwrdereaseMap`

| 参数名称    | 数据类型 | 取值范围 | 含义   |
| :---------- | :------- | :------- | :----- |
| `band`      | `U8`     | `1..255` | 频带值 |
| `Pwrderease` | `U8`     | `0..255` | 衰减值 |
| `Reserved[2]` | `U8`     | `/`      | 预留   |

---
### Band接收增益关系表查询(0xF0CA)

**功能描述**：
该接口用于查询band和接收增益对应关系表。返回的结构体和配置的结构体一致。

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_MULTI_BAND_RXGAIN_QUERY(0xF0CA)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_MULTI_BAND_RXGAIN_QUERY_ACK (0xF0CB)`

**消息结构：`O_FL_ENB_TO_LMT_MULTI_BAND_RXGAIN_QUERY_ACK`**

| 参数名称          | 数据类型 | 取值范围 | 含义                             |
| :---------------- | :------- | :------- | :------------------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF0CB)                   |
| `NumElem`         | `U8`     | `0..32`  | 0: 关闭功能  >0:配置相关关系及关系个数 |
| `Reserved[3]`     | `U8`     | `/`      | 预留                             |
| `BandRxgainMap[32]` | `bandRxgainMap` | `/`      | Band和增益值对应关系             |

`bandRxgainMap`

| 参数名称    | 数据类型 | 取值范围 | 含义   |
| :---------- | :------- | :------- | :----- |
| `band`      | `U8`     | `1..127` | 频带值 |
| `RxGain`    | `U8`     | `0..127` | 接收增益值 |
| `Reserved[2]` | `U8`     | `/`      | 预留   |

---
### RX口功率值查询(0xF0AB)

**功能描述**：
该接口用于获取RX口的功率值，仅在小区去激活状态有效。

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_GET_RX_PARAMS(0xF0AB)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_GET_RX_PARAMS_ACK(0xF0AC)`

**消息结构：`O_FL_ENB_TO_LMT_GET_RX_PARAMS_ACK`**

| 参数名称          | 数据类型 | 取值范围   | 含义             |
| :---------------- | :------- | :--------- | :--------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`        | 消息头(0xF0AC)   |
| `RxPwrVal`        | `F32`    | `32位浮点取值范围` | RX口功率值，单位:dbm |

---
### 扫频/同步端口查询(0xF0AD)

**功能描述**：
（无详细功能描述）

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_REM_PORT_QUERY (0xF0AD)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_REM_PORT_QUERY_ACK (0xF0AE)`

**消息结构：`O_FL_ENB_TO_LMT_REM_PORT_QUERY_ACK`**

| 参数名称  | 数据类型 | 取值范围 | 含义       |
| :-------- | :------- | :------- | :--------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF0AE) |
| `ScanPort` | `U8`     | `0,1`    | 扫频端口  1：Snf口  0：Rx口 |
| `SyncPort` | `U8`     | `0,1`    | 同步端口  1：Snf口  0：Rx口 |
| `Spare`   | `U16`    | `/`      | /          |

---
### GPS观星数量及其信噪比查询(0xF0E4)

**功能描述**：
（无详细功能描述）

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_GPS_SATELLITE_SIGNAL_LEVEL_QUERY(0xF0E4)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_GPS_SATELLITE_SIGNAL_LEVEL_QUERY_ACK (0xF0E5)`

**消息结构：`O_FL_ENB_TO_LMT_GPS_SATELLITE_SIGNAL_LEVEL_QUERY_ACK`**

| 参数名称     | 数据类型 | 取值范围 | 含义           |
| :----------- | :------- | :------- | :------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF0E5) |
| `SvNumber`   | `U32`    | `0~14`   | 观测到的GPS卫星数量 |
| `SvSignalPair[14]` | `wrsvssignalpair` | `/`      | 卫星编号及其信噪比 |

`wrsvssignalpair`

| 参数名称    | 数据类型 | 取值范围 | 含义   |
| :---------- | :------- | :------- | :----- |
| `SvCount`   | `U32`    | `0~237`  | 卫星编号 |
| `Signalval` | `F32`    | `/`      | 卫星信噪比 |

---
### 异频频点列表查询(0xF0EA)

**功能描述**：
（无详细功能描述）

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_EUTRA_FREQ_QUERY(0xF0EA)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_EUTRA_FREQ_QUERY_ACK (0xF0EB)`

**消息结构：`O_FL_ENB_TO_LMT_EUTRA_FREQ_QUERY_ACK`**

| 参数名称          | 数据类型 | 取值范围 | 含义         |
| :---------------- | :------- | :------- | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF0EB) |
| `EutraArfcnNumber` | `U32`    | `1~4`    | 异频频点数量 |
| `EutraArfcn[C_MAX_EUTRA_ARFCN]` | `U32`    | `下行频点号` | /            |

---
### QRxLevMin查询(0xF035)

**功能描述**：
（无详细功能描述）

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_QRXLEVMIN_VALUE_QUERY (0xF035)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_QRXLEVMIN_VALUE_QUERY_ACK (0xF036)`

**消息结构：`O_FL_ENB_TO_LMT_QRXLEVMIN_VALUE_QUERY_ACK`**

| 参数名称          | 数据类型 | 取值范围     | 含义 |
| :---------------- | :------- | :----------- | :--- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`          | 消息头(0xF036) |
| `s32QrxlevMin`    | `S32`    | `(- 70 ~ - 22)` | /    |

---
### 基站执行状态查询(0xF01A)

**功能描述**：
基站执行状态会实时上报，一般不需要查询。

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_GET_ENB_STATE（0xF01A）`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_ENB_STATE_IND (0xF019)`

**消息结构：`O_FL_ENB_TO_LMT_ENB_STATE_IND`**

| 参数名称     | 数据类型 | 取值范围        | 含义               |
| :----------- | :------- | :-------------- | :----------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`             | 消息头(0xF019)     |
| `CellStateInd` | `U32`    | `WR_FL_ENB_STATE` | `wrFLEnbToLmtEnbStateInd` |

`WR_FL_ENB_STATE`:

| 值       | 含义           |
| :------- | :------------- |
| `0`      | 空口同步成功   |
| `1`      | 空口同步失败   |
| `2`      | GPS同步成功    |
| `3`      | GPS同步失败    |
| `4`      | 扫频成功       |
| `5`      | 扫频失败       |
| `6`      | 小区激活成功   |
| `7`      | 小区激活失败   |
| `8`      | 小区去激活     |
| `9`      | 空口同步中     |
| `10`     | GPS同步中      |
| `11`     | 扫频中         |
| `12`     | 小区激活中     |
| `13`     | 小区去激活中   |
| `14`     | 频偏校准开始   |
| `15`     | 频偏校准进行中 |
| `16`     | 频偏校准结束   |
| `0xFFFF` | 无效状态       |

---
### 基站IP查询(0xF033)

**功能描述**：
（无详细功能描述）

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_ENB_IP_QUERY (0xF033)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_ENB_IP_QUERY_ACK (0xF034)`

**消息结构：`O_FL_ENB_TO_LMT_ENB_IP_QUERY_ACK`**

| 参数名称       | 数据类型 | 取值范围    | 含义         |
| :------------- | :------- | :---------- | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`         | 消息头(0xF034) |
| `u8EnbIp[4]`   | `U8`     | `0~255`     | 基站IP       |
| `u8EnbSubMask[4]` | `U8`     | `0~255`     | 基站子网掩码 |
| `u8EnbGateWay[4]` | `U8`     | `0~255`     | 基站网管     |
| `u32EnbPort`   | `U32`    | `1~65535`   | 基站端口号   |
| `u8PcMonitorIp[4]` | `U8`     | `0~255`     | 上位机IP     |
| `u32PcMonitorPort` | `U32`    | `1~65535`   | 上位机端口号 |

---
### 扫频频点配置查询(0xF037)

**功能描述**：
（无详细功能描述）

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_REM_CFG_QUERY (0xF037)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_REM_CFG_QUERY_ACK (0xF038)`

**消息结构：`O_FL_ENB_TO_LMT_REM_CFG_QUERY_ACK`**

| 参数名称     | 数据类型 | 取值范围        | 含义         |
| :----------- | :------- | :-------------- | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`             | 消息头(0xF038) |
| `wideRemSREnable` | `U32`    | `0：不开启；  1：开启` | 是否开启全频段扫频 |
| `sysEarfcnNum` | `U32`    | `1~10`          | 扫频频点数目 |
| `sysEarfcn[10]` | `U32`    | `频点数组`      | 频点，如38400等 |

---
### 轮循载波信息查询(0xF0F2)

**功能描述**：
（无详细功能描述）

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_ROLL_CARRIER_QUERY (0xF0F2)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_ROLL_CARRIER_QUERY_ACK (0xF0F3)`

**消息结构：`O_FL_ENB_TO_LMT_ROLL_CARRIER_QUERY_ACK`**

| 参数名称          | 数据类型 | 取值范围                 | 含义         |
| :---------------- | :------- | :----------------------- | :----------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`                      | 消息头(0xF0F3) |
| `queryResult`     | `U8`     | `0：查询失败  1：查询成功` | 查询结果     |
| `rollCarrierCfgNum` | `U8`     | `0-8`                    | 轮循载波数，0表示无载波配置 |
| `bRollCarrierCfg` | `U8`     | `0-1`                    | 0：无正在轮循的载波信息  1：有正在轮循的载波信息 |
| `Res[1]`          | `U8`     | `/`                      | 预留         |
| `stRollCarrierCfg[8]` | `wrRollCarrierCfg` | `参见F0F0接口wrRollCarrierCfg` | /            |
| `curRollCarrierCfg` | `wrRollCarrierCfg` | `参见F0F0接口wrRollCarrierCfg` | 正在轮循的载波信息 |

---
### 发射功率衰减偏移配置查询(0xF0F6)

**功能描述**：
（无详细功能描述）

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_PWR1_DEREASE_QUERY (0xF0F6)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_PWR1_DEREASE_QUERY_ACK (0xF0F7)`

**消息结构：`O_FL_ENB_TO_LMT_PWR1_DEREASE_QUERY_ACK`**

| 参数名称          | 数据类型 | 取值范围 | 含义                                                         |
| :---------------- | :------- | :------- | :----------------------------------------------------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF0F7)                                               |
| `pwr1Derease`     | `U8`     | `0~20`   | 配置0则为F015配置与F0A7生效值，配置1则在上面两条消息的基础上-1Db,2则为-2Db,19则为-19Db,20则输出功率为最小255 |
| `Res[3]`          | `U8`     | `/`      | 预留                                                         |

---
### Fdd共建站重定向配置查询(0xF0E8)

**功能描述**：
（无详细功能描述）

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_FDD_REDIRECTION_CFG_QUERY(0xF0E8)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_FDD_REDIRECTION_CFG_QUERY_ACK(0xF0E9)`

**消息结构：`O_FL_ENB_TO_LMT_REDIRECT_INFO_CFG_QUERY_ACK`**

| 参数 | 数据类型 | 取值范围 | 含义       |
| :--- | :------- | :------- | :--------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF0E9) |
| `OnOff` | `U32`    | `0：打开  1：关闭` | 重定向开关 |
| `UnicomEarfcn` | `U32`    | `0~65535` | 联通重定向频点 |
| `TelecomEarfcn` | `U32`    | `0~65535` | 电信重定向频点 |

---
### TAC配置(0xF069)

**是否立即生效**：是

**重启是否保留配置**：否

**应答消息（eNB ->LMT）**：`O_FL_ENB_TO_LMT_TAC_MODIFY_REQ_ACK (0xF06A)`

**功能描述**：
此接口用于修改基站的当前TAC值。

**消息结构：`O_FL_LMT_TO_ENB_TAC_MODIFY_REQ`**

| 参数名称    | 数据类型 | 取值范围  | 含义 |
| :---------- | :------- | :-------- | :--- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`       | 消息头(0xF069) |
| `TacValue`  | `U32`    | `0~65535` | /    |

---
### 随机接入成功率问询(0xF065)

**功能描述**：
此接口用于调试测试阶段，客户端查询基站调度UE性能。一般情况下，`RrcConnCmpNum/RrcConnReqNum`可以达到90%左右。

**查询消息（LMT->eNB）**：`O_FL_LMT_TO_ENB_RA_ACCESS_QUERY (0xF065)`

**应答消息（eNB->LMT）**：`O_FL_ENB_TO_LMT_RA_ACCESS_QUERY_ACK (0xF066)`

**消息结构：`O_FL_ENB_TO_LMT_RA_ACCESS_QUERY_ACK`**

| 参数名称     | 数据类型 | 取值范围 | 含义           |
| :----------- | :------- | :------- | :------------- |
| `WrmsgHeaderInfo` | `wrMsgHeader` | `/`      | 消息头(0xF066) |
| `RrcConnReqNum` | `U32`    | `0`      | RRC连接请求次数 |
| `RrcConnCmpNum` | `U32`    | `0`      | RRC连接建立完成次数 |
| `Msg2SchedNum` | `U32`    | `0`      | 调度Msg2次数   |
| `Msg3SchedNum` | `U32`    | `0`      | 收到Msg3次数   |
| `RrcImsiNum`   | `U32`    | `0`      | 获取IMSI个数   |