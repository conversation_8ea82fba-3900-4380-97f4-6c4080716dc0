const fs = require('fs');
const MarkdownIt = require('markdown-it');

class ProtocolParser {
    constructor(protocolFilePath) {
        this.protocolFilePath = protocolFilePath;
        this.md = new MarkdownIt();
        this.protocolMetadataRegistry = {}; // Stores functional units, each potentially having request/response
    }

    async parseProtocolDocument() {
        try {
            const markdownContent = await fs.promises.readFile(this.protocolFilePath, 'utf8');
            const tokens = this.md.parse(markdownContent, {});
            
            this._extractMetadataFromTokens(tokens);

            console.log("Protocol parsing completed. Metadata registry:", JSON.stringify(this.protocolMetadataRegistry, null, 2));
            return this.protocolMetadataRegistry;

        } catch (error) {
            console.error("Error parsing protocol document:", error);
            throw error;
        }
    }

    _mapTypeToLlmSchemaType(protocolType, originalDescription) {
        let schema = {
            type: "string", // Default to string if unknown
            description: originalDescription // Always include original description
        };

        // Remove any Markdown bolding (**) and trim whitespace for consistent matching
        const cleanedProtocolType = protocolType.replace(/\*\*/g, '').toLowerCase().trim();

        // First, check for array-like types like "Name[Length]" or "Buffer(Length)"
        // Use cleanedProtocolType for matching
        const arrayLikeMatch = cleanedProtocolType.match(/^(?:.+)\[(\\d+)\]$|^buffer\\((\\d+)\\)$/i);

        if (arrayLikeMatch) {
            const size = parseInt(arrayLikeMatch[1] || arrayLikeMatch[2], 10); // Get size from either group 1 (for [X]) or group 2 (for (X))
            schema.type = 'string';
            schema.pattern = `^([0-9a-fA-F]{2}){${size}}$`; // Expecting hex string, 2 chars per byte
            schema.description = `${originalDescription} (${size}字节十六进制字符串)`;
            return schema;
        }

        // Existing type mappings, now using cleanedProtocolType
        if (cleanedProtocolType === 'u32') {
            schema.type = 'integer';
            schema.minimum = 0;
            schema.maximum = 4294967295; // 2^32 - 1
            schema.description = `${originalDescription} (无符号32位整数, 范围: 0 ~ 4294967295)`;
        } else if (cleanedProtocolType === 'uint16le') {
            schema.type = 'integer';
            schema.minimum = 0;
            schema.maximum = 65535; // 2^16 - 1
            schema.description = `${originalDescription} (无符号16位整数, 小端序, 范围: 0 ~ 65535)`;
        } else if (cleanedProtocolType === 'uint8') {
            schema.type = 'integer';
            schema.minimum = 0;
            schema.maximum = 255; // 2^8 - 1
            schema.description = `${originalDescription} (无符号8位整数, 范围: 0 ~ 255)`;
        } else if (cleanedProtocolType.startsWith('buffer(') && cleanedProtocolType.endsWith(')')) {
            // This block is now redundant because arrayLikeMatch handles buffer() as well.
            // Keeping it for now but might be removed in future if no other buffer() cases exist.
            const sizeMatch = cleanedProtocolType.match(/buffer\\((\\d+)\\)/);
            if (sizeMatch) {
                const size = parseInt(sizeMatch[1], 10);
                schema.type = 'string';
                schema.pattern = `^([0-9a-fA-F]{2}){${size}}$`;
                schema.description = `${originalDescription} (${size}字节十六进制字符串)`;
            } else {
                schema.description = `${originalDescription} (原始字节数组，请输入十六进制字符串)`;
            }
        } else if (cleanedProtocolType === 'string') {
            schema.type = 'string';
        } else if (cleanedProtocolType === 'bool' || cleanedProtocolType === 'boolean') {
            schema.type = 'boolean';
        } else if (cleanedProtocolType === 'float' || cleanedProtocolType === 'double') {
            schema.type = 'number';
        }
        // Add more type mappings as needed based on actual protocol.

        return schema;
    }

    _extractMetadataFromTokens(tokens) {
        let currentFunctionalUnit = null;
        let isParsingRequestSchema = false;
        let isParsingResponseSchema = false;
        let pendingResponseMessage = null; // To store response message from "应答消息" line

        for (let i = 0; i < tokens.length; i++) {
            const token = tokens[i];

            // Identify message blocks by '###' heading (h3) - start of a new functional unit
            if (token.type === 'heading_open' && token.tag === 'h3') {
                const nextToken = tokens[i + 1];
                if (nextToken && nextToken.type === 'inline') {
                    const match = nextToken.content.match(/(?:\d+\.\d+\.\d+\s+)?([^(]+?)\(0x([0-9a-fA-F]+)\)/);
                    if (match) {
                        const messageName = match[1].trim();
                        const messageIdFromHeading = '0x' + match[2].trim();
                        currentFunctionalUnit = {
                            name: messageName,
                            id: messageIdFromHeading, // This ID is typically the request ID for the functional unit
                            description: [],
                            metadata: {}, // Add a metadata field
                            request: null,   // To be populated later
                            response: null   // To be populated later
                        };
                        this.protocolMetadataRegistry[messageName] = currentFunctionalUnit;
                        i++; // Skip the inline token for the heading
                        // Reset flags for new functional unit
                        isParsingRequestSchema = false;
                        isParsingResponseSchema = false;
                        pendingResponseMessage = null; // Reset pending response message
                        continue;
                    } else {
                        // Handle cases where a h3 is not a message definition, e.g., "注意事项"
                        currentFunctionalUnit = null; // Reset if not a message heading
                    }
                }
            }

            if (currentFunctionalUnit) {
                // Process paragraphs line by line (logical lines)
                if (token.type === 'paragraph_open') {
                    let j = i + 1;
                    let currentLogicalLine = '';

                    while (j < tokens.length && tokens[j].type !== 'paragraph_close') {
                        const innerToken = tokens[j];

                        if (innerToken.type === 'inline') {
                            currentLogicalLine += innerToken.content;
                        } else if (innerToken.type === 'softbreak') {
                            // Process accumulated line at softbreak
                            ({ isParsingRequestSchema, isParsingResponseSchema, pendingResponseMessage } = this._processParagraphLogicalLine(currentLogicalLine.trim(), currentFunctionalUnit, isParsingRequestSchema, isParsingResponseSchema, pendingResponseMessage));
                            currentLogicalLine = ''; // Reset for next line
                        }
                        j++;
                    }
                    // Process any remaining content after the last softbreak or if no softbreaks
                    if (currentLogicalLine.trim() !== '') {
                        ({ isParsingRequestSchema, isParsingResponseSchema, pendingResponseMessage } = this._processParagraphLogicalLine(currentLogicalLine.trim(), currentFunctionalUnit, isParsingRequestSchema, isParsingResponseSchema, pendingResponseMessage));
                    }
                    i = j; // Move cursor past the paragraph
                    continue;
                }

                // Handle Schema from tables
                if (token.type === 'table_open') {
                    const collectedParameters = [];
                    let j = i + 1;
                    let hasProcessedHeader = false; // Flag to track if header row has been processed

                    while (j < tokens.length && tokens[j].type !== 'table_close') {
                        if (tokens[j].type === 'tr_open') {
                            const rowTokens = [];
                            let k = j + 1;
                            while (k < tokens.length && tokens[k].type !== 'tr_close') {
                                if (tokens[k].type === 'inline') {
                                    rowTokens.push(tokens[k].content.trim());
                                }
                                k++;
                            }

                            // If this is the first row after table_open and contains common header keywords, treat as header.
                            // We are relaxing the bolding requirement for headers.
                            if (!hasProcessedHeader && rowTokens.some(cellContent =>
                                cellContent.includes('参数') ||
                                cellContent.includes('数据类型') ||
                                cellContent.includes('含义')
                            )) {
                                hasProcessedHeader = true;
                                j = k; // Move to the end of the current row (header)
                                continue; // Skip header row from parameter collection
                            }

                            // Process data rows
                            // Expecting 4 columns: Name, Type, Range, Description
                            if (rowTokens.length >= 4) {
                                const paramName = rowTokens[0];
                                const paramType = rowTokens[1];
                                const paramDescription = rowTokens[3]; // Assuming '含义' is the description

                                if (paramName && paramType) { // Ensure essential fields exist
                                    // Apply cleaning to parameter description as well
                                    const cleanedParamDescription = paramDescription ? 
                                        paramDescription.replace(/\*\*/g, '').replace(/\s*\n\s*/g, ' ').replace(/\s+/g, ' ').trim() 
                                        : '';

                                    collectedParameters.push({
                                        name: paramName,
                                        type: paramType,
                                        description: cleanedParamDescription
                                    });
                                }
                            }
                            j = k; // Move to the end of the current row
                        }
                        j++;
                    }

                    if (isParsingRequestSchema && currentFunctionalUnit.request) {
                        currentFunctionalUnit.request.parameters = collectedParameters;
                        // Try to extract messageId from wrMsgHeader.description
                        const headerParam = collectedParameters.find(p => p.name === 'WrmsgHeaderInfo');
                        if (headerParam && headerParam.description) {
                            const idMatch = headerParam.description.match(/0x([0-9a-fA-F]+)/);
                            if (idMatch) {
                                currentFunctionalUnit.request.messageId = '0x' + idMatch[1];
                            }
                        }

                    } else if (isParsingResponseSchema && currentFunctionalUnit.response) {
                        currentFunctionalUnit.response.parameters = collectedParameters;
                        // Try to extract messageId from wrMsgHeader.description
                        const headerParam = collectedParameters.find(p => p.name === 'WrmsgHeaderInfo');
                        if (headerParam && headerParam.description) {
                            const idMatch = headerParam.description.match(/0x([0-9a-fA-F]+)/);
                            if (idMatch) {
                                currentFunctionalUnit.response.messageId = '0x' + idMatch[1];
                            }
                        }
                    }
                    // Reset flags after processing a table
                    isParsingRequestSchema = false;
                    isParsingResponseSchema = false;
                    pendingResponseMessage = null; // Reset pending response message
                    i = j; // Move to the end of the table
                    continue;
                }

                // If we encounter a new h2 heading, it means we've exited the current functional unit
                if (token.type === 'heading_open' && token.tag === 'h2') {
                    currentFunctionalUnit = null;
                    isParsingRequestSchema = false;
                    isParsingResponseSchema = false;
                    pendingResponseMessage = null;
                }
            }
        }
    }

    _processParagraphLogicalLine(rawLine, currentFunctionalUnit, isParsingRequestSchema, isParsingResponseSchema, pendingResponseMessage) {
        const cleanedLine = rawLine.replace(/\*\*/g, '').replace(/\s+/g, ' ').trim(); // Remove bold and normalize spaces

        // Define regex for various metadata and technical identifier lines
        const metadataPatterns = [
            /^是否立即生效：(.+)$/,
            /^重启是否保留配置：(.+)$/,
            /^消息ID:\s*(0x[0-9a-fA-F]+)$/i,
            /^(?:O_FL_)?(LMT_TO_ENB|ENB_TO_LMT)_([A-Z0-9_]+):?$/i, // Technical identifiers like O_FL_LMT_TO_ENB_... or LMT_TO_ENB_...
            /^应答消息（eNB ->LMT）：$/ // Response message indicator
        ];

        // Check if the line matches any metadata or technical pattern
        let isMetadataOrTechnical = false;
        for (const pattern of metadataPatterns) {
            if (cleanedLine.match(pattern)) {
                isMetadataOrTechnical = true;
                break;
            }
        }

        // 1. Check for Response Message Name and ID (e.g., "O_FL_ENB_TO_LMT_SECONDARY_PLMNS_SET_ACK (0xF061)")
        // This is expected *after* "应答消息（eNB ->LMT）："
        const responseMsgIdMatch = rawLine.match(/O_FL_ENB_TO_LMT_([A-Z0-9_]+)\s*\((?:0x)?([0-9a-fA-F]+)\)/i);
        if (pendingResponseMessage && responseMsgIdMatch) {
            if (currentFunctionalUnit) {
                currentFunctionalUnit.metadata.responseMessageName = responseMsgIdMatch[1];
                currentFunctionalUnit.metadata.responseMessageId = '0x' + responseMsgIdMatch[2];
            }
            pendingResponseMessage = null; // Consume the pending state
            return { isParsingRequestSchema, isParsingResponseSchema, pendingResponseMessage };
        }

        // 2. Check for "应答消息（eNB ->LMT）：" - set a flag for the next line
        if (rawLine.includes('应答消息（eNB ->LMT）：')) {
            pendingResponseMessage = true;
            // Optionally store the raw text as part of metadata if desired (e.g., if LLM needs to know the exact phrasing)
            if (currentFunctionalUnit) {
                currentFunctionalUnit.metadata.rawResponseMessageIndicator = cleanedLine;
            }
            return { isParsingRequestSchema, isParsingResponseSchema, pendingResponseMessage };
        }

        // 3. Check for technical identifiers (e.g., "O_FL_LMT_TO_ENB_SECONDARY_PLMNS_SET：")
        const messageCodeMatch = rawLine.match(/O_FL_(LMT_TO_ENB|ENB_TO_LMT)_([A-Z0-9_]+):?/i);
        if (messageCodeMatch) {
            const directionPrefix = messageCodeMatch[1];
            const messageCodeName = messageCodeMatch[2];

            if (directionPrefix.toUpperCase() === 'LMT_TO_ENB') {
                isParsingRequestSchema = true;
                isParsingResponseSchema = false;
                if (currentFunctionalUnit && !currentFunctionalUnit.request) {
                    currentFunctionalUnit.request = {
                        parameters: [],
                        direction: 'Server -> Board',
                        type: 'Request',
                        messageCodeName: messageCodeName,
                        messageId: null
                    };
                }
            } else if (directionPrefix.toUpperCase() === 'ENB_TO_LMT') {
                isParsingResponseSchema = true;
                isParsingRequestSchema = false;
                if (currentFunctionalUnit && !currentFunctionalUnit.response) {
                    currentFunctionalUnit.response = {
                        parameters: [],
                        direction: 'Board -> Server',
                        type: 'Response',
                        messageCodeName: messageCodeName,
                        messageId: null
                    };
                }
            }
            // These lines are technical indicators, not part of the semantic description
            return { isParsingRequestSchema, isParsingResponseSchema, pendingResponseMessage };
        }

        // 4. Check for other metadata lines (e.g., "是否立即生效：否", "重启是否保留配置：是", "消息ID: 0xXXXX")
        const effectiveMatch = cleanedLine.match(/^是否立即生效：(.+)$/);
        const persistMatch = cleanedLine.match(/^重启是否保留配置：(.+)$/);
        const messageIdLineMatch = cleanedLine.match(/^消息ID:\s*(0x[0-9a-fA-F]+)$/i); // Explicit message ID line

        if (effectiveMatch) {
            if (currentFunctionalUnit) {
                currentFunctionalUnit.metadata.isEffectiveImmediately = effectiveMatch[1];
            }
        } else if (persistMatch) {
            if (currentFunctionalUnit) {
                currentFunctionalUnit.metadata.persistsOnReboot = persistMatch[1];
            }
        } else if (messageIdLineMatch) {
            // Message ID from the line, not the heading (if present)
            // This ID might be different from the main ID if it's a response message ID within the description
            if (currentFunctionalUnit) {
                // Prioritize setting messageId for the respective request/response based on current parsing state
                if (isParsingRequestSchema && currentFunctionalUnit.request) {
                    currentFunctionalUnit.request.messageId = messageIdLineMatch[1];
                } else if (isParsingResponseSchema && currentFunctionalUnit.response) {
                    currentFunctionalUnit.response.messageId = messageIdLineMatch[1];
                } else {
                    // Fallback if not specifically request/response context, store as general metadata
                    currentFunctionalUnit.metadata.explicitMessageId = messageIdLineMatch[1];
                }
            }
        }

        // If the line is not metadata or a technical identifier, add it to the description
        if (currentFunctionalUnit && !isMetadataOrTechnical && cleanedLine !== '') {
            currentFunctionalUnit.description.push(cleanedLine);
        }

        return { isParsingRequestSchema, isParsingResponseSchema, pendingResponseMessage };
    }

    generateLlmToolsSchema() {
        const llmTools = [];

        for (const unitName in this.protocolMetadataRegistry) {
            const unit = this.protocolMetadataRegistry[unitName];

            // Only generate tools for Request messages (Server -> Board)
            if (unit.request && unit.request.direction === 'Server -> Board' && unit.request.type === 'Request') {
                let toolName = null;
                // Prioritize messageCodeName if available and not empty
                if (unit.request.messageCodeName) {
                    toolName = this._snakeCase(unit.request.messageCodeName);
                }

                // Fallback to functional unit's main name (Chinese) if messageCodeName is not suitable
                if (!toolName || toolName === 'generated_name') {
                    toolName = this._snakeCase(unit.name); // This will be Chinese name converted to snake_case
                }

                // Final fallback if snakeCase still produces an empty string or is not suitable
                if (!toolName || toolName === 'generated_name') { // Re-check after trying unit.name
                    if (unit.request.messageId) {
                        toolName = 'id_' + unit.request.messageId.replace('0x', '').toLowerCase() + '_request';
                    } else if (unit.id) { // Fallback to functional unit's main ID
                        toolName = 'id_' + unit.id.replace('0x', '').toLowerCase() + '_request';
                    }
                    else {
                        toolName = 'unnamed_request_tool_' + llmTools.length;
                    }
                }

                const description = unit.description.join(' '); // Use the functional unit's description

                const properties = {};
                const requiredParams = [];

                if (unit.request.parameters) {
                    unit.request.parameters.forEach(param => {
                        // Use the new mapping function
                        const paramSchema = this._mapTypeToLlmSchemaType(param.type, param.description);
                        properties[param.name] = {
                            type: paramSchema.type,
                            description: paramSchema.description
                        };

                        if (paramSchema.minimum !== undefined) {
                            properties[param.name].minimum = paramSchema.minimum;
                        }
                        if (paramSchema.maximum !== undefined) {
                            properties[param.name].maximum = paramSchema.maximum;
                        }
                        if (paramSchema.pattern !== undefined) {
                            properties[param.name].pattern = paramSchema.pattern;
                        }

                        // Assuming all parameters are required for now, adjust if protocol specifies optional fields
                        requiredParams.push(param.name);
                    });
                }

                llmTools.push({
                    type: 'function',
                    function: {
                        name: toolName,
                        description: description,
                        parameters: {
                            type: 'object',
                            properties: properties,
                            required: requiredParams
                        }
                    }
                });
            }
        }
        console.log("Generated LLM Tools Schema:", JSON.stringify(llmTools, null, 2));
        return llmTools;
    }

    _snakeCase(text) {
        if (!text) return 'generated_name';
        let result = text.toLowerCase();
        // Replace all non-ASCII alphanumeric characters with underscores
        result = result.replace(/[^a-z0-9_]+/g, '_'); // Allow existing underscores
        // Trim leading/trailing underscores and replace multiple underscores with a single one
        result = result.replace(/^_|_$/g, '').replace(/__+/g, '_');

        if (result === '') {
            return 'generated_name';
        }
        return result;
    }
}

module.exports = ProtocolParser; 