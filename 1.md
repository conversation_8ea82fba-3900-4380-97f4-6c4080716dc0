Starting protocol parsing...
Protocol parsing completed. Metadata registry: {
  "服务小区参数配置": {
    "name": "服务小区参数配置",
    "id": "0xF003",
    "description": [
      "此接口用于配置建立小区相关参数配置，在小区激活态配置此消息，基站会执行先去激活再激活的流程；在小区IDLE态下配置此消息，基站会直接执行激活小区的流程。",
      "ServingCellCfgInfo:",
      "备注1：",
      "终端最大发射功率对应系统消息SIB1中P-Max，表示小区允许UE的最大发射功率，一般设置为23，表示23dBm。",
      "备注2：",
      "基站最大发射功率对应系统广播消息SIB2中的referenceSignalPower。此值的设置从加功放之后的总输出功率计算而来，用于终端计算路损，不会影响单板的输出功率。一般设置为20dBm（20W），此值相对于其他功率会比较大，但是经过测试，对基站性能影响不大，可以不用修改。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是。",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_SYS_ARFCN_ACK (0xF004)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头（0xF003）"
        },
        {
          "name": "stServingCellCfgInfo",
          "type": "结构体",
          "description": "小区信息配置"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SYS_ARFCN_CFG",
      "messageId": "0xF003"
    },
    "response": null
  },
  "扫频频点配置": {
    "name": "扫频频点配置",
    "id": "0xF009",
    "description": [
      "TDD模式：",
      "wholeBandRem字段指示是否开启全频段扫频，若开启全频段扫频(wholeBandRem=1)，板卡在扫完配置的频点后，继续搜索公网小区SIB5中配置的其他邻区频点。",
      "基站单板本身支持的扫频范围是（(50MHZ~4GHz)，，但若整机系统中RX或者SINNIFFER口外接了限制接收频率的硬件（比如滤波器），则配置频点时应仅限于属于该频段的频点，且配置wholeBandRem为0。",
      "\\*注：TDD模式下，空口同步、频偏校准和自配置流程会修改扫频频点配置",
      "FDD模式：",
      "此接口用于客户端在基站IDLE态时开始SCAN公网LTE小区参数的流程。无需配置Band ID，基站会根据频点自动计算。FDD模式只支持用SNF端口扫频，基站默认版本是SNF口。",
      "wholeBandRem字段指示是否开启全频段扫频，若开启全频段扫频(wholeBandRem=1)，板卡在扫完配置的频点后，继续搜索公网小区SIB5中配置的其他邻区频点。",
      "\\*注：FDD模式下，服务小区参数配置和频偏校准流程会修改扫频频点配置"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是。",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_SYS_ARFCN_ACK (0xF004)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头（0xF009）"
        },
        {
          "name": "wholeBandRem",
          "type": "U32",
          "description": "是否开启全频段扫频"
        },
        {
          "name": "sysEarfcnNum",
          "type": "U32",
          "description": "扫频频点数目"
        },
        {
          "name": "sysEarfcn[10]",
          "type": "U32",
          "description": "频点，如38400等"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "REM_ANT_CFG",
      "messageId": "0xF009"
    },
    "response": null
  },
  "扫频端口配置": {
    "name": "扫频端口配置",
    "id": "0xF07D",
    "description": [
      "此接口仅用于配置TDD扫频端口，目前支持RX和SINNIFER 2个端口模式。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_REM_ANT_CFG_ACK(0xF07E)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF07D)"
        },
        {
          "name": "RxorSnf",
          "type": "U32",
          "description": "端口类型"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "REM_ANT_CFG",
      "messageId": "0xF07D"
    },
    "response": null
  },
  "基站重启配置": {
    "name": "基站重启配置",
    "id": "0xF00B",
    "description": [
      "此接口用于客户端指示基站执行reboot操作。基站收到此消息，先回复ACK，再执行reboot。基站处于任何状态都会处理该消息。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "否",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_REBOOT_ACK (0xF00C)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头0xF00B"
        },
        {
          "name": "SelfActiveCfg",
          "type": "U32",
          "description": "指示基站重启后是否采用现有参数配置自动激活小区，该字段只有在定位版本中生效，围栏版本不需要判断该字节。"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "REBOOT_CFG",
      "messageId": "0xF00B"
    },
    "response": null
  },
  "小区激活去激活配置": {
    "name": "小区激活去激活配置",
    "id": "0xF00D",
    "description": [
      "在基站IDLE状态下，可通过此消息指示基站采用当前小区配置参数激活小区，如果workAdminState配置为1，TDD基站则不进行同步流程，直接激活小区，如果workAdminState配置为2；TDD基站先执行同步流程，同步成功后再激活小区，如果同步失败，基站仍然回到IDLE状态。",
      "在基站激活状态下，通过配置workAdminState为0，基站则会执行去激活小区的操作，进入IDLE状态。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "否",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_SET_ADMIN_STATE_ACK(0xF00E)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF00D)"
        },
        {
          "name": "workAdminState",
          "type": "U32",
          "description": "/"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SET_ADMIN_STATE_CFG",
      "messageId": "0xF00D"
    },
    "response": null
  },
  "接收增益配置": {
    "name": "接收增益配置",
    "id": "0xF013",
    "description": [
      "该接口用于配置基站9361寄存器的接收增益，表示将接收到的来自UE的信号放大多少倍。接收增益配置值说明参考《[2.4 接收增益](#_接收增益)》。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "可配置",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_SYS_RxGAIN_ACK(0xF014)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF013)"
        },
        {
          "name": "Rxgain",
          "type": "U32",
          "description": "接收增益"
        },
        {
          "name": "RxGainSaveFlag",
          "type": "U8",
          "description": "配置值是否保存到配置，重启之后也保留"
        },
        {
          "name": "RxOrSnfFlag",
          "type": "U8",
          "description": "配置该增益是修改rx口增益还是snf口增益 注：仅FDD有效 对于TDD，该字段无意义，基站不做判断。"
        },
        {
          "name": "Res[2]",
          "type": "U8",
          "description": "保留字节"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SYS_RxGAIN_CFG",
      "messageId": "0xF013"
    },
    "response": null
  },
  "发射功率衰减配置": {
    "name": "发射功率衰减配置",
    "id": "0xF015",
    "description": [
      "该接口用于配置基站发送通道的衰减值，用于客户校准整机输出功率。衰减值每加4，基站输出功率增加1dB衰减。无衰减时，即衰减值为0x00时，基站输出功率范围在-1dbm~-2dbm，每块单板会有差异。",
      "基站实际输出功率 = 零衰减功率 - 衰减值（Pwr1Derease\\*0.25）",
      "例如：基站输出功率为-1dB，当衰减值设置为0x28，输出功率为-11dBm。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "可配置",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_SYS_PWR1_DEREASE_ACK(0xF016)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF015)"
        },
        {
          "name": "Pwr1Derease",
          "type": "U32",
          "description": "功率衰减，每步长代表0.25dB"
        },
        {
          "name": "IsSave",
          "type": "U8",
          "description": "配置值是否保存到配置，重启之后也保留"
        },
        {
          "name": "Res [3]",
          "type": "U8",
          "description": "保留字节"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SYS_PWR1_DEREASE_CFG",
      "messageId": "0xF015"
    },
    "response": null
  },
  "基站IP配置": {
    "name": "基站IP配置",
    "id": "0xF01B",
    "description": [
      "该接口用于修改基站的IP配置。",
      "版本默认基站地址是“************#*************#***********#”。"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_IP_CFG_ACK (0xF01C)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF01B)"
        },
        {
          "name": "eNBIPStr[52]",
          "type": "U8",
          "description": "设置基站的IP"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "IP_CFG",
      "messageId": "0xF01B"
    },
    "response": null
  },
  "客户端IP配置": {
    "name": "客户端IP配置",
    "id": "0xF025",
    "description": [
      "该接口用于配置客户端IP配置，版本默认客户端地址是“***********1#3345”。",
      "注意：该接口中的端口字段，单板在UDP模式下，也使用该端口值进行监听。",
      "端口默认3345，若更改，建议配置3350---3399."
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_LMTIP_CFG_ACK (0xF026)"
    },
    "request": null,
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF025)"
        },
        {
          "name": "LMTIPStr[32]",
          "type": "U8",
          "description": "设置主控板的IP和端口,字符串，以’\\0’结束"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "LMTIP_CFG_ACK",
      "messageId": "0xF025"
    }
  },
  "GPS同步模式下的pp1s偏移量配置": {
    "name": "GPS同步模式下的pp1s偏移量配置",
    "id": "0xF029",
    "description": [
      "在TDD选择GPS同步模式下，此接口用于设置同步偏移量，在中国，一般band39,band40需要进行GPS同步偏移量调节,一般-700微秒（OffsetTime）左右数据帧头偏移（正值说明时域相对原始值向后移动，负值说明是时域对应原始值向前移动），具体各个BAND的偏移量以实际测量为准。",
      "接口中设置的Gpspps1s = OffsetTime \\* （Gpspps1sToBW/微秒）",
      "OffsetTime：运营商网络此BAND相对于GPS的偏移量，单位微秒；",
      "Gpspps1sToBW/微秒：相关带宽下每微秒的偏移值，带宽是指本基带板的带宽；",
      "1微秒的偏移情况下，Gpspps1s与带宽对应关系如下：",
      "例如：基站配置20M带宽，BAND40偏移-700微妙，则接口中配置的Gpspps1s=-700\\*30.72。"
    ],
    "metadata": {
      "isEffectiveImmediately": "否，需要重新进行GPS同步生效。",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_GPS_PP1S_ACK(0xF02A)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF029)"
        },
        {
          "name": "Gpspps1s",
          "type": "S32",
          "description": "Gps pps1s偏移量"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GPS_PP1S_CFG",
      "messageId": "0xF029"
    },
    "response": null
  },
  "上电自动激活小区配置": {
    "name": "上电自动激活小区配置",
    "id": "0xF03B",
    "description": [
      "上电自激活配置：",
      "用于配置基站上电启动时是否执行自动激活小区的流程，默认版本中上电不自动激活小区，进入IDLE状态。",
      "Reboot配置：",
      "Wl模式下，用于配置除带宽改变时，reboot是否执行自动激活小区的流程，默认版本中reboot自动激活小区，进入active状态。",
      ";"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "SELF_ACTIVE_CFG_PWR_ON_ACK",
      "responseMessageId": "0xF03C"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF03B)"
        },
        {
          "name": "SelfActiveCfg",
          "type": "U32",
          "description": "/"
        },
        {
          "name": "rebootSelfActiveCfg",
          "type": "U32",
          "description": "仅WL版本有效"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SELF_ACTIVE_CFG_PWR_ON",
      "messageId": "0xF03B"
    },
    "response": null
  },
  "TDD子帧配置": {
    "name": "TDD子帧配置",
    "id": "0xF049",
    "description": [
      "此消息接口用于配置TDD小区的子帧配比，小区去激活状态下配置立即生效。"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "TDD_SUBFRAME_ASSIGNMENT_SET_ACK",
      "responseMessageId": "0xF04A"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF049)"
        },
        {
          "name": "u8TddSfAssignment;",
          "type": "U8",
          "description": "TDD子帧配比"
        },
        {
          "name": "u8TddSpecialSfPatterns",
          "type": "U8",
          "description": "TDD特殊子帧配比"
        },
        {
          "name": "Res[2]",
          "type": "U8",
          "description": "保留字节"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "TDD_SUBFRAME_ASSIGNMENT_SET",
      "messageId": "0xF049"
    },
    "response": null
  },
  "GPS经纬度信息复位配置": {
    "name": "GPS经纬度信息复位配置",
    "id": "0xF06D",
    "description": [
      "基站启动时，会自动获取GPS经纬度信息，获取的经纬度信息可通过4.9章节的《[GPS经纬高度查询](#_Gps经纬高度查询)》接口查询，一旦获取到经纬度信息，基站会保存此次获取的值，下次重启将不再重复获取，因此如果基站移动了位置，请使用此接口清除上一次获取的经纬度信息。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "否",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_GPS_INFO_RESET_ACK (0xF06E)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF06D)"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GPS_INFO_RESET",
      "messageId": "0xF06D"
    },
    "response": null
  },
  "辅PLMN列表配置": {
    "name": "辅PLMN列表配置",
    "id": "0xF060",
    "description": [
      "此接口用于配置基站广播SIB1 中PLMN LIST字段中的非主PLMN。"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "SECONDARY_PLMNS_SET_ACK",
      "responseMessageId": "0xF061"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF060)"
        },
        {
          "name": "u8SecPLMNNum",
          "type": "U8",
          "description": "辅PLMN的数目"
        },
        {
          "name": "u8SecPLMNList[5][7]",
          "type": "U8",
          "description": "辅PLMN列表"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SECONDARY_PLMNS_SET",
      "messageId": "0xF060"
    },
    "response": null
  },
  "启动小区自配置请求": {
    "name": "启动小区自配置请求",
    "id": "0xF04F",
    "description": [
      "此接口用于指示基站在IDLE态开始小区自配置流程，小区自配置流程图参考《[小区自配置流程](#_小区自配置流程)》。",
      "基站收到此消息，根据自配置频点列表搜索公网频点和小区信息，基站会根据扫频结果，选择本小区参数自动建立小区。",
      "如果整机设备支持全频段，可以配置SelfBand为0xFF，基站将会对自配置频点列表中所有频点以及公网广播消息SIB5中的频点进行全频段扫频。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "否",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "SELFCFG_CELLPARA_REQ_ACK",
      "responseMessageId": "0xF050"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF04F)"
        },
        {
          "name": "SelfBand",
          "type": "U8",
          "description": "指定自配置的频段"
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": "保留字节"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SELFCFG_CELLPARA_REQ",
      "messageId": "0xF04F"
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头 (0xF050)"
        },
        {
          "name": "CfgResult",
          "type": "U32",
          "description": "配置结果"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "SELFCFG_CELLPARA_REQ_ACK",
      "messageId": "0xF050"
    }
  },
  "基站TDD/FDD同步方式配置": {
    "name": "基站TDD/FDD同步方式配置",
    "id": "0xF023",
    "description": [
      "此接口用于设置基站的同步方式，目前仅支持空口和GPS同步。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_REM_MODE_CFG_ACK (0xF024)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF023)"
        },
        {
          "name": "Remmode",
          "type": "U32",
          "description": "TDD模式支持空口和GPS同步，FDD仅支持GPS，用于频率同步。"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "REM_MODE_CFG",
      "messageId": "0xF023"
    },
    "response": null
  },
  "小区自配置后台频点添加/删除": {
    "name": "小区自配置后台频点添加/删除",
    "id": "0xF051",
    "description": [
      "此接口用于配置小区自配置功能的扫频频点列表。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "SELFCFG_ARFCN_CFG_REQ_ACK",
      "responseMessageId": "0xF052"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF051)"
        },
        {
          "name": "Cfgtype",
          "type": "U32",
          "description": ""
        },
        {
          "name": "EarfcnValue",
          "type": "U32",
          "description": "频点值"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SELFCFG_ARFCN_CFG_REQ",
      "messageId": "0xF051"
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头 (0xF052)"
        },
        {
          "name": "CfgResult",
          "type": "U32",
          "description": "配置结果"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "SELFCFG_ARFCN_CFG_REQ_ACK",
      "messageId": "0xF052"
    }
  },
  "动态修改小区参数": {
    "name": "动态修改小区参数",
    "id": "0xF080",
    "description": [
      "此接口用于在小区激活态下，即时修改小区参数，但是此时修改的小区参数重启或者断电之后不会保存。如果当前小区没有激活，会返回配置失败。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "否",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_SYS_ARFCN_MOD_ACK (0xF081)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头 (0xF080)"
        },
        {
          "name": "ulEarfcn",
          "type": "U32",
          "description": "上行频点"
        },
        {
          "name": "dlEarfcn",
          "type": "U32",
          "description": "下行频点"
        },
        {
          "name": "PLMN[7 ]",
          "type": "U8",
          "description": "plmn"
        },
        {
          "name": "Band",
          "type": "U8",
          "description": "频段"
        },
        {
          "name": "CellId",
          "type": "U32",
          "description": "小区Id"
        },
        {
          "name": "UePMax",
          "type": "U32",
          "description": "终端最大发射功率"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SYS_ARFCN_MOD",
      "messageId": "0xF080"
    },
    "response": null
  },
  "NTP服务器IP配置": {
    "name": "NTP服务器IP配置",
    "id": "0xF075",
    "description": [
      "此接口用于设置基站NTP时间同步的NTP服务器IP，系统启动时会自动进行NTP时间同步。"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_NTP_SERVER_IP_CFG_ACK (0xF076)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": ""
        },
        {
          "name": "ntpServerIp[20]",
          "type": "U8",
          "description": ""
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "NTP_SERVER_IP_CFG",
      "messageId": null
    },
    "response": null
  },
  "定点重启配置": {
    "name": "定点重启配置",
    "id": "0xF086",
    "description": [
      "此接口用于配置基站是否开启定点重启功能，基站系统采用NTP同步方式获取系统格林威治时间，如果开启此功能，请设置正确的NTP服务器IP。",
      "版本发布默认此功能关闭。"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "TIME_TO_RESET_CFG_ACK",
      "responseMessageId": "0xF087"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF086)"
        },
        {
          "name": "ResetSwitch",
          "type": "U8",
          "description": "定点重启开关"
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": "保留字节"
        },
        {
          "name": "ResetTime[12]",
          "type": "S8",
          "description": "重启时间配置 例如：“23：15：15” 格林威治时间"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "TIME_TO_RESET_CFG",
      "messageId": "0xF086"
    },
    "response": null
  },
  "开启IMEI捕获功能配置": {
    "name": "开启IMEI捕获功能配置",
    "id": "0xF08A",
    "description": [
      "此接口用于配置基站是否开启获取UE的IMEI功能，由于IMEI的获取，基站会对接入的UE先释放让其重新接入，会影响高速抓号的成功率，因此版本默认是关闭功能，客户可根据自己的需要决定是否开启此功能。根据测试，获取UE IMEI相对于IMSI的比例大概是10~15%。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "IMEI_REQUEST_CFG_ACK",
      "responseMessageId": "0xF08B"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF08A)"
        },
        {
          "name": "ImeiEnable",
          "type": "U8",
          "description": "是否开启IMEI获取功能"
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": "保留字节"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "IMEI_REQUEST_CFG",
      "messageId": "0xF08A"
    },
    "response": null
  },
  "选频配置": {
    "name": "选频配置",
    "id": "0xF082",
    "description": [
      "基站根据当前激活小区的BAND输出不同的GPIO信号，用于客户一个单板切换不同的BAND建立小区时根据该GPIO信号匹配不同的功放。输出GPIO信号的管脚以及配置描述见下图。目前V2 Board提供2根GPIO管脚，V3 Board以及后面型号的Board提供4根GPIO管脚，配置接口兼容支持，请根据不同的Board配置值范围。",
      "PinBandRelation：",
      "版本默认发布BAND与其PIN脚的关系见下图：",
      "V2单板默认关系：",
      "V3单板默认关系：",
      "V5-C单板默认关系：",
      "V6单板默认关系："
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "SELECT_FREQ_CFG_ACK",
      "responseMessageId": "0xF083"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF082)"
        },
        {
          "name": "PinBandRelaNum",
          "type": "U32",
          "description": "指定结构体数组元素个数。"
        },
        {
          "name": "pinBandRelaMap[15]",
          "type": "PinBandRelation",
          "description": "管脚频带关系表"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SELECT_FREQ_CFG",
      "messageId": "0xF082"
    },
    "response": null
  },
  "IMSI黑白名单配置": {
    "name": "IMSI黑白名单配置",
    "id": "0xF039",
    "description": [
      "此接口用于配置IMSI的黑名单和白名单，每次最大可以配置10个IMSI，基站可同时保存维护黑名单和白名单两套名单，最大支持各100个名单配置。根据测量UE的配置模式决定采用哪个名单。",
      "备注1：",
      "此消息会根据IgnoreUENum数组包，请根据消息头中MsgLen和IgnoreUENum解析数据"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "CONTROL_UE_LIST_CFG_ACK",
      "responseMessageId": "0xF03A"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF039)"
        },
        {
          "name": "ControlMovement",
          "type": "U8",
          "description": ""
        },
        {
          "name": "ControlUENum",
          "type": "U8",
          "description": "添加/删除UE数目"
        },
        {
          "name": "ControlUEProperty",
          "type": "U8",
          "description": ""
        },
        {
          "name": "ControlUEIdentity[10][C_MAX_IMSI_LEN]",
          "type": "U8",
          "description": "UE IMSI数组"
        },
        {
          "name": "ClearType",
          "type": "U8",
          "description": "清除黑白名单配置"
        },
        {
          "name": "RejCause",
          "type": "U8",
          "description": "黑/白名单对应的TAU REJECT原因值（仅对重定向模式子模式0生效，名单内外可以使用不同的RejCause）"
        },
        {
          "name": "Res",
          "type": "U8",
          "description": "保留字节"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "CONTROL_UE_LIST_CFG",
      "messageId": "0xF039"
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0x F03A)"
        },
        {
          "name": "CfgResult",
          "type": "U8",
          "description": "配置结果"
        },
        {
          "name": "IgnoreUENum",
          "type": "U8",
          "description": "未操作成功UE数目"
        },
        {
          "name": "IgnoreUEList[10][C_MAX_IMSI_LEN]",
          "type": "U8",
          "description": "UE IMSI数组"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "CONTROL_UE_LIST_CFG_ACK",
      "messageId": null
    }
  },
  "UE NAS REJECT CAUSE配置": {
    "name": "UE NAS REJECT CAUSE配置",
    "id": "0xF057",
    "description": [
      "此接口用于配置基站把接入UE踢回公网时，回复UE的TAU REJECT或者ATTACH REJECT消息中的reject cause值，基站默认使用cause#15，一般此值不需要修改。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "TAU_ATTACH_REJECT_CAUSE_CFG_ACK",
      "responseMessageId": "0xF058"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF057)"
        },
        {
          "name": "RejectCause",
          "type": "U32",
          "description": "回复UE的TAU REJECT或者ATTACH REJECT消息中的reject cause值"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "TAU_ATTACH_REJECT_CAUSE_CFG",
      "messageId": "0xF057"
    },
    "response": null
  },
  "UE重定向信息配置": {
    "name": "UE重定向信息配置",
    "id": "0xF017",
    "description": [
      "此接口用于配置基站发送给UE的释放消息中是否携带重定向参数，默认不携带重定向参数。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_REDIRECT_INFO_ACK (0xF018)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF017)"
        },
        {
          "name": "OnOff",
          "type": "U32",
          "description": "重定向开关"
        },
        {
          "name": "Earfcn",
          "type": "U32",
          "description": "重定向频点"
        },
        {
          "name": "RedirectType",
          "type": "U32",
          "description": "重定向类型"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "REDIRECT_INFO_CFG",
      "messageId": "0xF017"
    },
    "response": null
  },
  "系统模式（TDD/FDD）配置": {
    "name": "系统模式（TDD/FDD）配置",
    "id": "0xF001",
    "description": [
      "此消息用于设置系统模式（TDD/FDD），基站收到此配置，记录启动模式，",
      "客户端下发reboot指令或者重新上下电给基站，基站会根据配置的模式启动系统。",
      "当sysMode=2时，系统根据硬件一个GPIO（PIN5）管脚的输入信号决定启动TDD还是FDD，PINI5悬空时启动TDD，PIN5接地时启动FDD，仅V3系列板卡支持此功能。",
      "仅TDD和FDD共版版本才支持此设置，非共版版本基站会回复失败。",
      "PIN5位置图如下：",
      "![](data:image/png;base64...)"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_SYS_MODE_ACK(0xF002)"
    },
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SYS_MODE_CFG",
      "messageId": null
    },
    "response": null
  },
  "基站版本升级配置": {
    "name": "基站版本升级配置",
    "id": "0xF06F",
    "description": [
      "此接口用于客户端配置升级基站版本使用，需要客户端建立FTP服务器。",
      "UpgradeStatusId:"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "否",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "UPDATE_SOFT_VERSION_CFG_ACK",
      "responseMessageId": "0x070"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF06F)"
        },
        {
          "name": "updateType",
          "type": "U8",
          "description": "升级版本类型"
        },
        {
          "name": "enbSoftFileName[102]",
          "type": "U8",
          "description": "字符串，基站软件版本名字：如 “BaiStation128D_ FDD_R002C0000G01B005.IMG”；"
        },
        {
          "name": "isReservedCfg",
          "type": "U8",
          "description": "是否保留配置（仅对基站软件系统有效）"
        },
        {
          "name": "enbSoftMD5 [36]",
          "type": "U8",
          "description": "针对升级基站软件计算的md5值,32字节长度。"
        },
        {
          "name": "uBootFileName[40]",
          "type": "U8",
          "description": "该字段为boot文件名，如：“u-boot-t2200-nand-1.0.15.img”"
        },
        {
          "name": "ubootMD5 [36]",
          "type": "U8",
          "description": "针对升级文件计算的md5值,32字节长度。"
        },
        {
          "name": "isCfgFtpServer",
          "type": "U8",
          "description": "是否重新配置FTP服务器地址"
        },
        {
          "name": "FtpServerIp[16]",
          "type": "U8",
          "description": "FTP服务器IP, eg: “***********1”，"
        },
        {
          "name": "Reserved[3]",
          "type": "U8",
          "description": "保留字节"
        },
        {
          "name": "FtpServerPort",
          "type": "U32",
          "description": "FTP服务器端口号，eg: 21"
        },
        {
          "name": "FtpLoginNam[20]",
          "type": "U8",
          "description": "Ftp用户名，eg:“kkk ”"
        },
        {
          "name": "FtpPassword[10]",
          "type": "U8",
          "description": "Ftp登录密码, eg: “123456 ”"
        },
        {
          "name": "FtpServerFilePath[66]",
          "type": "U8",
          "description": "待升级文件所在FTP服务器路径，默认根目录。路径以/结尾。 eg：待升级文件位于FTP服务器根目录下的filePath文件夹，完整的路径为：“/filePath/”"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "UPDATE_SOFT_VERSION_CFG",
      "messageId": "0xF06F"
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF070)"
        },
        {
          "name": "VerType",
          "type": "U8",
          "description": "指示升级的版本类型"
        },
        {
          "name": "StatusInd",
          "type": "U8",
          "description": "指示执行状态(UpgradeStatusId)"
        },
        {
          "name": "Reserved[2]",
          "type": "U8",
          "description": "预留"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "UPDATE_SOFT_VERSION_CFG_ACK",
      "messageId": "0xF070"
    }
  },
  "上传IMSI文件配置": {
    "name": "上传IMSI文件配置",
    "id": "0xF077",
    "description": [
      "此接口用于开启基站支持以IMSI文件上传的方式上报到客户端，使用FTP方式。",
      "uploadImsiFileCfg:"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "UPLOAD_IMSI_FILE_CFG_ACK",
      "responseMessageId": "0xF078"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头定义(0xF077)"
        },
        {
          "name": "UploadImsiFileCfg",
          "type": "uploadImsiFileCfg",
          "description": ""
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "UPLOAD_IMSI_FILE_CFG",
      "messageId": "0xF077"
    },
    "response": null
  },
  "UE重定向模式动态黑名单配置": {
    "name": "UE重定向模式动态黑名单配置",
    "id": "0xF08E",
    "description": [
      "本接口用于重定向模式下，配置IMSI黑名单，单次配置最多20，累次配置总数达到1000，删除时间节点配置靠前的IMSI。",
      "ClearImsiListFlag取1并且AddImsiNum非零同时配置，先清空列表再添加。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "否",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "UE_REDIRECT_IMSI_LIST_CFG_ACK",
      "responseMessageId": "0xF08F"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头定义(0xF08E)"
        },
        {
          "name": "ClearImsiListFlag",
          "type": "U8",
          "description": "0:保留当前所有配置 1:清空所有IMSI配置列表"
        },
        {
          "name": "AddImsiNum",
          "type": "U8",
          "description": "增加的IMSI条数;"
        },
        {
          "name": "ImsiStr[C_MAX_UE_REDIRECT_IMSI_ADD_NUM][C_MAX_IMSI_LEN]",
          "type": "U8",
          "description": "字符串数组，如： \"460011111111111\" 非有效UE ID位注意置为'\\0'。 (该字段为固定长度)"
        },
        {
          "name": "Res[2]",
          "type": "U8",
          "description": "预留"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "UE_REDIRECT_IMSI_LIST_CFG",
      "messageId": "0xF08E"
    },
    "response": null
  },
  "FDD GPS同步不成功重新同步配置": {
    "name": "FDD GPS同步不成功重新同步配置",
    "id": "0xF090",
    "description": [
      "该接口用于FDD 模式下（FDD的GPS同步功能只用于校准频偏，小区激活态下有效，同小区激活顺序无关）：",
      "此消息接口用于当gps同步不成功时，重新配置gps去做同步，不需要重新建立小区。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "否",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "FDD_GPS_RESYNC_CFG_ACK",
      "responseMessageId": "0xF091"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头定义(0xF090)"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "REM_MODE_CFG",
      "messageId": "0xF090"
    },
    "response": null
  },
  "上行功控Alpha系数配置": {
    "name": "上行功控Alpha系数配置",
    "id": "0xF092",
    "description": [
      "此消息接口用于当FDD开启配置TDD/FDD小区的SIB2中的上行功控系数，小区去激活状态下配置立即生效。",
      "本接口配置TDD出厂配置默认值为70，FDD默认出厂值为80。在空旷地带测试抓号，建议该值配置成80。",
      "注：配置的（0，40，50，60，70，80，90，100）分别对应sib2信息alpha值的（0，1，2，3，4，5，6，7）。"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "UL_POWER_CONTROL_ALPHA_CFG_ACK",
      "responseMessageId": "0xF093"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头定义(0xF092)"
        },
        {
          "name": "UlPowerAlpha",
          "type": "U8",
          "description": "Sib2中上行功控系数"
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": "预留"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "UL_POWER_CONTROL_ALPHA_CFG",
      "messageId": "0xF092"
    },
    "response": null
  },
  "GPS芯片选择gps或北斗配置": {
    "name": "GPS芯片选择gps或北斗配置",
    "id": "0xF097",
    "description": [
      "此消息接口用于配置gps芯片选择gps或者北斗，配置完重启生效。"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "GPS_OR_BEIDOU_CFG_ACK",
      "responseMessageId": "0xF098"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF097)"
        },
        {
          "name": "u8FlagInUse",
          "type": "U8",
          "description": ""
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": "保留字节"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GPS_OR_BEIDOU_CFG",
      "messageId": "0xF097"
    },
    "response": null
  },
  "单板管脚电平输出控制": {
    "name": "单板管脚电平输出控制",
    "id": "0xF09F",
    "description": [
      "此接口用于控制V3单板PIN1管脚的电平输出，V5板IO6管脚输出控制，以及V6板IO5管脚输出控制。",
      "单板自主判断当前板卡类型，若是V3则该接口控制PIN1输出，若是V5板卡，控制IO6电平输出，若是V6板卡，控制IO5电平输出。",
      "V3对应板卡上扩展IO1里的IO1（J7-IO1）；V5对应板卡上IO6；V6对应板卡上IO5。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "否",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_PINX_SWITCH_CFG_ACK (0xF0A0)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF09F)"
        },
        {
          "name": "PinxSwitch",
          "type": "U8",
          "description": "0: 输出低电平 1：输出高电平"
        },
        {
          "name": "Reserved[3]",
          "type": "U8",
          "description": "预留"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "PINX_SWITCH_CFG",
      "messageId": "0xF09F"
    },
    "response": null
  },
  "Band功率衰减关系表配置": {
    "name": "Band功率衰减关系表配置",
    "id": "0xF0A7",
    "description": [
      "该接口用于配置不同band的小区时，基站根据band，查询关系表，配置衰减值。",
      "出厂默认关闭该功能。",
      "bandPwrdereaseMap"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "MULTI_BAND_POWERDEREASE_ACK",
      "responseMessageId": "0xF0A8"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0A7)"
        },
        {
          "name": "NumElem",
          "type": "U8",
          "description": "0: 关闭功能 >0:配置相关关系及关系个数"
        },
        {
          "name": "Reserved[3]",
          "type": "U8",
          "description": "预留"
        },
        {
          "name": "BandPwrdereaseMap[32]",
          "type": "bandPwrdereaseMap",
          "description": "Band和衰减值对应关系"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "MULTI_BAND_POWERDEREASE_CFG",
      "messageId": "0xF0A7"
    },
    "response": {
      "parameters": [],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "SYS_PWR1_DEREASE",
      "messageId": null
    }
  },
  "Band接收增益关系表配置": {
    "name": "Band接收增益关系表配置",
    "id": "0xF0C8",
    "description": [
      "该接口用于配置不同band的小区时，基站根据band，查询关系表，配置接收增益值。",
      "不支持增量配置。若建小区的band无法在表中查找到对应接收增益值，默认输出接收增益值0xFF。",
      "出厂默认关闭该功能。",
      "bandRxgainMap"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "MULTI_BAND_RXGAIN_CFG_ACK",
      "responseMessageId": "0xF0C9"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0C8)"
        },
        {
          "name": "NumElem",
          "type": "U8",
          "description": "0: 关闭功能 >0:配置相关关系及关系个数"
        },
        {
          "name": "Reserved[3]",
          "type": "U8",
          "description": "预留"
        },
        {
          "name": "BandRxgainMap[32]",
          "type": "bandRxgainMap",
          "description": "Band和增益值对应关系"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "MULTI_BAND_RXGAIN_CFG",
      "messageId": "0xF0C8"
    },
    "response": null
  },
  "异频同步接口配置": {
    "name": "异频同步接口配置",
    "id": "0xF05E",
    "description": [],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是（SNF端口和时偏保留，频点有场景保留有场景不保留）",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "SYNCARFCN_CFG_ACK",
      "responseMessageId": "0xF05F"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF05E)"
        },
        {
          "name": "UsingSnfSwitch",
          "type": "U8",
          "description": "0: 使用Rx同步 1：使用SNF同步 2: 取消异频同步"
        },
        {
          "name": "Reserved1[3]",
          "type": "U8",
          "description": "保留字节"
        },
        {
          "name": "SyncArfcn",
          "type": "U32",
          "description": "同步频点"
        },
        {
          "name": "TimeOffsetPresent",
          "type": "U8",
          "description": "0：不需要配置 1：需要配置"
        },
        {
          "name": "Reserved2[3]",
          "type": "U8",
          "description": "保留字节"
        },
        {
          "name": "S32",
          "type": "TimeOffsetValue",
          "description": "异频同步时偏值（不同band间）"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SYS_ARFCN_CFG",
      "messageId": "0xF05E"
    },
    "response": null
  },
  "MSG4功率抬升配置": {
    "name": "MSG4功率抬升配置",
    "id": "0xF0AF",
    "description": [
      "此接口用于配置MSG4功率是否抬升，TDD生效。默认出厂设置为不抬升，在不同环境下测试时，如出现接入成功率不理想，可以配置抬升MSG4功率。"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMB_MSG4_POWER_BOOST_CFG_ACK (0xF0B0)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0AF)"
        },
        {
          "name": "Msg4PowerBoost",
          "type": "U32",
          "description": "0：不抬升（出厂默认值） 1：抬升"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "MSG4_POWER_BOOST_CFG",
      "messageId": "0xF0AF"
    },
    "response": null
  },
  "GPS固件彻底复位配置": {
    "name": "GPS固件彻底复位配置",
    "id": "0xF0D2",
    "description": [
      "此接口主要用于当gps信号很好的场景下反复无法同步成功，搜不到星的场景，可以尝试将GPS固件彻底复位，复位时间比较长，等收到响应后，重启生效。"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_LMT_TO_ENB_GPS_SOFTWARE_RENOVATE_CFG_ACK(0xF0D3)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0D2)"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GPS_SOFTWARE_RENOVATE_CFG",
      "messageId": "0xF0D2"
    },
    "response": null
  },
  "QRxLevMin配置": {
    "name": "QRxLevMin配置",
    "id": "0xF021",
    "description": [],
    "metadata": {
      "isEffectiveImmediately": "重启生效",
      "persistsOnReboot": "是"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF021)"
        },
        {
          "name": "s8QrxlevMin",
          "type": "S8",
          "description": ""
        },
        {
          "name": "Reserved1[3]",
          "type": "U8",
          "description": "保留字节"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SET_QRXLEVMIN",
      "messageId": "0xF021"
    },
    "response": {
      "parameters": [],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "SET_QRXLEVMIN_ACK",
      "messageId": null
    }
  },
  "设置单板时间": {
    "name": "设置单板时间",
    "id": "0xF01F",
    "description": [],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "否",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_SET_SYS_TMR_ACK (0xF020)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF01F)"
        },
        {
          "name": "Time[20]",
          "type": "U8",
          "description": "单板系统时间设置"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SET_SYS_TMR",
      "messageId": "0xF01F"
    },
    "response": null
  },
  "设置同步失败后激活小区方式": {
    "name": "设置同步失败后激活小区方式",
    "id": "0xF0CC",
    "description": [],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_BUILD_CELL_CFG_ACK (0xF0CD)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0CC)"
        },
        {
          "name": "buildFlag",
          "type": "U32",
          "description": "0: 同步失败不建小区； 1：同步失败强制激活小区。（GPS同步失败1次，空口同步失败3次以上）"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "BUILD_CELL_CFG",
      "messageId": "0xF0CC"
    },
    "response": null
  },
  "设置GPS失步后重启时间": {
    "name": "设置GPS失步后重启时间",
    "id": "0xF0CE",
    "description": [],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "GPS_LOSS_REBOOT_TMR_CFG_ACK",
      "responseMessageId": "0xF0CF"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0CE)"
        },
        {
          "name": "value",
          "type": "U32",
          "description": "单位：分钟"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GPS_LOSS_REBOOT_TMR_CFG",
      "messageId": "0xF0CE"
    },
    "response": null
  },
  "GPS有源/无源设置": {
    "name": "GPS有源/无源设置",
    "id": "0xF0C6",
    "description": [],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "否",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_GPS_SRC_SEL_ACK (0xF0C7)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0C6)"
        },
        {
          "name": "gpsSel",
          "type": "U8",
          "description": "0:无源 1:有源 默认有源"
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": ""
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GPS_SRC_SEL",
      "messageId": "0xF0C6"
    },
    "response": null
  },
  "FTP 上传下载配置": {
    "name": "FTP 上传下载配置",
    "id": "0xF0D0",
    "description": [
      "使用该接口，可以通过FTP下发管控黑白名单，黑名单文件命名格式black.txt,白名单文件名格式white.txt,文件中每个imis一行，文件中不要有空行，最多支持100000个imsi。",
      "isCfgFtp:",
      "0:FTP相关配置仅本次下发有效，重启后不生效",
      "1:FTP相关配置保存在本地，下次不需要再次配置",
      "actionType：",
      "1:通过FTP配置管控黑白名单",
      "2:通过FTP上传当前黑白名单配置文件",
      "ftpGetPutCfg："
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_FTP_GET_PUT_CFG_ACK (0xF0D1)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0D0)"
        },
        {
          "name": "ftpCfg",
          "type": "ftpGetPutCfg",
          "description": "上传下载相关配置"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "FTP_GET_PUT_CFG",
      "messageId": "0xF0D0"
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0D1)"
        },
        {
          "name": "resultNum",
          "type": "U32",
          "description": "操作错误码： 0：下载操作参数错误 1：下载操作开始传输 2：下载操作传输成功 3：下载操作传输失败 4：上传操作参数错误 5：上传操作开始传输 6：上传操作传输成功 7：上传操作传输失败 8：磁盘空间不足"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "FTP_GET_PUT_CFG_ACK",
      "messageId": "0xF0D1"
    }
  },
  "异频频点列表配置": {
    "name": "异频频点列表配置",
    "id": "0xF0E2",
    "description": [
      "此接口用于添加小区SIB5中异频信息，填写下行频点号。"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_EUTRA_FREQ_CFG_ACK (0xF0E3)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0E2)"
        },
        {
          "name": "EutraArfcnNumber",
          "type": "U32",
          "description": "配置下行频点数量"
        },
        {
          "name": "EutraArfcn[C_MAX_EUTRA_ARFCN]",
          "type": "U32",
          "description": ""
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "EUTRA_FREQ_CFG",
      "messageId": "0xF0E2"
    },
    "response": null
  },
  "频偏校准开关配置": {
    "name": "频偏校准开关配置",
    "id": "0xF0DA",
    "description": [
      "基站收到此条消息，设置校准开关打开，重启基站，基站重启以后会开始频偏校准过程。",
      "频偏校准过程相关流程如下：",
      "然后客户端下发频偏校准开关（0xF0DA）,打开频偏校准功能；",
      "基站收到0xF0DA消息会上报状态：“频偏校准开始”（0xF019），然后重启基站。",
      "基站重启以后则开始频偏校准流程，上报状态：“频偏校准进行中”（0xF019）。校准过程中会有扫频状态和扫频结果上报。",
      "频偏校准结束，上报状态：“频偏校准结束”（0xF019），同时上报频偏校准结果（0xF0DC），复位校准开关。",
      "校准结束以后，需要客户端下发重启基站的指令，基站重启以后才能进行正常工作的状态。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0DA)"
        },
        {
          "name": "FreqOffsetSwitch",
          "type": "U8",
          "description": "0:关闭 1：打开"
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": "保留字节"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SELFCFG_ARFCN_CFG_REQ",
      "messageId": "0xF0DA"
    },
    "response": {
      "parameters": [],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "FREQ_OFFSET_ADJ_CFG_ACK",
      "messageId": null
    }
  },
  "频偏配置": {
    "name": "频偏配置",
    "id": "0xF059",
    "description": [
      "设置频偏。"
    ],
    "metadata": {
      "isEffectiveImmediately": "重启生效",
      "persistsOnReboot": "是"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF059)"
        },
        {
          "name": "FreqOffset",
          "type": "U32",
          "description": "频偏"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "FREQ_OFFSET_CFG",
      "messageId": "0xF059"
    },
    "response": {
      "parameters": [],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "FREQ_OFFSET_CFG_ACK",
      "messageId": null
    }
  },
  "AGC配置": {
    "name": "AGC配置",
    "id": "0xF079",
    "description": [
      "FDD有效。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF079)"
        },
        {
          "name": "AgcFlag",
          "type": "U32",
          "description": "是否开启AGC"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "AGC_SET",
      "messageId": "0xF079"
    },
    "response": {
      "parameters": [],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "AGC_SET_ACK",
      "messageId": null
    }
  },
  "IMSI加密密钥配置": {
    "name": "IMSI加密密钥配置",
    "id": "0xF08C",
    "description": [],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF08C)"
        },
        {
          "name": "ImsiKenc[16]",
          "type": "U8",
          "description": "IMSI加密密钥"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "IMSI_TRANS_KENC_CFG_ACK",
      "messageId": "0xF08C"
    },
    "response": {
      "parameters": [],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "IMSI_TRANS_KENC_CFG_ACK",
      "messageId": null
    }
  },
  "轮循载波信息配置": {
    "name": "轮循载波信息配置",
    "id": "0xF0F0",
    "description": [
      "wrRollCarrierCfg:"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0F0)"
        },
        {
          "name": "rollCarrierCfgNum",
          "type": "U8",
          "description": "需要配置的轮循载波数，0表示无载波配置"
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": "预留"
        },
        {
          "name": "stRollCarrierCfg[8]",
          "type": "wrRollCarrierCfg",
          "description": "手动轮循配置参数"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "ROLL_CARRIER_CFG",
      "messageId": "0xF0F0"
    },
    "response": {
      "parameters": [],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "ROLL_CARRIER_CFG_ACK",
      "messageId": null
    }
  },
  "发射功率衰减偏移配置": {
    "name": "发射功率衰减偏移配置",
    "id": "0xF0F4",
    "description": [],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0F4)"
        },
        {
          "name": "pwr1Derease",
          "type": "U8",
          "description": "配置0则为F015配置与F0A7生效值，配置1则在上面两条消息的基础上-1Db,2则为-2Db,19则为-19Db,20则输出功率为最小255"
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": "预留"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "PWR1_DEREASE_CFG",
      "messageId": "0xF0F4"
    },
    "response": {
      "parameters": [],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "PWR1_DEREASE_CFG_ACK",
      "messageId": null
    }
  },
  "Fdd共建站重定向信息配置": {
    "name": "Fdd共建站重定向信息配置",
    "id": "0xF0E6",
    "description": [],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_FDD_REDIRECTION_CFG_ACK(0xF0E7)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0E6)"
        },
        {
          "name": "OnOff",
          "type": "U32",
          "description": "重定向开关"
        },
        {
          "name": "UnicomEarfcn",
          "type": "U32",
          "description": "4G联通重定向频点"
        },
        {
          "name": "TelecomEarfcn",
          "type": "U32",
          "description": "4G电信重定向频点"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "REDIRECT_INFO_CFG",
      "messageId": "0xF0E6"
    },
    "response": null
  },
  "采集用户信息上报": {
    "name": "采集用户信息上报",
    "id": "0xF005",
    "description": [
      "小区激活以后，基站采集接入用户信息并立即上报给客户端，只有开启IMEI捕获功能的时，才会上报IMEI。",
      "主控板用户接口：",
      "围栏版本：",
      "非主控板用户接口——围栏版本：",
      "说明：对于4.7.28中的配置，按照UploadImsiType取1要求并且非主控模式才进行序列号分配，才进行ACK回复。",
      "非主控板用户ACK接口："
    ],
    "metadata": {},
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF096)"
        },
        {
          "name": "seqNum",
          "type": "U32",
          "description": "序列号：等于O_FL_ENB_TO_LMT_UE_INFO_RPT消息中seqNum字段"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "UE_INFO_RPT_ACK",
      "messageId": "0xF096"
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF005)"
        },
        {
          "name": "UeIdType",
          "type": "U32",
          "description": "UE ID类型"
        },
        {
          "name": "IMSI[C_MAX_IMSI_LEN ]",
          "type": "U8",
          "description": "IMSI"
        },
        {
          "name": "IMEI[C_MAX_IMEI_LEN ]",
          "type": "U8",
          "description": "IMEI"
        },
        {
          "name": "RSSI",
          "type": "U8",
          "description": "采集用户的RSSI"
        },
        {
          "name": "STMSIPresent",
          "type": "U8",
          "description": "S-TMSI是否有效"
        },
        {
          "name": "S-TMSI[5]",
          "type": "U8",
          "description": "采集用户的S-TMSI"
        },
        {
          "name": "ConnectedUeNum",
          "type": "U8",
          "description": "当前在线的用户数"
        },
        {
          "name": "TimingAdv",
          "type": "U16",
          "description": "时间提前量"
        },
        {
          "name": "UeAccessType",
          "type": "U8",
          "description": "UE接入类型"
        },
        {
          "name": "Res2[3]",
          "type": "U8",
          "description": "保留字节"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "UE_INFO_RPT",
      "messageId": "0xF005"
    }
  },
  "扫频/同步小区信息上报": {
    "name": "扫频/同步小区信息上报",
    "id": "0xF00A",
    "description": [
      "TDD同步过程中，通过此消息上报尝试同步的小区信息(collectionTypeFlag=1)。",
      "扫频完成后，通过此消息上报扫频的结果信息(collectionTypeFlag=0)。",
      "由于此消息体长度比较大，对于数组的信息，基站会按照实际Num填充上报信息，请客户端解析时，根据相应数组的Num解析数组信息。",
      "常量：",
      "wrFLCollectionCellInfo：",
      "wrFLCellInfo：",
      "wrIntraFreqNeighCellInfo：",
      "stFlLteIntreFreqLst:",
      "wrFLInterNeighCellInfo："
    ],
    "metadata": {},
    "request": null,
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头0xF00A"
        },
        {
          "name": "collectionCellNum",
          "type": "U16",
          "description": "采集的小区数目"
        },
        {
          "name": "collectionTypeFlag",
          "type": "U16",
          "description": "扫频信息标识/同步信息标识"
        },
        {
          "name": "stCollCellInfo  [C_MAX_COLLTECTION_INTRA_CELL_NUM]",
          "type": "wrFLCollectionCellInfo",
          "description": "小区信息"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "REM_INFO_RPT",
      "messageId": "0xF00A"
    }
  },
  "基站执行状态实时上报": {
    "name": "基站执行状态实时上报",
    "id": "0xF019",
    "description": [
      "此消息用于基站实时上报操作流程的执行结果，具体信息参见上报值。",
      "WR_FL_ENB_STATE:"
    ],
    "metadata": {},
    "request": null,
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF019)"
        },
        {
          "name": "CellStateInd",
          "type": "U32",
          "description": "wrFLEnbToLmtEnbStateInd"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "ENB_STATE_IND",
      "messageId": "0xF019"
    }
  },
  "告警指示上报": {
    "name": "告警指示上报",
    "id": "0xF05B",
    "description": [
      "此消息用于基站上报一些运行异常的告警信息，目前支持的告警包括失步、高低温（基带板温度）告警。"
    ],
    "metadata": {},
    "request": null,
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF05B)"
        },
        {
          "name": "AlarmingType",
          "type": "U32",
          "description": "0：基带板高温告警>=70度 1：失步告警 5：基带板低温告警<=-20"
        },
        {
          "name": "AlarmingFlag",
          "type": "U32",
          "description": "0：产生告警指示 1：取消告警指示"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "ALARMING_TYPE_IND",
      "messageId": "0xF05B"
    }
  },
  "频偏校准结果上报": {
    "name": "频偏校准结果上报",
    "id": "0xF0DC",
    "description": [
      "频偏校准结果上报。"
    ],
    "metadata": {},
    "request": null,
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0DC)"
        },
        {
          "name": "FreqOffsetAdjResult",
          "type": "U8",
          "description": "0:成功 1：失败"
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": "保留字节"
        },
        {
          "name": "FreqOffsetValue",
          "type": "S32",
          "description": "仅FreqOffsetAdjResult为0时有效，代表当前单板的频偏值，频偏校准精度为绝对值200以内。"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "FRQ_OFFSET_ADJ_RESULT_IND",
      "messageId": "0xF0DC"
    }
  },
  "自配置结果上报": {
    "name": "自配置结果上报",
    "id": "0xF064",
    "description": [
      "自配置结果上报，TDD有效。"
    ],
    "metadata": {},
    "request": null,
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF064)"
        },
        {
          "name": "dlAfrcn",
          "type": "U32",
          "description": "下行频点"
        },
        {
          "name": "pci",
          "type": "U16",
          "description": "PCI"
        },
        {
          "name": "Tac",
          "type": "U16",
          "description": "TAC"
        },
        {
          "name": "sfassign",
          "type": "U16",
          "description": "TDD子帧配置值"
        },
        {
          "name": "specsfassign",
          "type": "U16",
          "description": "TDD特殊子帧配置值"
        },
        {
          "name": "cellid",
          "type": "U32",
          "description": "Cell ID"
        },
        {
          "name": "Bandwidth",
          "type": "U8",
          "description": "带宽"
        },
        {
          "name": "sysBand",
          "type": "U8",
          "description": "频段"
        },
        {
          "name": "Plmn[6]",
          "type": "U8",
          "description": "PLMN"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "SELFCFG_PARA_RPT",
      "messageId": "0xF064"
    }
  },
  "基站基本信息查询": {
    "name": "基站基本信息查询",
    "id": "0xF02B",
    "description": [
      "此消息用于客户端查询一些基站的基本信息，比如版本号，MAC地址，SN等。"
    ],
    "metadata": {},
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF02B)"
        },
        {
          "name": "u32EnbBaseInfoType",
          "type": "U32",
          "description": "查询信息的类型，"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "BASE_INFO_QUERY",
      "messageId": "0xF02B"
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF02C)"
        },
        {
          "name": "u32EnbBaseInfoType",
          "type": "U32",
          "description": "查询信息的类型，"
        },
        {
          "name": "u8EnbbaseInfo[100]",
          "type": "U8",
          "description": "信息上报"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "BASE_INFO_QUERY_ACK",
      "messageId": "0xF02C"
    }
  },
  "服务小区配置参数查询": {
    "name": "服务小区配置参数查询",
    "id": "0xF027",
    "description": [],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GET_ARFCN",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头 (0xF028)"
        },
        {
          "name": "stServingCellCfgInfo",
          "type": "结构体",
          "description": "小区信息配置"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "ARFCN_IND",
      "messageId": "0xF028"
    }
  },
  "基站同步信息查询": {
    "name": "基站同步信息查询",
    "id": "0xF02D",
    "description": [
      "此消息用于客户端查询基站当前的同步方式和同步状态。"
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SYNC_INFO_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF02E)"
        },
        {
          "name": "u16SyncMode",
          "type": "U16",
          "description": "同步类型"
        },
        {
          "name": "u16SyncState",
          "type": "U16",
          "description": "同步状态"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "SYNC_INFO_QUERY_ACK",
      "messageId": "0xF02E"
    }
  },
  "小区状态信息查询": {
    "name": "小区状态信息查询",
    "id": "0xF02F",
    "description": [
      "应答消息（eNB->LMT）：",
      "此消息用于查询基站的小区状态信息。"
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "CELL_STATE_INFO_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF030)"
        },
        {
          "name": "u32CellState",
          "type": "U32",
          "description": "小区状态"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "CELL_STATE_INFO_QUERY_ACK",
      "messageId": "0xF030"
    }
  },
  "接收增益和发射功率查询": {
    "name": "接收增益和发射功率查询",
    "id": "0xF031",
    "description": [
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "RXGAIN_POWER_DEREASE_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF032)"
        },
        {
          "name": "u8RxGainValueFromReg",
          "type": "U8",
          "description": "寄存器中的值，实际生效的值（FDD模式下仅在建立完小区查询，该值有效）"
        },
        {
          "name": "u8RxGainValueFromMib",
          "type": "U8",
          "description": "数据库中的保存值，重启保留生效的值,"
        },
        {
          "name": "u8PowerDereaseValueFromReg",
          "type": "U8",
          "description": "寄存器中的值，实际生效的值（FDD模式下仅在建立完小区查询，该值有效）"
        },
        {
          "name": "u8PowerDereaseValueFromMib",
          "type": "U8",
          "description": "数据库中的保存值，重启保留生效的值"
        },
        {
          "name": "u8AgcFlag",
          "type": "U8",
          "description": "FDD AGC开关"
        },
        {
          "name": "u8SnfRxGainValueFromReg",
          "type": "U8",
          "description": "只在FDD模式下有效，寄存器中的值，实际生效的值,该值只有在扫频完成后，建立小区前查询有效"
        },
        {
          "name": "u8SnfRxGainValueFromMib",
          "type": "U8",
          "description": "eeprom中的保存值，重启保留生效的值"
        },
        {
          "name": "Res[1]",
          "type": "U8",
          "description": "保留字段"
        },
        {
          "name": "pwrDecreDelta",
          "type": "S32",
          "description": "u8PowerDereaseValueFromReg = u8PowerDereaseValueFromMib +4\\*delta， 仅DW版本有效"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "RXGAIN_POWER_DEREASE_QUERY_ACK",
      "messageId": "0xF032"
    }
  },
  "重定向配置查询": {
    "name": "重定向配置查询",
    "id": "0xF03F",
    "description": [
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "REDIRECT_INFO_CFG_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF040)"
        },
        {
          "name": "OnOff",
          "type": "U32",
          "description": "重定向开关"
        },
        {
          "name": "Earfcn",
          "type": "U32",
          "description": "重定向频点"
        },
        {
          "name": "RedirectType",
          "type": "U32",
          "description": "重定向类型"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "REDIRECT_INFO_CFG_QUERY_ACK",
      "messageId": "0xF040"
    }
  },
  "上电小区自激活配置查询": {
    "name": "上电小区自激活配置查询",
    "id": "0xF041",
    "description": [
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SELF_ACTIVE_CFG_PWR_ON_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF042)"
        },
        {
          "name": "SelfActiveCfg",
          "type": "U32",
          "description": "基站上电是否采用当前配置自动激活小区"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "SELF_ACTIVE_CFG_PWR_ON_QUERY_ACK",
      "messageId": "0xF042"
    }
  },
  "TDD子帧配置和上行功控系数查询": {
    "name": "TDD子帧配置和上行功控系数查询",
    "id": "0xF04B",
    "description": [
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "TDD_SUBFRAME_ASSIGNMENT_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF04C)"
        },
        {
          "name": "u8TddSfAssignment;",
          "type": "U8",
          "description": "TDD子帧配比（fdd该值为255）"
        },
        {
          "name": "u8TddSpecialSfPatterns",
          "type": "U8",
          "description": "TDD特殊子帧配比（fdd该值为255）"
        },
        {
          "name": "u8UlAlpha",
          "type": "U8",
          "description": "Sib2中上行功控系数"
        },
        {
          "name": "Res[1]",
          "type": "U8",
          "description": "保留字节"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "TDD_SUBFRAME_ASSIGNMENT_QUERY_ACK",
      "messageId": "0xF04C"
    }
  },
  "GPS经纬高度查询": {
    "name": "GPS经纬高度查询",
    "id": "0xF05C",
    "description": [
      "基站启动时，会自动获取GPS经纬度信息，客户端可通过此查询接口获取的经纬度信息。"
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GPS_LOCATION_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF05D)"
        },
        {
          "name": "Paraoff1",
          "type": "U32",
          "description": "保留字节"
        },
        {
          "name": "Longitude",
          "type": "F64",
          "description": "经度（正值为东经，负值为西经）"
        },
        {
          "name": "Latitude",
          "type": "F64",
          "description": "维度（正值为北纬，负值为南纬）"
        },
        {
          "name": "Altitude",
          "type": "F64",
          "description": "高度"
        },
        {
          "name": "RateO  fPro",
          "type": "U32",
          "description": "GPS经纬高度获取进度,百分比的值，例如： 50对应50%"
        },
        {
          "name": "Paraoff2",
          "type": "U32",
          "description": "保留字节"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "GPS_LOCATION_QUERY_ACK",
      "messageId": "0xF05D"
    }
  },
  "UE NAS REJECT CAUSE配置查询": {
    "name": "UE NAS REJECT CAUSE配置查询",
    "id": "0xF06B",
    "description": [],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "TAU_ATTACH_REJECT_CAUSE_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF06C)"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "TAU_ATTACH_REJECT_CAUSE_QUERY_ACK",
      "messageId": "0xF06C"
    }
  },
  "GPS同步模式下的pp1s偏移量查询": {
    "name": "GPS同步模式下的pp1s偏移量查询",
    "id": "0xF073",
    "description": [],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GPS1PPS_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF074)"
        },
        {
          "name": "Gpspps1s",
          "type": "S32",
          "description": ""
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "GSP1PPS_QUERY_ACK",
      "messageId": "0xF074"
    }
  },
  "频点自配置后台频点列表查询": {
    "name": "频点自配置后台频点列表查询",
    "id": "0xF04D",
    "description": [
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SELFCFG_ARFCN_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF04E)"
        },
        {
          "name": "DefaultArfcnNum",
          "type": "U32",
          "description": ""
        },
        {
          "name": "ArfcnValue[C_MAX_DEFAULT_ARFCN_NUM]",
          "type": "U32",
          "description": "频点值列表"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "SELFCFG_ARFCN_QUERY_ACK",
      "messageId": "0xF04E"
    }
  },
  "选频配置查询": {
    "name": "选频配置查询",
    "id": "0xF088",
    "description": [
      "应答消息（eNB->LMT）：",
      "PinBandRelation："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SELECT_FREQ_CFG_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF089)"
        },
        {
          "name": "PinBandRelaNum",
          "type": "U32",
          "description": "指定结构体数组元素个数。"
        },
        {
          "name": "pinBandRelaMap[15]",
          "type": "PinBandRelation",
          "description": "管脚频带关系表"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "SELECT_FREQ_CFG_QUERY_ACK",
      "messageId": "0xF089"
    }
  },
  "辅PLMN 列表查询": {
    "name": "辅PLMN 列表查询",
    "id": "0xF062",
    "description": [
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SECONDARY_PLMNS_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF063)"
        },
        {
          "name": "u8SecPLMNNum",
          "type": "U8",
          "description": "辅PLMN的数目"
        },
        {
          "name": "u8SecPLMNList[5][7]",
          "type": "U8",
          "description": "辅PLMN列表"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "SECONDARY_PLMNS_QUERY_ACK",
      "messageId": "0xF063"
    }
  },
  "IMSI黑白名单查询": {
    "name": "IMSI黑白名单查询",
    "id": "0xF043",
    "description": [
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF043)"
        },
        {
          "name": "ControlListType",
          "type": "U8",
          "description": "名单类型"
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": "补充字节"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "CONTROL_LIST_QUERY",
      "messageId": "0xF043"
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF044)"
        },
        {
          "name": "ControlListProperty",
          "type": "U16",
          "description": "名单类型"
        },
        {
          "name": "ControlListUENum",
          "type": "U16",
          "description": "名单中含有的UE数目"
        },
        {
          "name": "ControlListUEId  [C_MAX_CONTROL_LIST_UE_NUM]  [C_MAX_IMSI_LEN]",
          "type": "U8",
          "description": "IMSI字符串，如： \"460011111111111\" 非有效UE ID为'\\0'。 (该字段为固定长度)"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "CONTROL_LIST",
      "messageId": "0xF044"
    }
  },
  "UE测量配置查询": {
    "name": "UE测量配置查询",
    "id": "0xF03D",
    "description": [],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "MEAS_UE_CFG_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF03E)"
        },
        {
          "name": "stUeMeasCfg",
          "type": "wrUeMeasCfg",
          "description": ""
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "MEAS_UE_CFG_QUERY_ACK",
      "messageId": "0xF03E"
    }
  },
  "IMSI文件上传配置查询": {
    "name": "IMSI文件上传配置查询",
    "id": "0xF094",
    "description": [
      "查询消息（LMT->eNB）：",
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "UPLOAD_IMSI_FILE_CFG_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头定义(0xF095)"
        },
        {
          "name": "UploadImsiFileCfg",
          "type": "UploadImsiFileCfg",
          "description": "见4.7.28节 “上传IMSI文件配置”中的定义； 说明：isCfgFtpServer字段在查询接口中无意义。"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "UPLOAD_IMSI_FILE_CFG_QUERY_ACK",
      "messageId": "0xF095"
    }
  },
  "NTP同步状态查询": {
    "name": "NTP同步状态查询",
    "id": "0xF09B",
    "description": [
      "查询消息（LMT->eNB）：",
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "NTP_SYNC_STATE_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头定义(0xF09C)"
        },
        {
          "name": "u8NtpSyncState",
          "type": "U8",
          "description": ""
        },
        {
          "name": "u8Res[3]",
          "type": "U8",
          "description": "空余字节"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "NTP_SYNC_STATE_QUERY_ACK",
      "messageId": "0xF09C"
    }
  },
  "GPS芯片选择gps或北斗配置查询": {
    "name": "GPS芯片选择gps或北斗配置查询",
    "id": "0xF099",
    "description": [
      "查询消息（LMT->eNB）：",
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GPS_OR_BEIDOU_CFG_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF09A)"
        },
        {
          "name": "u32FlagInUse",
          "type": "U32",
          "description": ""
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "GPS_OR_BEIDOU_CFG_QUERY_ACK",
      "messageId": "0xF09A"
    }
  },
  "Band功率衰减关系表查询": {
    "name": "Band功率衰减关系表查询",
    "id": "0xF0A9",
    "description": [
      "查询消息（LMT->eNB）：",
      "O_FL_LMT_TO_ENB_ MULTI_BAND_POWERDEREASE_QUERY(0xF0A9)",
      "应答消息（eNB->LMT）：",
      "该接口用于查询band和衰减对应关系表。返回的结构体和配置的结构体一致。",
      "bandPwrdereaseMap"
    ],
    "metadata": {},
    "request": null,
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0AA)"
        },
        {
          "name": "NumElem",
          "type": "U8",
          "description": "0: 关闭功能 >0:配置相关关系及关系个数"
        },
        {
          "name": "Reserved[3]",
          "type": "U8",
          "description": "预留"
        },
        {
          "name": "BandPwrdereaseMap[32]",
          "type": "bandPwrdereaseMap",
          "description": "Band和衰减值对应关系"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "MULTI_BAND_",
      "messageId": "0xF0AA"
    }
  },
  "Band接收增益关系表查询": {
    "name": "Band接收增益关系表查询",
    "id": "0xF0CA",
    "description": [
      "(0xF0CA)",
      "该接口用于查询band和接收增益对应关系表。返回的结构体和配置的结构体一致。",
      "bandRxgainMap"
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "MULTI_BAND_RXGAIN_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0CB)"
        },
        {
          "name": "NumElem",
          "type": "U8",
          "description": "0: 关闭功能 >0:配置相关关系及关系个数"
        },
        {
          "name": "Reserved[3]",
          "type": "U8",
          "description": "预留"
        },
        {
          "name": "BandRxgainMap[32]",
          "type": "bandRxgainMap",
          "description": "Band和增益值对应关系"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "MULTI_BAND_RXGAIN_QUERY_ACK",
      "messageId": "0xF0CB"
    }
  },
  "RX口功率值查询": {
    "name": "RX口功率值查询",
    "id": "0xF0AB",
    "description": [
      "该接口用于获取RX口的功率值，仅在小区去激活状态有效。"
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GET_RX_PARAMS",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0AC)"
        },
        {
          "name": "RxPwrVal",
          "type": "F32",
          "description": "RX口功率值，单位:dbm"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "GET_RX_PARAMS_ACK",
      "messageId": "0xF0AC"
    }
  },
  "扫频/同步端口查询": {
    "name": "扫频/同步端口查询",
    "id": "0xF0AD",
    "description": [
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "REM_PORT_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0AE)"
        },
        {
          "name": "ScanPort",
          "type": "U8",
          "description": "扫频端口 1：Snf口 0：Rx口"
        },
        {
          "name": "SyncPort",
          "type": "U8",
          "description": "同步端口 1：Snf口 0：Rx口"
        },
        {
          "name": "Spare",
          "type": "U16",
          "description": ""
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "REM_PORT_QUERY_ACK",
      "messageId": "0xF0AE"
    }
  },
  "GPS观星数量及其信噪比查询": {
    "name": "GPS观星数量及其信噪比查询",
    "id": "0xF0E4",
    "description": [
      "应答消息（eNB->LMT）：",
      "wrsvssignalpair"
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GPS_SATELLITE_SIGNAL_LEVEL_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0E5)"
        },
        {
          "name": "SvNumber",
          "type": "U32",
          "description": "观测到的GPS卫星数量"
        },
        {
          "name": "SvSignalPair[14]",
          "type": "wrsvssignalpair",
          "description": "卫星编号及其信噪比"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "GPS_SATELLITE_SIGNAL_LEVEL_QUERY_ACK",
      "messageId": "0xF0E5"
    }
  },
  "异频频点列表查询": {
    "name": "异频频点列表查询",
    "id": "0xF0EA",
    "description": [
      "查询消息（LMT->eNB）：",
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "EUTRA_FREQ_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0EB)"
        },
        {
          "name": "EutraArfcnNumber",
          "type": "U32",
          "description": "异频频点数量"
        },
        {
          "name": "EutraArfcn[C_MAX_EUTRA_ARFCN]",
          "type": "U32",
          "description": ""
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "EUTRA_FREQ_QUERY_ACK",
      "messageId": "0xF0EB"
    }
  },
  "QRxLevMin查询": {
    "name": "QRxLevMin查询",
    "id": "0xF035",
    "description": [
      "查询消息（LMT->eNB）：",
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "QRXLEVMIN_VALUE_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF036)"
        },
        {
          "name": "s32QrxlevMin",
          "type": "S32",
          "description": ""
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "QRXLEVMIN_VALUE_QUERY_ACK",
      "messageId": "0xF036"
    }
  },
  "基站执行状态查询": {
    "name": "基站执行状态查询",
    "id": "0xF01A",
    "description": [
      "查询消息（LMT->eNB）：",
      "应答消息（eNB->LMT）：",
      "基站执行状态会实时上报，一般不需要查询。",
      "应答信息格式见[基站执行状态实时上报](#_基站执行状态实时上报(0xF019))。"
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GET_ENB_STATE",
      "messageId": null
    },
    "response": {
      "parameters": [],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "ENB_STATE_IND",
      "messageId": null
    }
  },
  "基站IP查询": {
    "name": "基站IP查询",
    "id": "0xF033",
    "description": [
      "查询消息（LMT->eNB）：",
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "ENB_IP_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF034)"
        },
        {
          "name": "u8EnbIp[4]",
          "type": "U8",
          "description": "基站IP"
        },
        {
          "name": "u8EnbSubMask[4]",
          "type": "U8",
          "description": "基站子网掩码"
        },
        {
          "name": "u8EnbGateWay[4]",
          "type": "U8",
          "description": "基站网管"
        },
        {
          "name": "u32EnbPort",
          "type": "U32",
          "description": "基站端口号"
        },
        {
          "name": "u8PcMonitorIp[4]",
          "type": "U8",
          "description": "上位机IP"
        },
        {
          "name": "u32PcMonitorPort",
          "type": "U32",
          "description": "上位机端口号"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "ENB_IP_QUERY_ACK",
      "messageId": "0xF034"
    }
  },
  "扫频频点配置查询": {
    "name": "扫频频点配置查询",
    "id": "0xF037",
    "description": [
      "查询消息（LMT->eNB）：",
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "REM_CFG_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF038)"
        },
        {
          "name": "wideRemSREnable",
          "type": "U32",
          "description": "是否开启全频段扫频"
        },
        {
          "name": "sysEarfcnNum",
          "type": "U32",
          "description": "扫频频点数目"
        },
        {
          "name": "sysEarfcn[10]",
          "type": "U32",
          "description": "频点，如38400等"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "REM_CFG_QUERY_ACK",
      "messageId": "0xF038"
    }
  },
  "轮循载波信息查询": {
    "name": "轮循载波信息查询",
    "id": "0xF0F2",
    "description": [
      "查询消息（LMT->eNB）：",
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "ROLL_CARRIER_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0F3)"
        },
        {
          "name": "queryResult",
          "type": "U8",
          "description": "查询结果"
        },
        {
          "name": "rollCarrierCfgNum",
          "type": "U8",
          "description": "轮循载波数，0表示无载波配置"
        },
        {
          "name": "bRollCarrierCfg",
          "type": "U8",
          "description": "0：无正在轮循的载波信息 1：有正在轮循的载波信息"
        },
        {
          "name": "Res[1]",
          "type": "U8",
          "description": "预留"
        },
        {
          "name": "stRollCarrierCfg[8]",
          "type": "wrRollCarrierCfg",
          "description": ""
        },
        {
          "name": "curRollCarrierCfg",
          "type": "wrRollCarrierCfg",
          "description": "正在轮循的载波信息"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "ROLL_CARRIER_QUERY_ACK",
      "messageId": "0xF0F3"
    }
  },
  "发射功率衰减偏移配置查询": {
    "name": "发射功率衰减偏移配置查询",
    "id": "0xF0F6",
    "description": [
      "查询消息（LMT->eNB）：",
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "PWR1_DEREASE_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0F7)"
        },
        {
          "name": "pwr1Derease",
          "type": "U8",
          "description": "配置0则为F015配置与F0A7生效值，配置1则在上面两条消息的基础上-1Db,2则为-2Db,19则为-19Db,20则输出功率为最小255"
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": "预留"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "PWR1_DEREASE_QUERY_ACK",
      "messageId": "0xF0F7"
    }
  },
  "Fdd共建站重定向配置查询": {
    "name": "Fdd共建站重定向配置查询",
    "id": "0xF0E8",
    "description": [
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "FDD_REDIRECTION_CFG_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0E9)"
        },
        {
          "name": "OnOff",
          "type": "U32",
          "description": "重定向开关"
        },
        {
          "name": "UnicomEarfcn",
          "type": "U32",
          "description": "联通重定向频点"
        },
        {
          "name": "TelecomEarfcn",
          "type": "U32",
          "description": "电信重定向频点"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "FDD_REDIRECTION_CFG_QUERY_ACK",
      "messageId": "0xF0E9"
    }
  },
  "TAC配置": {
    "name": "TAC配置",
    "id": "0xF069",
    "description": [
      "此接口用于修改基站的当前TAC值。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "否",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_TAC_MODIFY_REQ_ACK (0xF06A)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF069)"
        },
        {
          "name": "TacValue",
          "type": "U32",
          "description": ""
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "TAC_MODIFY_REQ",
      "messageId": "0xF069"
    },
    "response": null
  },
  "随机接入成功率问询": {
    "name": "随机接入成功率问询",
    "id": "0xF065",
    "description": [
      "此接口用于调试测试阶段，客户端查询基站调度UE性能。一般情况下，",
      "RrcConnCmpNum/RrcConnReqNum可以达到90%左右。"
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "RA_ACCESS_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF066)"
        },
        {
          "name": "RrcConnReqNum",
          "type": "U32",
          "description": "RRC连接请求次数"
        },
        {
          "name": "RrcConnCmpNum",
          "type": "U32",
          "description": "RRC连接建立完成次数"
        },
        {
          "name": "Msg2SchedNum",
          "type": "U32",
          "description": "调度Msg2次数"
        },
        {
          "name": "Msg3SchedNum",
          "type": "U32",
          "description": "收到Msg3次数"
        },
        {
          "name": "RrcImsiNum",
          "type": "U32",
          "description": "获取IMSI个数"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "RA_ACCESS_QUERY_ACK",
      "messageId": "0xF066"
    }
  },
  "随机接入成功率清空请求": {
    "name": "随机接入成功率清空请求",
    "id": "0xF067",
    "description": [
      "基站收到客户端发送的此指令，会把RrcConnReqNum和RrcConnCmpNum清0，重新开始统计。"
    ],
    "metadata": {},
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF067)"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "RA_ACCESS_EMPTY_REQ",
      "messageId": "0xF067"
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF068)"
        },
        {
          "name": "CfgResult",
          "type": "U32",
          "description": "1：失败； 0：配置成功"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "RA_ACCESS_EMPTY_REQ_ACK",
      "messageId": "0xF068"
    }
  },
  "格林威治时间查询": {
    "name": "格林威治时间查询",
    "id": "0xF07B",
    "description": [
      "如果gps锁定，可以查询gps获取到的时间，时间格式是GMT。"
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GMT_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头 (0xF07C)"
        },
        {
          "name": "GmtTmie[32]",
          "type": "S8",
          "description": "格林威治时间"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "GMT_QUERY_ACK",
      "messageId": "0xF07C"
    }
  },
  "获取基站log": {
    "name": "获取基站log",
    "id": "0xF071",
    "description": [],
    "metadata": {},
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF071)"
        },
        {
          "name": "isCfgFtpServer",
          "type": "U8",
          "description": "0:不配置（使用取值范围，如下） 1:配置"
        },
        {
          "name": "FtpServerIp[16]",
          "type": "U8",
          "description": "FTP服务器IP, eg：“***********1”"
        },
        {
          "name": "Res [3]",
          "type": "U8",
          "description": "保留字节"
        },
        {
          "name": "FtpServerPort",
          "type": "U32",
          "description": "FTP服务器端口号，例如21"
        },
        {
          "name": "FtpLoginNam[20]",
          "type": "U8",
          "description": "Ftp用户名， “KKK”"
        },
        {
          "name": "FtpPassword[10]",
          "type": "U8",
          "description": "Ftp登录密码，”123456 ”"
        },
        {
          "name": "FtpServerFilePath[66]",
          "type": "U8",
          "description": "上传文件放置目录,不支持中文目录名,目录以/结尾。 Eg: 欲放置文件于FTP服务器根目录下的filePath文件夹，完整的路径为：“/filePath/”"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GET_ENB_LOG",
      "messageId": "0xF071"
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF072)"
        },
        {
          "name": "CfgResult",
          "type": "U8",
          "description": "配置结果"
        },
        {
          "name": "failCause[23]",
          "type": "U8",
          "description": "指示失败的原因"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "GET_ENB_LOG_ACK",
      "messageId": "0xF072"
    }
  },
  "查询log打印级别": {
    "name": "查询log打印级别",
    "id": "0xF047",
    "description": [
      "应答消息（eNB -> LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SYS_LOG_LEVL_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF048)"
        },
        {
          "name": "queryResult",
          "type": "U8",
          "description": "查询结果"
        },
        {
          "name": "stkLogLevel",
          "type": "U8",
          "description": "STK Log级别"
        },
        {
          "name": "dbgLogLevel",
          "type": "U8",
          "description": "DBG Log级别"
        },
        {
          "name": "oamLogLevel",
          "type": "U8",
          "description": "OAM Log级别"
        },
        {
          "name": "GpsLogSwitch",
          "type": "U8",
          "description": "Gps log 开关"
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": "预留"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "SYS_LOG_LEVL_QUERY_ACK",
      "messageId": "0xF048"
    }
  },
  "设置Log打印级别": {
    "name": "设置Log打印级别",
    "id": "0xF045",
    "description": [
      "基站版本发布默认log级别stkLogLevel为0，dbgLogLevel为2。",
      "调试测试定位问题时，一般stkLogLevel设置为4，dbgLogLevel设置为7。"
    ],
    "metadata": {},
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF045)"
        },
        {
          "name": "isSetStkLogLev",
          "type": "U8",
          "description": "0: 不设置 1：设置"
        },
        {
          "name": "stkLogLevel",
          "type": "U8",
          "description": "0：不打印log 1：critical 2：error 3：warning 4：info 5：debug 6：Max_Lev"
        },
        {
          "name": "isSetDbgLogLev",
          "type": "U8",
          "description": "0:不设置 1：设置"
        },
        {
          "name": "DbgLogLevel",
          "type": "U8",
          "description": "0：不打印log 1：fatal 2：error 3：event 4：warning 5：info 6：debug 7：Max_Lev"
        },
        {
          "name": "isSetOamLogLev",
          "type": "U8",
          "description": "0:不设置 1：设置"
        },
        {
          "name": "oamLogLevel",
          "type": "U8",
          "description": "0:不打印log 1: exception 2:call_stack 3:fatal 4: critical 5: warning 6:trace_info 7:trace_verbose 8:Max_lev"
        },
        {
          "name": "isSetGpsLogSwitch",
          "type": "U8",
          "description": "0:不设置 1：设置"
        },
        {
          "name": "gpsLogSwitch",
          "type": "U8",
          "description": "0: 关闭 1：打开"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SYS_LOG_LEVL_SET",
      "messageId": "0xF045"
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF046)"
        },
        {
          "name": "isSetStkLogLevOk",
          "type": "U8",
          "description": "STK Log配置结果"
        },
        {
          "name": "isSetDbgLogLevOk",
          "type": "U8",
          "description": "DBG Log配置结果"
        },
        {
          "name": "isSetOamLogLevOk",
          "type": "U8",
          "description": "OAM Log配置结果"
        },
        {
          "name": "isSetGpsLogSwitchOk",
          "type": "U8",
          "description": "Gps log 开关配置结果"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "SYS_LOG_LEVL_SET_ACK",
      "messageId": "0xF046"
    }
  }
}

--- Protocol Metadata Registry ---
{
  "服务小区参数配置": {
    "name": "服务小区参数配置",
    "id": "0xF003",
    "description": [
      "此接口用于配置建立小区相关参数配置，在小区激活态配置此消息，基站会执行先去激活再激活的流程；在小区IDLE态下配置此消息，基站会直接执行激活小区的流程。",
      "ServingCellCfgInfo:",
      "备注1：",
      "终端最大发射功率对应系统消息SIB1中P-Max，表示小区允许UE的最大发射功率，一般设置为23，表示23dBm。",
      "备注2：",
      "基站最大发射功率对应系统广播消息SIB2中的referenceSignalPower。此值的设置从加功放之后的总输出功率计算而来，用于终端计算路损，不会影响单板的输出功率。一般设置为20dBm（20W），此值相对于其他功率会比较大，但是经过测试，对基站性能影响不大，可以不用修改。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是。",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_SYS_ARFCN_ACK (0xF004)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头（0xF003）"
        },
        {
          "name": "stServingCellCfgInfo",
          "type": "结构体",
          "description": "小区信息配置"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SYS_ARFCN_CFG",
      "messageId": "0xF003"
    },
    "response": null
  },
  "扫频频点配置": {
    "name": "扫频频点配置",
    "id": "0xF009",
    "description": [
      "TDD模式：",
      "wholeBandRem字段指示是否开启全频段扫频，若开启全频段扫频(wholeBandRem=1)，板卡在扫完配置的频点后，继续搜索公网小区SIB5中配置的其他邻区频点。",
      "基站单板本身支持的扫频范围是（(50MHZ~4GHz)，，但若整机系统中RX或者SINNIFFER口外接了限制接收频率的硬件（比如滤波器），则配置频点时应仅限于属于该频段的频点，且配置wholeBandRem为0。",
      "\\*注：TDD模式下，空口同步、频偏校准和自配置流程会修改扫频频点配置",
      "FDD模式：",
      "此接口用于客户端在基站IDLE态时开始SCAN公网LTE小区参数的流程。无需配置Band ID，基站会根据频点自动计算。FDD模式只支持用SNF端口扫频，基站默认版本是SNF口。",
      "wholeBandRem字段指示是否开启全频段扫频，若开启全频段扫频(wholeBandRem=1)，板卡在扫完配置的频点后，继续搜索公网小区SIB5中配置的其他邻区频点。",
      "\\*注：FDD模式下，服务小区参数配置和频偏校准流程会修改扫频频点配置"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是。",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_SYS_ARFCN_ACK (0xF004)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头（0xF009）"
        },
        {
          "name": "wholeBandRem",
          "type": "U32",
          "description": "是否开启全频段扫频"
        },
        {
          "name": "sysEarfcnNum",
          "type": "U32",
          "description": "扫频频点数目"
        },
        {
          "name": "sysEarfcn[10]",
          "type": "U32",
          "description": "频点，如38400等"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "REM_ANT_CFG",
      "messageId": "0xF009"
    },
    "response": null
  },
  "扫频端口配置": {
    "name": "扫频端口配置",
    "id": "0xF07D",
    "description": [
      "此接口仅用于配置TDD扫频端口，目前支持RX和SINNIFER 2个端口模式。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_REM_ANT_CFG_ACK(0xF07E)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF07D)"
        },
        {
          "name": "RxorSnf",
          "type": "U32",
          "description": "端口类型"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "REM_ANT_CFG",
      "messageId": "0xF07D"
    },
    "response": null
  },
  "基站重启配置": {
    "name": "基站重启配置",
    "id": "0xF00B",
    "description": [
      "此接口用于客户端指示基站执行reboot操作。基站收到此消息，先回复ACK，再执行reboot。基站处于任何状态都会处理该消息。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "否",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_REBOOT_ACK (0xF00C)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头0xF00B"
        },
        {
          "name": "SelfActiveCfg",
          "type": "U32",
          "description": "指示基站重启后是否采用现有参数配置自动激活小区，该字段只有在定位版本中生效，围栏版本不需要判断该字节。"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "REBOOT_CFG",
      "messageId": "0xF00B"
    },
    "response": null
  },
  "小区激活去激活配置": {
    "name": "小区激活去激活配置",
    "id": "0xF00D",
    "description": [
      "在基站IDLE状态下，可通过此消息指示基站采用当前小区配置参数激活小区，如果workAdminState配置为1，TDD基站则不进行同步流程，直接激活小区，如果workAdminState配置为2；TDD基站先执行同步流程，同步成功后再激活小区，如果同步失败，基站仍然回到IDLE状态。",
      "在基站激活状态下，通过配置workAdminState为0，基站则会执行去激活小区的操作，进入IDLE状态。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "否",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_SET_ADMIN_STATE_ACK(0xF00E)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF00D)"
        },
        {
          "name": "workAdminState",
          "type": "U32",
          "description": "/"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SET_ADMIN_STATE_CFG",
      "messageId": "0xF00D"
    },
    "response": null
  },
  "接收增益配置": {
    "name": "接收增益配置",
    "id": "0xF013",
    "description": [
      "该接口用于配置基站9361寄存器的接收增益，表示将接收到的来自UE的信号放大多少倍。接收增益配置值说明参考《[2.4 接收增益](#_接收增益)》。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "可配置",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_SYS_RxGAIN_ACK(0xF014)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF013)"
        },
        {
          "name": "Rxgain",
          "type": "U32",
          "description": "接收增益"
        },
        {
          "name": "RxGainSaveFlag",
          "type": "U8",
          "description": "配置值是否保存到配置，重启之后也保留"
        },
        {
          "name": "RxOrSnfFlag",
          "type": "U8",
          "description": "配置该增益是修改rx口增益还是snf口增益 注：仅FDD有效 对于TDD，该字段无意义，基站不做判断。"
        },
        {
          "name": "Res[2]",
          "type": "U8",
          "description": "保留字节"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SYS_RxGAIN_CFG",
      "messageId": "0xF013"
    },
    "response": null
  },
  "发射功率衰减配置": {
    "name": "发射功率衰减配置",
    "id": "0xF015",
    "description": [
      "该接口用于配置基站发送通道的衰减值，用于客户校准整机输出功率。衰减值每加4，基站输出功率增加1dB衰减。无衰减时，即衰减值为0x00时，基站输出功率范围在-1dbm~-2dbm，每块单板会有差异。",
      "基站实际输出功率 = 零衰减功率 - 衰减值（Pwr1Derease\\*0.25）",
      "例如：基站输出功率为-1dB，当衰减值设置为0x28，输出功率为-11dBm。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "可配置",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_SYS_PWR1_DEREASE_ACK(0xF016)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF015)"
        },
        {
          "name": "Pwr1Derease",
          "type": "U32",
          "description": "功率衰减，每步长代表0.25dB"
        },
        {
          "name": "IsSave",
          "type": "U8",
          "description": "配置值是否保存到配置，重启之后也保留"
        },
        {
          "name": "Res [3]",
          "type": "U8",
          "description": "保留字节"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SYS_PWR1_DEREASE_CFG",
      "messageId": "0xF015"
    },
    "response": null
  },
  "基站IP配置": {
    "name": "基站IP配置",
    "id": "0xF01B",
    "description": [
      "该接口用于修改基站的IP配置。",
      "版本默认基站地址是“************#*************#***********#”。"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_IP_CFG_ACK (0xF01C)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF01B)"
        },
        {
          "name": "eNBIPStr[52]",
          "type": "U8",
          "description": "设置基站的IP"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "IP_CFG",
      "messageId": "0xF01B"
    },
    "response": null
  },
  "客户端IP配置": {
    "name": "客户端IP配置",
    "id": "0xF025",
    "description": [
      "该接口用于配置客户端IP配置，版本默认客户端地址是“***********1#3345”。",
      "注意：该接口中的端口字段，单板在UDP模式下，也使用该端口值进行监听。",
      "端口默认3345，若更改，建议配置3350---3399."
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_LMTIP_CFG_ACK (0xF026)"
    },
    "request": null,
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF025)"
        },
        {
          "name": "LMTIPStr[32]",
          "type": "U8",
          "description": "设置主控板的IP和端口,字符串，以’\\0’结束"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "LMTIP_CFG_ACK",
      "messageId": "0xF025"
    }
  },
  "GPS同步模式下的pp1s偏移量配置": {
    "name": "GPS同步模式下的pp1s偏移量配置",
    "id": "0xF029",
    "description": [
      "在TDD选择GPS同步模式下，此接口用于设置同步偏移量，在中国，一般band39,band40需要进行GPS同步偏移量调节,一般-700微秒（OffsetTime）左右数据帧头偏移（正值说明时域相对原始值向后移动，负值说明是时域对应原始值向前移动），具体各个BAND的偏移量以实际测量为准。",
      "接口中设置的Gpspps1s = OffsetTime \\* （Gpspps1sToBW/微秒）",
      "OffsetTime：运营商网络此BAND相对于GPS的偏移量，单位微秒；",
      "Gpspps1sToBW/微秒：相关带宽下每微秒的偏移值，带宽是指本基带板的带宽；",
      "1微秒的偏移情况下，Gpspps1s与带宽对应关系如下：",
      "例如：基站配置20M带宽，BAND40偏移-700微妙，则接口中配置的Gpspps1s=-700\\*30.72。"
    ],
    "metadata": {
      "isEffectiveImmediately": "否，需要重新进行GPS同步生效。",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_GPS_PP1S_ACK(0xF02A)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF029)"
        },
        {
          "name": "Gpspps1s",
          "type": "S32",
          "description": "Gps pps1s偏移量"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GPS_PP1S_CFG",
      "messageId": "0xF029"
    },
    "response": null
  },
  "上电自动激活小区配置": {
    "name": "上电自动激活小区配置",
    "id": "0xF03B",
    "description": [
      "上电自激活配置：",
      "用于配置基站上电启动时是否执行自动激活小区的流程，默认版本中上电不自动激活小区，进入IDLE状态。",
      "Reboot配置：",
      "Wl模式下，用于配置除带宽改变时，reboot是否执行自动激活小区的流程，默认版本中reboot自动激活小区，进入active状态。",
      ";"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "SELF_ACTIVE_CFG_PWR_ON_ACK",
      "responseMessageId": "0xF03C"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF03B)"
        },
        {
          "name": "SelfActiveCfg",
          "type": "U32",
          "description": "/"
        },
        {
          "name": "rebootSelfActiveCfg",
          "type": "U32",
          "description": "仅WL版本有效"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SELF_ACTIVE_CFG_PWR_ON",
      "messageId": "0xF03B"
    },
    "response": null
  },
  "TDD子帧配置": {
    "name": "TDD子帧配置",
    "id": "0xF049",
    "description": [
      "此消息接口用于配置TDD小区的子帧配比，小区去激活状态下配置立即生效。"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "TDD_SUBFRAME_ASSIGNMENT_SET_ACK",
      "responseMessageId": "0xF04A"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF049)"
        },
        {
          "name": "u8TddSfAssignment;",
          "type": "U8",
          "description": "TDD子帧配比"
        },
        {
          "name": "u8TddSpecialSfPatterns",
          "type": "U8",
          "description": "TDD特殊子帧配比"
        },
        {
          "name": "Res[2]",
          "type": "U8",
          "description": "保留字节"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "TDD_SUBFRAME_ASSIGNMENT_SET",
      "messageId": "0xF049"
    },
    "response": null
  },
  "GPS经纬度信息复位配置": {
    "name": "GPS经纬度信息复位配置",
    "id": "0xF06D",
    "description": [
      "基站启动时，会自动获取GPS经纬度信息，获取的经纬度信息可通过4.9章节的《[GPS经纬高度查询](#_Gps经纬高度查询)》接口查询，一旦获取到经纬度信息，基站会保存此次获取的值，下次重启将不再重复获取，因此如果基站移动了位置，请使用此接口清除上一次获取的经纬度信息。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "否",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_GPS_INFO_RESET_ACK (0xF06E)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF06D)"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GPS_INFO_RESET",
      "messageId": "0xF06D"
    },
    "response": null
  },
  "辅PLMN列表配置": {
    "name": "辅PLMN列表配置",
    "id": "0xF060",
    "description": [
      "此接口用于配置基站广播SIB1 中PLMN LIST字段中的非主PLMN。"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "SECONDARY_PLMNS_SET_ACK",
      "responseMessageId": "0xF061"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF060)"
        },
        {
          "name": "u8SecPLMNNum",
          "type": "U8",
          "description": "辅PLMN的数目"
        },
        {
          "name": "u8SecPLMNList[5][7]",
          "type": "U8",
          "description": "辅PLMN列表"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SECONDARY_PLMNS_SET",
      "messageId": "0xF060"
    },
    "response": null
  },
  "启动小区自配置请求": {
    "name": "启动小区自配置请求",
    "id": "0xF04F",
    "description": [
      "此接口用于指示基站在IDLE态开始小区自配置流程，小区自配置流程图参考《[小区自配置流程](#_小区自配置流程)》。",
      "基站收到此消息，根据自配置频点列表搜索公网频点和小区信息，基站会根据扫频结果，选择本小区参数自动建立小区。",
      "如果整机设备支持全频段，可以配置SelfBand为0xFF，基站将会对自配置频点列表中所有频点以及公网广播消息SIB5中的频点进行全频段扫频。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "否",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "SELFCFG_CELLPARA_REQ_ACK",
      "responseMessageId": "0xF050"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF04F)"
        },
        {
          "name": "SelfBand",
          "type": "U8",
          "description": "指定自配置的频段"
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": "保留字节"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SELFCFG_CELLPARA_REQ",
      "messageId": "0xF04F"
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头 (0xF050)"
        },
        {
          "name": "CfgResult",
          "type": "U32",
          "description": "配置结果"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "SELFCFG_CELLPARA_REQ_ACK",
      "messageId": "0xF050"
    }
  },
  "基站TDD/FDD同步方式配置": {
    "name": "基站TDD/FDD同步方式配置",
    "id": "0xF023",
    "description": [
      "此接口用于设置基站的同步方式，目前仅支持空口和GPS同步。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_REM_MODE_CFG_ACK (0xF024)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF023)"
        },
        {
          "name": "Remmode",
          "type": "U32",
          "description": "TDD模式支持空口和GPS同步，FDD仅支持GPS，用于频率同步。"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "REM_MODE_CFG",
      "messageId": "0xF023"
    },
    "response": null
  },
  "小区自配置后台频点添加/删除": {
    "name": "小区自配置后台频点添加/删除",
    "id": "0xF051",
    "description": [
      "此接口用于配置小区自配置功能的扫频频点列表。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "SELFCFG_ARFCN_CFG_REQ_ACK",
      "responseMessageId": "0xF052"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF051)"
        },
        {
          "name": "Cfgtype",
          "type": "U32",
          "description": ""
        },
        {
          "name": "EarfcnValue",
          "type": "U32",
          "description": "频点值"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SELFCFG_ARFCN_CFG_REQ",
      "messageId": "0xF051"
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头 (0xF052)"
        },
        {
          "name": "CfgResult",
          "type": "U32",
          "description": "配置结果"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "SELFCFG_ARFCN_CFG_REQ_ACK",
      "messageId": "0xF052"
    }
  },
  "动态修改小区参数": {
    "name": "动态修改小区参数",
    "id": "0xF080",
    "description": [
      "此接口用于在小区激活态下，即时修改小区参数，但是此时修改的小区参数重启或者断电之后不会保存。如果当前小区没有激活，会返回配置失败。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "否",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_SYS_ARFCN_MOD_ACK (0xF081)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头 (0xF080)"
        },
        {
          "name": "ulEarfcn",
          "type": "U32",
          "description": "上行频点"
        },
        {
          "name": "dlEarfcn",
          "type": "U32",
          "description": "下行频点"
        },
        {
          "name": "PLMN[7 ]",
          "type": "U8",
          "description": "plmn"
        },
        {
          "name": "Band",
          "type": "U8",
          "description": "频段"
        },
        {
          "name": "CellId",
          "type": "U32",
          "description": "小区Id"
        },
        {
          "name": "UePMax",
          "type": "U32",
          "description": "终端最大发射功率"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SYS_ARFCN_MOD",
      "messageId": "0xF080"
    },
    "response": null
  },
  "NTP服务器IP配置": {
    "name": "NTP服务器IP配置",
    "id": "0xF075",
    "description": [
      "此接口用于设置基站NTP时间同步的NTP服务器IP，系统启动时会自动进行NTP时间同步。"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_NTP_SERVER_IP_CFG_ACK (0xF076)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": ""
        },
        {
          "name": "ntpServerIp[20]",
          "type": "U8",
          "description": ""
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "NTP_SERVER_IP_CFG",
      "messageId": null
    },
    "response": null
  },
  "定点重启配置": {
    "name": "定点重启配置",
    "id": "0xF086",
    "description": [
      "此接口用于配置基站是否开启定点重启功能，基站系统采用NTP同步方式获取系统格林威治时间，如果开启此功能，请设置正确的NTP服务器IP。",
      "版本发布默认此功能关闭。"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "TIME_TO_RESET_CFG_ACK",
      "responseMessageId": "0xF087"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF086)"
        },
        {
          "name": "ResetSwitch",
          "type": "U8",
          "description": "定点重启开关"
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": "保留字节"
        },
        {
          "name": "ResetTime[12]",
          "type": "S8",
          "description": "重启时间配置 例如：“23：15：15” 格林威治时间"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "TIME_TO_RESET_CFG",
      "messageId": "0xF086"
    },
    "response": null
  },
  "开启IMEI捕获功能配置": {
    "name": "开启IMEI捕获功能配置",
    "id": "0xF08A",
    "description": [
      "此接口用于配置基站是否开启获取UE的IMEI功能，由于IMEI的获取，基站会对接入的UE先释放让其重新接入，会影响高速抓号的成功率，因此版本默认是关闭功能，客户可根据自己的需要决定是否开启此功能。根据测试，获取UE IMEI相对于IMSI的比例大概是10~15%。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "IMEI_REQUEST_CFG_ACK",
      "responseMessageId": "0xF08B"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF08A)"
        },
        {
          "name": "ImeiEnable",
          "type": "U8",
          "description": "是否开启IMEI获取功能"
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": "保留字节"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "IMEI_REQUEST_CFG",
      "messageId": "0xF08A"
    },
    "response": null
  },
  "选频配置": {
    "name": "选频配置",
    "id": "0xF082",
    "description": [
      "基站根据当前激活小区的BAND输出不同的GPIO信号，用于客户一个单板切换不同的BAND建立小区时根据该GPIO信号匹配不同的功放。输出GPIO信号的管脚以及配置描述见下图。目前V2 Board提供2根GPIO管脚，V3 Board以及后面型号的Board提供4根GPIO管脚，配置接口兼容支持，请根据不同的Board配置值范围。",
      "PinBandRelation：",
      "版本默认发布BAND与其PIN脚的关系见下图：",
      "V2单板默认关系：",
      "V3单板默认关系：",
      "V5-C单板默认关系：",
      "V6单板默认关系："
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "SELECT_FREQ_CFG_ACK",
      "responseMessageId": "0xF083"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF082)"
        },
        {
          "name": "PinBandRelaNum",
          "type": "U32",
          "description": "指定结构体数组元素个数。"
        },
        {
          "name": "pinBandRelaMap[15]",
          "type": "PinBandRelation",
          "description": "管脚频带关系表"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SELECT_FREQ_CFG",
      "messageId": "0xF082"
    },
    "response": null
  },
  "IMSI黑白名单配置": {
    "name": "IMSI黑白名单配置",
    "id": "0xF039",
    "description": [
      "此接口用于配置IMSI的黑名单和白名单，每次最大可以配置10个IMSI，基站可同时保存维护黑名单和白名单两套名单，最大支持各100个名单配置。根据测量UE的配置模式决定采用哪个名单。",
      "备注1：",
      "此消息会根据IgnoreUENum数组包，请根据消息头中MsgLen和IgnoreUENum解析数据"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "CONTROL_UE_LIST_CFG_ACK",
      "responseMessageId": "0xF03A"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF039)"
        },
        {
          "name": "ControlMovement",
          "type": "U8",
          "description": ""
        },
        {
          "name": "ControlUENum",
          "type": "U8",
          "description": "添加/删除UE数目"
        },
        {
          "name": "ControlUEProperty",
          "type": "U8",
          "description": ""
        },
        {
          "name": "ControlUEIdentity[10][C_MAX_IMSI_LEN]",
          "type": "U8",
          "description": "UE IMSI数组"
        },
        {
          "name": "ClearType",
          "type": "U8",
          "description": "清除黑白名单配置"
        },
        {
          "name": "RejCause",
          "type": "U8",
          "description": "黑/白名单对应的TAU REJECT原因值（仅对重定向模式子模式0生效，名单内外可以使用不同的RejCause）"
        },
        {
          "name": "Res",
          "type": "U8",
          "description": "保留字节"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "CONTROL_UE_LIST_CFG",
      "messageId": "0xF039"
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0x F03A)"
        },
        {
          "name": "CfgResult",
          "type": "U8",
          "description": "配置结果"
        },
        {
          "name": "IgnoreUENum",
          "type": "U8",
          "description": "未操作成功UE数目"
        },
        {
          "name": "IgnoreUEList[10][C_MAX_IMSI_LEN]",
          "type": "U8",
          "description": "UE IMSI数组"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "CONTROL_UE_LIST_CFG_ACK",
      "messageId": null
    }
  },
  "UE NAS REJECT CAUSE配置": {
    "name": "UE NAS REJECT CAUSE配置",
    "id": "0xF057",
    "description": [
      "此接口用于配置基站把接入UE踢回公网时，回复UE的TAU REJECT或者ATTACH REJECT消息中的reject cause值，基站默认使用cause#15，一般此值不需要修改。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "TAU_ATTACH_REJECT_CAUSE_CFG_ACK",
      "responseMessageId": "0xF058"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF057)"
        },
        {
          "name": "RejectCause",
          "type": "U32",
          "description": "回复UE的TAU REJECT或者ATTACH REJECT消息中的reject cause值"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "TAU_ATTACH_REJECT_CAUSE_CFG",
      "messageId": "0xF057"
    },
    "response": null
  },
  "UE重定向信息配置": {
    "name": "UE重定向信息配置",
    "id": "0xF017",
    "description": [
      "此接口用于配置基站发送给UE的释放消息中是否携带重定向参数，默认不携带重定向参数。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_REDIRECT_INFO_ACK (0xF018)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF017)"
        },
        {
          "name": "OnOff",
          "type": "U32",
          "description": "重定向开关"
        },
        {
          "name": "Earfcn",
          "type": "U32",
          "description": "重定向频点"
        },
        {
          "name": "RedirectType",
          "type": "U32",
          "description": "重定向类型"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "REDIRECT_INFO_CFG",
      "messageId": "0xF017"
    },
    "response": null
  },
  "系统模式（TDD/FDD）配置": {
    "name": "系统模式（TDD/FDD）配置",
    "id": "0xF001",
    "description": [
      "此消息用于设置系统模式（TDD/FDD），基站收到此配置，记录启动模式，",
      "客户端下发reboot指令或者重新上下电给基站，基站会根据配置的模式启动系统。",
      "当sysMode=2时，系统根据硬件一个GPIO（PIN5）管脚的输入信号决定启动TDD还是FDD，PINI5悬空时启动TDD，PIN5接地时启动FDD，仅V3系列板卡支持此功能。",
      "仅TDD和FDD共版版本才支持此设置，非共版版本基站会回复失败。",
      "PIN5位置图如下：",
      "![](data:image/png;base64...)"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_SYS_MODE_ACK(0xF002)"
    },
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SYS_MODE_CFG",
      "messageId": null
    },
    "response": null
  },
  "基站版本升级配置": {
    "name": "基站版本升级配置",
    "id": "0xF06F",
    "description": [
      "此接口用于客户端配置升级基站版本使用，需要客户端建立FTP服务器。",
      "UpgradeStatusId:"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "否",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "UPDATE_SOFT_VERSION_CFG_ACK",
      "responseMessageId": "0x070"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF06F)"
        },
        {
          "name": "updateType",
          "type": "U8",
          "description": "升级版本类型"
        },
        {
          "name": "enbSoftFileName[102]",
          "type": "U8",
          "description": "字符串，基站软件版本名字：如 “BaiStation128D_ FDD_R002C0000G01B005.IMG”；"
        },
        {
          "name": "isReservedCfg",
          "type": "U8",
          "description": "是否保留配置（仅对基站软件系统有效）"
        },
        {
          "name": "enbSoftMD5 [36]",
          "type": "U8",
          "description": "针对升级基站软件计算的md5值,32字节长度。"
        },
        {
          "name": "uBootFileName[40]",
          "type": "U8",
          "description": "该字段为boot文件名，如：“u-boot-t2200-nand-1.0.15.img”"
        },
        {
          "name": "ubootMD5 [36]",
          "type": "U8",
          "description": "针对升级文件计算的md5值,32字节长度。"
        },
        {
          "name": "isCfgFtpServer",
          "type": "U8",
          "description": "是否重新配置FTP服务器地址"
        },
        {
          "name": "FtpServerIp[16]",
          "type": "U8",
          "description": "FTP服务器IP, eg: “***********1”，"
        },
        {
          "name": "Reserved[3]",
          "type": "U8",
          "description": "保留字节"
        },
        {
          "name": "FtpServerPort",
          "type": "U32",
          "description": "FTP服务器端口号，eg: 21"
        },
        {
          "name": "FtpLoginNam[20]",
          "type": "U8",
          "description": "Ftp用户名，eg:“kkk ”"
        },
        {
          "name": "FtpPassword[10]",
          "type": "U8",
          "description": "Ftp登录密码, eg: “123456 ”"
        },
        {
          "name": "FtpServerFilePath[66]",
          "type": "U8",
          "description": "待升级文件所在FTP服务器路径，默认根目录。路径以/结尾。 eg：待升级文件位于FTP服务器根目录下的filePath文件夹，完整的路径为：“/filePath/”"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "UPDATE_SOFT_VERSION_CFG",
      "messageId": "0xF06F"
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF070)"
        },
        {
          "name": "VerType",
          "type": "U8",
          "description": "指示升级的版本类型"
        },
        {
          "name": "StatusInd",
          "type": "U8",
          "description": "指示执行状态(UpgradeStatusId)"
        },
        {
          "name": "Reserved[2]",
          "type": "U8",
          "description": "预留"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "UPDATE_SOFT_VERSION_CFG_ACK",
      "messageId": "0xF070"
    }
  },
  "上传IMSI文件配置": {
    "name": "上传IMSI文件配置",
    "id": "0xF077",
    "description": [
      "此接口用于开启基站支持以IMSI文件上传的方式上报到客户端，使用FTP方式。",
      "uploadImsiFileCfg:"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "UPLOAD_IMSI_FILE_CFG_ACK",
      "responseMessageId": "0xF078"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头定义(0xF077)"
        },
        {
          "name": "UploadImsiFileCfg",
          "type": "uploadImsiFileCfg",
          "description": ""
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "UPLOAD_IMSI_FILE_CFG",
      "messageId": "0xF077"
    },
    "response": null
  },
  "UE重定向模式动态黑名单配置": {
    "name": "UE重定向模式动态黑名单配置",
    "id": "0xF08E",
    "description": [
      "本接口用于重定向模式下，配置IMSI黑名单，单次配置最多20，累次配置总数达到1000，删除时间节点配置靠前的IMSI。",
      "ClearImsiListFlag取1并且AddImsiNum非零同时配置，先清空列表再添加。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "否",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "UE_REDIRECT_IMSI_LIST_CFG_ACK",
      "responseMessageId": "0xF08F"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头定义(0xF08E)"
        },
        {
          "name": "ClearImsiListFlag",
          "type": "U8",
          "description": "0:保留当前所有配置 1:清空所有IMSI配置列表"
        },
        {
          "name": "AddImsiNum",
          "type": "U8",
          "description": "增加的IMSI条数;"
        },
        {
          "name": "ImsiStr[C_MAX_UE_REDIRECT_IMSI_ADD_NUM][C_MAX_IMSI_LEN]",
          "type": "U8",
          "description": "字符串数组，如： \"460011111111111\" 非有效UE ID位注意置为'\\0'。 (该字段为固定长度)"
        },
        {
          "name": "Res[2]",
          "type": "U8",
          "description": "预留"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "UE_REDIRECT_IMSI_LIST_CFG",
      "messageId": "0xF08E"
    },
    "response": null
  },
  "FDD GPS同步不成功重新同步配置": {
    "name": "FDD GPS同步不成功重新同步配置",
    "id": "0xF090",
    "description": [
      "该接口用于FDD 模式下（FDD的GPS同步功能只用于校准频偏，小区激活态下有效，同小区激活顺序无关）：",
      "此消息接口用于当gps同步不成功时，重新配置gps去做同步，不需要重新建立小区。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "否",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "FDD_GPS_RESYNC_CFG_ACK",
      "responseMessageId": "0xF091"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头定义(0xF090)"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "REM_MODE_CFG",
      "messageId": "0xF090"
    },
    "response": null
  },
  "上行功控Alpha系数配置": {
    "name": "上行功控Alpha系数配置",
    "id": "0xF092",
    "description": [
      "此消息接口用于当FDD开启配置TDD/FDD小区的SIB2中的上行功控系数，小区去激活状态下配置立即生效。",
      "本接口配置TDD出厂配置默认值为70，FDD默认出厂值为80。在空旷地带测试抓号，建议该值配置成80。",
      "注：配置的（0，40，50，60，70，80，90，100）分别对应sib2信息alpha值的（0，1，2，3，4，5，6，7）。"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "UL_POWER_CONTROL_ALPHA_CFG_ACK",
      "responseMessageId": "0xF093"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头定义(0xF092)"
        },
        {
          "name": "UlPowerAlpha",
          "type": "U8",
          "description": "Sib2中上行功控系数"
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": "预留"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "UL_POWER_CONTROL_ALPHA_CFG",
      "messageId": "0xF092"
    },
    "response": null
  },
  "GPS芯片选择gps或北斗配置": {
    "name": "GPS芯片选择gps或北斗配置",
    "id": "0xF097",
    "description": [
      "此消息接口用于配置gps芯片选择gps或者北斗，配置完重启生效。"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "GPS_OR_BEIDOU_CFG_ACK",
      "responseMessageId": "0xF098"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF097)"
        },
        {
          "name": "u8FlagInUse",
          "type": "U8",
          "description": ""
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": "保留字节"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GPS_OR_BEIDOU_CFG",
      "messageId": "0xF097"
    },
    "response": null
  },
  "单板管脚电平输出控制": {
    "name": "单板管脚电平输出控制",
    "id": "0xF09F",
    "description": [
      "此接口用于控制V3单板PIN1管脚的电平输出，V5板IO6管脚输出控制，以及V6板IO5管脚输出控制。",
      "单板自主判断当前板卡类型，若是V3则该接口控制PIN1输出，若是V5板卡，控制IO6电平输出，若是V6板卡，控制IO5电平输出。",
      "V3对应板卡上扩展IO1里的IO1（J7-IO1）；V5对应板卡上IO6；V6对应板卡上IO5。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "否",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_PINX_SWITCH_CFG_ACK (0xF0A0)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF09F)"
        },
        {
          "name": "PinxSwitch",
          "type": "U8",
          "description": "0: 输出低电平 1：输出高电平"
        },
        {
          "name": "Reserved[3]",
          "type": "U8",
          "description": "预留"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "PINX_SWITCH_CFG",
      "messageId": "0xF09F"
    },
    "response": null
  },
  "Band功率衰减关系表配置": {
    "name": "Band功率衰减关系表配置",
    "id": "0xF0A7",
    "description": [
      "该接口用于配置不同band的小区时，基站根据band，查询关系表，配置衰减值。",
      "出厂默认关闭该功能。",
      "bandPwrdereaseMap"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "MULTI_BAND_POWERDEREASE_ACK",
      "responseMessageId": "0xF0A8"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0A7)"
        },
        {
          "name": "NumElem",
          "type": "U8",
          "description": "0: 关闭功能 >0:配置相关关系及关系个数"
        },
        {
          "name": "Reserved[3]",
          "type": "U8",
          "description": "预留"
        },
        {
          "name": "BandPwrdereaseMap[32]",
          "type": "bandPwrdereaseMap",
          "description": "Band和衰减值对应关系"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "MULTI_BAND_POWERDEREASE_CFG",
      "messageId": "0xF0A7"
    },
    "response": {
      "parameters": [],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "SYS_PWR1_DEREASE",
      "messageId": null
    }
  },
  "Band接收增益关系表配置": {
    "name": "Band接收增益关系表配置",
    "id": "0xF0C8",
    "description": [
      "该接口用于配置不同band的小区时，基站根据band，查询关系表，配置接收增益值。",
      "不支持增量配置。若建小区的band无法在表中查找到对应接收增益值，默认输出接收增益值0xFF。",
      "出厂默认关闭该功能。",
      "bandRxgainMap"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "MULTI_BAND_RXGAIN_CFG_ACK",
      "responseMessageId": "0xF0C9"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0C8)"
        },
        {
          "name": "NumElem",
          "type": "U8",
          "description": "0: 关闭功能 >0:配置相关关系及关系个数"
        },
        {
          "name": "Reserved[3]",
          "type": "U8",
          "description": "预留"
        },
        {
          "name": "BandRxgainMap[32]",
          "type": "bandRxgainMap",
          "description": "Band和增益值对应关系"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "MULTI_BAND_RXGAIN_CFG",
      "messageId": "0xF0C8"
    },
    "response": null
  },
  "异频同步接口配置": {
    "name": "异频同步接口配置",
    "id": "0xF05E",
    "description": [],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是（SNF端口和时偏保留，频点有场景保留有场景不保留）",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "SYNCARFCN_CFG_ACK",
      "responseMessageId": "0xF05F"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF05E)"
        },
        {
          "name": "UsingSnfSwitch",
          "type": "U8",
          "description": "0: 使用Rx同步 1：使用SNF同步 2: 取消异频同步"
        },
        {
          "name": "Reserved1[3]",
          "type": "U8",
          "description": "保留字节"
        },
        {
          "name": "SyncArfcn",
          "type": "U32",
          "description": "同步频点"
        },
        {
          "name": "TimeOffsetPresent",
          "type": "U8",
          "description": "0：不需要配置 1：需要配置"
        },
        {
          "name": "Reserved2[3]",
          "type": "U8",
          "description": "保留字节"
        },
        {
          "name": "S32",
          "type": "TimeOffsetValue",
          "description": "异频同步时偏值（不同band间）"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SYS_ARFCN_CFG",
      "messageId": "0xF05E"
    },
    "response": null
  },
  "MSG4功率抬升配置": {
    "name": "MSG4功率抬升配置",
    "id": "0xF0AF",
    "description": [
      "此接口用于配置MSG4功率是否抬升，TDD生效。默认出厂设置为不抬升，在不同环境下测试时，如出现接入成功率不理想，可以配置抬升MSG4功率。"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMB_MSG4_POWER_BOOST_CFG_ACK (0xF0B0)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0AF)"
        },
        {
          "name": "Msg4PowerBoost",
          "type": "U32",
          "description": "0：不抬升（出厂默认值） 1：抬升"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "MSG4_POWER_BOOST_CFG",
      "messageId": "0xF0AF"
    },
    "response": null
  },
  "GPS固件彻底复位配置": {
    "name": "GPS固件彻底复位配置",
    "id": "0xF0D2",
    "description": [
      "此接口主要用于当gps信号很好的场景下反复无法同步成功，搜不到星的场景，可以尝试将GPS固件彻底复位，复位时间比较长，等收到响应后，重启生效。"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_LMT_TO_ENB_GPS_SOFTWARE_RENOVATE_CFG_ACK(0xF0D3)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0D2)"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GPS_SOFTWARE_RENOVATE_CFG",
      "messageId": "0xF0D2"
    },
    "response": null
  },
  "QRxLevMin配置": {
    "name": "QRxLevMin配置",
    "id": "0xF021",
    "description": [],
    "metadata": {
      "isEffectiveImmediately": "重启生效",
      "persistsOnReboot": "是"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF021)"
        },
        {
          "name": "s8QrxlevMin",
          "type": "S8",
          "description": ""
        },
        {
          "name": "Reserved1[3]",
          "type": "U8",
          "description": "保留字节"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SET_QRXLEVMIN",
      "messageId": "0xF021"
    },
    "response": {
      "parameters": [],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "SET_QRXLEVMIN_ACK",
      "messageId": null
    }
  },
  "设置单板时间": {
    "name": "设置单板时间",
    "id": "0xF01F",
    "description": [],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "否",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_SET_SYS_TMR_ACK (0xF020)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF01F)"
        },
        {
          "name": "Time[20]",
          "type": "U8",
          "description": "单板系统时间设置"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SET_SYS_TMR",
      "messageId": "0xF01F"
    },
    "response": null
  },
  "设置同步失败后激活小区方式": {
    "name": "设置同步失败后激活小区方式",
    "id": "0xF0CC",
    "description": [],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_BUILD_CELL_CFG_ACK (0xF0CD)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0CC)"
        },
        {
          "name": "buildFlag",
          "type": "U32",
          "description": "0: 同步失败不建小区； 1：同步失败强制激活小区。（GPS同步失败1次，空口同步失败3次以上）"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "BUILD_CELL_CFG",
      "messageId": "0xF0CC"
    },
    "response": null
  },
  "设置GPS失步后重启时间": {
    "name": "设置GPS失步后重启时间",
    "id": "0xF0CE",
    "description": [],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：",
      "responseMessageName": "GPS_LOSS_REBOOT_TMR_CFG_ACK",
      "responseMessageId": "0xF0CF"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0CE)"
        },
        {
          "name": "value",
          "type": "U32",
          "description": "单位：分钟"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GPS_LOSS_REBOOT_TMR_CFG",
      "messageId": "0xF0CE"
    },
    "response": null
  },
  "GPS有源/无源设置": {
    "name": "GPS有源/无源设置",
    "id": "0xF0C6",
    "description": [],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "否",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_GPS_SRC_SEL_ACK (0xF0C7)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0C6)"
        },
        {
          "name": "gpsSel",
          "type": "U8",
          "description": "0:无源 1:有源 默认有源"
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": ""
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GPS_SRC_SEL",
      "messageId": "0xF0C6"
    },
    "response": null
  },
  "FTP 上传下载配置": {
    "name": "FTP 上传下载配置",
    "id": "0xF0D0",
    "description": [
      "使用该接口，可以通过FTP下发管控黑白名单，黑名单文件命名格式black.txt,白名单文件名格式white.txt,文件中每个imis一行，文件中不要有空行，最多支持100000个imsi。",
      "isCfgFtp:",
      "0:FTP相关配置仅本次下发有效，重启后不生效",
      "1:FTP相关配置保存在本地，下次不需要再次配置",
      "actionType：",
      "1:通过FTP配置管控黑白名单",
      "2:通过FTP上传当前黑白名单配置文件",
      "ftpGetPutCfg："
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_FTP_GET_PUT_CFG_ACK (0xF0D1)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0D0)"
        },
        {
          "name": "ftpCfg",
          "type": "ftpGetPutCfg",
          "description": "上传下载相关配置"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "FTP_GET_PUT_CFG",
      "messageId": "0xF0D0"
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0D1)"
        },
        {
          "name": "resultNum",
          "type": "U32",
          "description": "操作错误码： 0：下载操作参数错误 1：下载操作开始传输 2：下载操作传输成功 3：下载操作传输失败 4：上传操作参数错误 5：上传操作开始传输 6：上传操作传输成功 7：上传操作传输失败 8：磁盘空间不足"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "FTP_GET_PUT_CFG_ACK",
      "messageId": "0xF0D1"
    }
  },
  "异频频点列表配置": {
    "name": "异频频点列表配置",
    "id": "0xF0E2",
    "description": [
      "此接口用于添加小区SIB5中异频信息，填写下行频点号。"
    ],
    "metadata": {
      "isEffectiveImmediately": "否",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_EUTRA_FREQ_CFG_ACK (0xF0E3)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0E2)"
        },
        {
          "name": "EutraArfcnNumber",
          "type": "U32",
          "description": "配置下行频点数量"
        },
        {
          "name": "EutraArfcn[C_MAX_EUTRA_ARFCN]",
          "type": "U32",
          "description": ""
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "EUTRA_FREQ_CFG",
      "messageId": "0xF0E2"
    },
    "response": null
  },
  "频偏校准开关配置": {
    "name": "频偏校准开关配置",
    "id": "0xF0DA",
    "description": [
      "基站收到此条消息，设置校准开关打开，重启基站，基站重启以后会开始频偏校准过程。",
      "频偏校准过程相关流程如下：",
      "然后客户端下发频偏校准开关（0xF0DA）,打开频偏校准功能；",
      "基站收到0xF0DA消息会上报状态：“频偏校准开始”（0xF019），然后重启基站。",
      "基站重启以后则开始频偏校准流程，上报状态：“频偏校准进行中”（0xF019）。校准过程中会有扫频状态和扫频结果上报。",
      "频偏校准结束，上报状态：“频偏校准结束”（0xF019），同时上报频偏校准结果（0xF0DC），复位校准开关。",
      "校准结束以后，需要客户端下发重启基站的指令，基站重启以后才能进行正常工作的状态。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0DA)"
        },
        {
          "name": "FreqOffsetSwitch",
          "type": "U8",
          "description": "0:关闭 1：打开"
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": "保留字节"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SELFCFG_ARFCN_CFG_REQ",
      "messageId": "0xF0DA"
    },
    "response": {
      "parameters": [],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "FREQ_OFFSET_ADJ_CFG_ACK",
      "messageId": null
    }
  },
  "频偏配置": {
    "name": "频偏配置",
    "id": "0xF059",
    "description": [
      "设置频偏。"
    ],
    "metadata": {
      "isEffectiveImmediately": "重启生效",
      "persistsOnReboot": "是"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF059)"
        },
        {
          "name": "FreqOffset",
          "type": "U32",
          "description": "频偏"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "FREQ_OFFSET_CFG",
      "messageId": "0xF059"
    },
    "response": {
      "parameters": [],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "FREQ_OFFSET_CFG_ACK",
      "messageId": null
    }
  },
  "AGC配置": {
    "name": "AGC配置",
    "id": "0xF079",
    "description": [
      "FDD有效。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF079)"
        },
        {
          "name": "AgcFlag",
          "type": "U32",
          "description": "是否开启AGC"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "AGC_SET",
      "messageId": "0xF079"
    },
    "response": {
      "parameters": [],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "AGC_SET_ACK",
      "messageId": null
    }
  },
  "IMSI加密密钥配置": {
    "name": "IMSI加密密钥配置",
    "id": "0xF08C",
    "description": [],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF08C)"
        },
        {
          "name": "ImsiKenc[16]",
          "type": "U8",
          "description": "IMSI加密密钥"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "IMSI_TRANS_KENC_CFG_ACK",
      "messageId": "0xF08C"
    },
    "response": {
      "parameters": [],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "IMSI_TRANS_KENC_CFG_ACK",
      "messageId": null
    }
  },
  "轮循载波信息配置": {
    "name": "轮循载波信息配置",
    "id": "0xF0F0",
    "description": [
      "wrRollCarrierCfg:"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0F0)"
        },
        {
          "name": "rollCarrierCfgNum",
          "type": "U8",
          "description": "需要配置的轮循载波数，0表示无载波配置"
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": "预留"
        },
        {
          "name": "stRollCarrierCfg[8]",
          "type": "wrRollCarrierCfg",
          "description": "手动轮循配置参数"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "ROLL_CARRIER_CFG",
      "messageId": "0xF0F0"
    },
    "response": {
      "parameters": [],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "ROLL_CARRIER_CFG_ACK",
      "messageId": null
    }
  },
  "发射功率衰减偏移配置": {
    "name": "发射功率衰减偏移配置",
    "id": "0xF0F4",
    "description": [],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0F4)"
        },
        {
          "name": "pwr1Derease",
          "type": "U8",
          "description": "配置0则为F015配置与F0A7生效值，配置1则在上面两条消息的基础上-1Db,2则为-2Db,19则为-19Db,20则输出功率为最小255"
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": "预留"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "PWR1_DEREASE_CFG",
      "messageId": "0xF0F4"
    },
    "response": {
      "parameters": [],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "PWR1_DEREASE_CFG_ACK",
      "messageId": null
    }
  },
  "Fdd共建站重定向信息配置": {
    "name": "Fdd共建站重定向信息配置",
    "id": "0xF0E6",
    "description": [],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "是",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_FDD_REDIRECTION_CFG_ACK(0xF0E7)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0E6)"
        },
        {
          "name": "OnOff",
          "type": "U32",
          "description": "重定向开关"
        },
        {
          "name": "UnicomEarfcn",
          "type": "U32",
          "description": "4G联通重定向频点"
        },
        {
          "name": "TelecomEarfcn",
          "type": "U32",
          "description": "4G电信重定向频点"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "REDIRECT_INFO_CFG",
      "messageId": "0xF0E6"
    },
    "response": null
  },
  "采集用户信息上报": {
    "name": "采集用户信息上报",
    "id": "0xF005",
    "description": [
      "小区激活以后，基站采集接入用户信息并立即上报给客户端，只有开启IMEI捕获功能的时，才会上报IMEI。",
      "主控板用户接口：",
      "围栏版本：",
      "非主控板用户接口——围栏版本：",
      "说明：对于4.7.28中的配置，按照UploadImsiType取1要求并且非主控模式才进行序列号分配，才进行ACK回复。",
      "非主控板用户ACK接口："
    ],
    "metadata": {},
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF096)"
        },
        {
          "name": "seqNum",
          "type": "U32",
          "description": "序列号：等于O_FL_ENB_TO_LMT_UE_INFO_RPT消息中seqNum字段"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "UE_INFO_RPT_ACK",
      "messageId": "0xF096"
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF005)"
        },
        {
          "name": "UeIdType",
          "type": "U32",
          "description": "UE ID类型"
        },
        {
          "name": "IMSI[C_MAX_IMSI_LEN ]",
          "type": "U8",
          "description": "IMSI"
        },
        {
          "name": "IMEI[C_MAX_IMEI_LEN ]",
          "type": "U8",
          "description": "IMEI"
        },
        {
          "name": "RSSI",
          "type": "U8",
          "description": "采集用户的RSSI"
        },
        {
          "name": "STMSIPresent",
          "type": "U8",
          "description": "S-TMSI是否有效"
        },
        {
          "name": "S-TMSI[5]",
          "type": "U8",
          "description": "采集用户的S-TMSI"
        },
        {
          "name": "ConnectedUeNum",
          "type": "U8",
          "description": "当前在线的用户数"
        },
        {
          "name": "TimingAdv",
          "type": "U16",
          "description": "时间提前量"
        },
        {
          "name": "UeAccessType",
          "type": "U8",
          "description": "UE接入类型"
        },
        {
          "name": "Res2[3]",
          "type": "U8",
          "description": "保留字节"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "UE_INFO_RPT",
      "messageId": "0xF005"
    }
  },
  "扫频/同步小区信息上报": {
    "name": "扫频/同步小区信息上报",
    "id": "0xF00A",
    "description": [
      "TDD同步过程中，通过此消息上报尝试同步的小区信息(collectionTypeFlag=1)。",
      "扫频完成后，通过此消息上报扫频的结果信息(collectionTypeFlag=0)。",
      "由于此消息体长度比较大，对于数组的信息，基站会按照实际Num填充上报信息，请客户端解析时，根据相应数组的Num解析数组信息。",
      "常量：",
      "wrFLCollectionCellInfo：",
      "wrFLCellInfo：",
      "wrIntraFreqNeighCellInfo：",
      "stFlLteIntreFreqLst:",
      "wrFLInterNeighCellInfo："
    ],
    "metadata": {},
    "request": null,
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头0xF00A"
        },
        {
          "name": "collectionCellNum",
          "type": "U16",
          "description": "采集的小区数目"
        },
        {
          "name": "collectionTypeFlag",
          "type": "U16",
          "description": "扫频信息标识/同步信息标识"
        },
        {
          "name": "stCollCellInfo  [C_MAX_COLLTECTION_INTRA_CELL_NUM]",
          "type": "wrFLCollectionCellInfo",
          "description": "小区信息"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "REM_INFO_RPT",
      "messageId": "0xF00A"
    }
  },
  "基站执行状态实时上报": {
    "name": "基站执行状态实时上报",
    "id": "0xF019",
    "description": [
      "此消息用于基站实时上报操作流程的执行结果，具体信息参见上报值。",
      "WR_FL_ENB_STATE:"
    ],
    "metadata": {},
    "request": null,
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF019)"
        },
        {
          "name": "CellStateInd",
          "type": "U32",
          "description": "wrFLEnbToLmtEnbStateInd"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "ENB_STATE_IND",
      "messageId": "0xF019"
    }
  },
  "告警指示上报": {
    "name": "告警指示上报",
    "id": "0xF05B",
    "description": [
      "此消息用于基站上报一些运行异常的告警信息，目前支持的告警包括失步、高低温（基带板温度）告警。"
    ],
    "metadata": {},
    "request": null,
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF05B)"
        },
        {
          "name": "AlarmingType",
          "type": "U32",
          "description": "0：基带板高温告警>=70度 1：失步告警 5：基带板低温告警<=-20"
        },
        {
          "name": "AlarmingFlag",
          "type": "U32",
          "description": "0：产生告警指示 1：取消告警指示"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "ALARMING_TYPE_IND",
      "messageId": "0xF05B"
    }
  },
  "频偏校准结果上报": {
    "name": "频偏校准结果上报",
    "id": "0xF0DC",
    "description": [
      "频偏校准结果上报。"
    ],
    "metadata": {},
    "request": null,
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0DC)"
        },
        {
          "name": "FreqOffsetAdjResult",
          "type": "U8",
          "description": "0:成功 1：失败"
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": "保留字节"
        },
        {
          "name": "FreqOffsetValue",
          "type": "S32",
          "description": "仅FreqOffsetAdjResult为0时有效，代表当前单板的频偏值，频偏校准精度为绝对值200以内。"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "FRQ_OFFSET_ADJ_RESULT_IND",
      "messageId": "0xF0DC"
    }
  },
  "自配置结果上报": {
    "name": "自配置结果上报",
    "id": "0xF064",
    "description": [
      "自配置结果上报，TDD有效。"
    ],
    "metadata": {},
    "request": null,
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF064)"
        },
        {
          "name": "dlAfrcn",
          "type": "U32",
          "description": "下行频点"
        },
        {
          "name": "pci",
          "type": "U16",
          "description": "PCI"
        },
        {
          "name": "Tac",
          "type": "U16",
          "description": "TAC"
        },
        {
          "name": "sfassign",
          "type": "U16",
          "description": "TDD子帧配置值"
        },
        {
          "name": "specsfassign",
          "type": "U16",
          "description": "TDD特殊子帧配置值"
        },
        {
          "name": "cellid",
          "type": "U32",
          "description": "Cell ID"
        },
        {
          "name": "Bandwidth",
          "type": "U8",
          "description": "带宽"
        },
        {
          "name": "sysBand",
          "type": "U8",
          "description": "频段"
        },
        {
          "name": "Plmn[6]",
          "type": "U8",
          "description": "PLMN"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "SELFCFG_PARA_RPT",
      "messageId": "0xF064"
    }
  },
  "基站基本信息查询": {
    "name": "基站基本信息查询",
    "id": "0xF02B",
    "description": [
      "此消息用于客户端查询一些基站的基本信息，比如版本号，MAC地址，SN等。"
    ],
    "metadata": {},
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF02B)"
        },
        {
          "name": "u32EnbBaseInfoType",
          "type": "U32",
          "description": "查询信息的类型，"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "BASE_INFO_QUERY",
      "messageId": "0xF02B"
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF02C)"
        },
        {
          "name": "u32EnbBaseInfoType",
          "type": "U32",
          "description": "查询信息的类型，"
        },
        {
          "name": "u8EnbbaseInfo[100]",
          "type": "U8",
          "description": "信息上报"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "BASE_INFO_QUERY_ACK",
      "messageId": "0xF02C"
    }
  },
  "服务小区配置参数查询": {
    "name": "服务小区配置参数查询",
    "id": "0xF027",
    "description": [],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GET_ARFCN",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头 (0xF028)"
        },
        {
          "name": "stServingCellCfgInfo",
          "type": "结构体",
          "description": "小区信息配置"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "ARFCN_IND",
      "messageId": "0xF028"
    }
  },
  "基站同步信息查询": {
    "name": "基站同步信息查询",
    "id": "0xF02D",
    "description": [
      "此消息用于客户端查询基站当前的同步方式和同步状态。"
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SYNC_INFO_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF02E)"
        },
        {
          "name": "u16SyncMode",
          "type": "U16",
          "description": "同步类型"
        },
        {
          "name": "u16SyncState",
          "type": "U16",
          "description": "同步状态"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "SYNC_INFO_QUERY_ACK",
      "messageId": "0xF02E"
    }
  },
  "小区状态信息查询": {
    "name": "小区状态信息查询",
    "id": "0xF02F",
    "description": [
      "应答消息（eNB->LMT）：",
      "此消息用于查询基站的小区状态信息。"
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "CELL_STATE_INFO_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF030)"
        },
        {
          "name": "u32CellState",
          "type": "U32",
          "description": "小区状态"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "CELL_STATE_INFO_QUERY_ACK",
      "messageId": "0xF030"
    }
  },
  "接收增益和发射功率查询": {
    "name": "接收增益和发射功率查询",
    "id": "0xF031",
    "description": [
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "RXGAIN_POWER_DEREASE_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF032)"
        },
        {
          "name": "u8RxGainValueFromReg",
          "type": "U8",
          "description": "寄存器中的值，实际生效的值（FDD模式下仅在建立完小区查询，该值有效）"
        },
        {
          "name": "u8RxGainValueFromMib",
          "type": "U8",
          "description": "数据库中的保存值，重启保留生效的值,"
        },
        {
          "name": "u8PowerDereaseValueFromReg",
          "type": "U8",
          "description": "寄存器中的值，实际生效的值（FDD模式下仅在建立完小区查询，该值有效）"
        },
        {
          "name": "u8PowerDereaseValueFromMib",
          "type": "U8",
          "description": "数据库中的保存值，重启保留生效的值"
        },
        {
          "name": "u8AgcFlag",
          "type": "U8",
          "description": "FDD AGC开关"
        },
        {
          "name": "u8SnfRxGainValueFromReg",
          "type": "U8",
          "description": "只在FDD模式下有效，寄存器中的值，实际生效的值,该值只有在扫频完成后，建立小区前查询有效"
        },
        {
          "name": "u8SnfRxGainValueFromMib",
          "type": "U8",
          "description": "eeprom中的保存值，重启保留生效的值"
        },
        {
          "name": "Res[1]",
          "type": "U8",
          "description": "保留字段"
        },
        {
          "name": "pwrDecreDelta",
          "type": "S32",
          "description": "u8PowerDereaseValueFromReg = u8PowerDereaseValueFromMib +4\\*delta， 仅DW版本有效"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "RXGAIN_POWER_DEREASE_QUERY_ACK",
      "messageId": "0xF032"
    }
  },
  "重定向配置查询": {
    "name": "重定向配置查询",
    "id": "0xF03F",
    "description": [
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "REDIRECT_INFO_CFG_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF040)"
        },
        {
          "name": "OnOff",
          "type": "U32",
          "description": "重定向开关"
        },
        {
          "name": "Earfcn",
          "type": "U32",
          "description": "重定向频点"
        },
        {
          "name": "RedirectType",
          "type": "U32",
          "description": "重定向类型"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "REDIRECT_INFO_CFG_QUERY_ACK",
      "messageId": "0xF040"
    }
  },
  "上电小区自激活配置查询": {
    "name": "上电小区自激活配置查询",
    "id": "0xF041",
    "description": [
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SELF_ACTIVE_CFG_PWR_ON_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF042)"
        },
        {
          "name": "SelfActiveCfg",
          "type": "U32",
          "description": "基站上电是否采用当前配置自动激活小区"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "SELF_ACTIVE_CFG_PWR_ON_QUERY_ACK",
      "messageId": "0xF042"
    }
  },
  "TDD子帧配置和上行功控系数查询": {
    "name": "TDD子帧配置和上行功控系数查询",
    "id": "0xF04B",
    "description": [
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "TDD_SUBFRAME_ASSIGNMENT_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF04C)"
        },
        {
          "name": "u8TddSfAssignment;",
          "type": "U8",
          "description": "TDD子帧配比（fdd该值为255）"
        },
        {
          "name": "u8TddSpecialSfPatterns",
          "type": "U8",
          "description": "TDD特殊子帧配比（fdd该值为255）"
        },
        {
          "name": "u8UlAlpha",
          "type": "U8",
          "description": "Sib2中上行功控系数"
        },
        {
          "name": "Res[1]",
          "type": "U8",
          "description": "保留字节"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "TDD_SUBFRAME_ASSIGNMENT_QUERY_ACK",
      "messageId": "0xF04C"
    }
  },
  "GPS经纬高度查询": {
    "name": "GPS经纬高度查询",
    "id": "0xF05C",
    "description": [
      "基站启动时，会自动获取GPS经纬度信息，客户端可通过此查询接口获取的经纬度信息。"
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GPS_LOCATION_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF05D)"
        },
        {
          "name": "Paraoff1",
          "type": "U32",
          "description": "保留字节"
        },
        {
          "name": "Longitude",
          "type": "F64",
          "description": "经度（正值为东经，负值为西经）"
        },
        {
          "name": "Latitude",
          "type": "F64",
          "description": "维度（正值为北纬，负值为南纬）"
        },
        {
          "name": "Altitude",
          "type": "F64",
          "description": "高度"
        },
        {
          "name": "RateO  fPro",
          "type": "U32",
          "description": "GPS经纬高度获取进度,百分比的值，例如： 50对应50%"
        },
        {
          "name": "Paraoff2",
          "type": "U32",
          "description": "保留字节"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "GPS_LOCATION_QUERY_ACK",
      "messageId": "0xF05D"
    }
  },
  "UE NAS REJECT CAUSE配置查询": {
    "name": "UE NAS REJECT CAUSE配置查询",
    "id": "0xF06B",
    "description": [],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "TAU_ATTACH_REJECT_CAUSE_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF06C)"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "TAU_ATTACH_REJECT_CAUSE_QUERY_ACK",
      "messageId": "0xF06C"
    }
  },
  "GPS同步模式下的pp1s偏移量查询": {
    "name": "GPS同步模式下的pp1s偏移量查询",
    "id": "0xF073",
    "description": [],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GPS1PPS_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF074)"
        },
        {
          "name": "Gpspps1s",
          "type": "S32",
          "description": ""
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "GSP1PPS_QUERY_ACK",
      "messageId": "0xF074"
    }
  },
  "频点自配置后台频点列表查询": {
    "name": "频点自配置后台频点列表查询",
    "id": "0xF04D",
    "description": [
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SELFCFG_ARFCN_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF04E)"
        },
        {
          "name": "DefaultArfcnNum",
          "type": "U32",
          "description": ""
        },
        {
          "name": "ArfcnValue[C_MAX_DEFAULT_ARFCN_NUM]",
          "type": "U32",
          "description": "频点值列表"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "SELFCFG_ARFCN_QUERY_ACK",
      "messageId": "0xF04E"
    }
  },
  "选频配置查询": {
    "name": "选频配置查询",
    "id": "0xF088",
    "description": [
      "应答消息（eNB->LMT）：",
      "PinBandRelation："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SELECT_FREQ_CFG_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF089)"
        },
        {
          "name": "PinBandRelaNum",
          "type": "U32",
          "description": "指定结构体数组元素个数。"
        },
        {
          "name": "pinBandRelaMap[15]",
          "type": "PinBandRelation",
          "description": "管脚频带关系表"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "SELECT_FREQ_CFG_QUERY_ACK",
      "messageId": "0xF089"
    }
  },
  "辅PLMN 列表查询": {
    "name": "辅PLMN 列表查询",
    "id": "0xF062",
    "description": [
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SECONDARY_PLMNS_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF063)"
        },
        {
          "name": "u8SecPLMNNum",
          "type": "U8",
          "description": "辅PLMN的数目"
        },
        {
          "name": "u8SecPLMNList[5][7]",
          "type": "U8",
          "description": "辅PLMN列表"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "SECONDARY_PLMNS_QUERY_ACK",
      "messageId": "0xF063"
    }
  },
  "IMSI黑白名单查询": {
    "name": "IMSI黑白名单查询",
    "id": "0xF043",
    "description": [
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF043)"
        },
        {
          "name": "ControlListType",
          "type": "U8",
          "description": "名单类型"
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": "补充字节"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "CONTROL_LIST_QUERY",
      "messageId": "0xF043"
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF044)"
        },
        {
          "name": "ControlListProperty",
          "type": "U16",
          "description": "名单类型"
        },
        {
          "name": "ControlListUENum",
          "type": "U16",
          "description": "名单中含有的UE数目"
        },
        {
          "name": "ControlListUEId  [C_MAX_CONTROL_LIST_UE_NUM]  [C_MAX_IMSI_LEN]",
          "type": "U8",
          "description": "IMSI字符串，如： \"460011111111111\" 非有效UE ID为'\\0'。 (该字段为固定长度)"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "CONTROL_LIST",
      "messageId": "0xF044"
    }
  },
  "UE测量配置查询": {
    "name": "UE测量配置查询",
    "id": "0xF03D",
    "description": [],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "MEAS_UE_CFG_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF03E)"
        },
        {
          "name": "stUeMeasCfg",
          "type": "wrUeMeasCfg",
          "description": ""
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "MEAS_UE_CFG_QUERY_ACK",
      "messageId": "0xF03E"
    }
  },
  "IMSI文件上传配置查询": {
    "name": "IMSI文件上传配置查询",
    "id": "0xF094",
    "description": [
      "查询消息（LMT->eNB）：",
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "UPLOAD_IMSI_FILE_CFG_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头定义(0xF095)"
        },
        {
          "name": "UploadImsiFileCfg",
          "type": "UploadImsiFileCfg",
          "description": "见4.7.28节 “上传IMSI文件配置”中的定义； 说明：isCfgFtpServer字段在查询接口中无意义。"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "UPLOAD_IMSI_FILE_CFG_QUERY_ACK",
      "messageId": "0xF095"
    }
  },
  "NTP同步状态查询": {
    "name": "NTP同步状态查询",
    "id": "0xF09B",
    "description": [
      "查询消息（LMT->eNB）：",
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "NTP_SYNC_STATE_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头定义(0xF09C)"
        },
        {
          "name": "u8NtpSyncState",
          "type": "U8",
          "description": ""
        },
        {
          "name": "u8Res[3]",
          "type": "U8",
          "description": "空余字节"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "NTP_SYNC_STATE_QUERY_ACK",
      "messageId": "0xF09C"
    }
  },
  "GPS芯片选择gps或北斗配置查询": {
    "name": "GPS芯片选择gps或北斗配置查询",
    "id": "0xF099",
    "description": [
      "查询消息（LMT->eNB）：",
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GPS_OR_BEIDOU_CFG_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF09A)"
        },
        {
          "name": "u32FlagInUse",
          "type": "U32",
          "description": ""
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "GPS_OR_BEIDOU_CFG_QUERY_ACK",
      "messageId": "0xF09A"
    }
  },
  "Band功率衰减关系表查询": {
    "name": "Band功率衰减关系表查询",
    "id": "0xF0A9",
    "description": [
      "查询消息（LMT->eNB）：",
      "O_FL_LMT_TO_ENB_ MULTI_BAND_POWERDEREASE_QUERY(0xF0A9)",
      "应答消息（eNB->LMT）：",
      "该接口用于查询band和衰减对应关系表。返回的结构体和配置的结构体一致。",
      "bandPwrdereaseMap"
    ],
    "metadata": {},
    "request": null,
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0AA)"
        },
        {
          "name": "NumElem",
          "type": "U8",
          "description": "0: 关闭功能 >0:配置相关关系及关系个数"
        },
        {
          "name": "Reserved[3]",
          "type": "U8",
          "description": "预留"
        },
        {
          "name": "BandPwrdereaseMap[32]",
          "type": "bandPwrdereaseMap",
          "description": "Band和衰减值对应关系"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "MULTI_BAND_",
      "messageId": "0xF0AA"
    }
  },
  "Band接收增益关系表查询": {
    "name": "Band接收增益关系表查询",
    "id": "0xF0CA",
    "description": [
      "(0xF0CA)",
      "该接口用于查询band和接收增益对应关系表。返回的结构体和配置的结构体一致。",
      "bandRxgainMap"
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "MULTI_BAND_RXGAIN_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0CB)"
        },
        {
          "name": "NumElem",
          "type": "U8",
          "description": "0: 关闭功能 >0:配置相关关系及关系个数"
        },
        {
          "name": "Reserved[3]",
          "type": "U8",
          "description": "预留"
        },
        {
          "name": "BandRxgainMap[32]",
          "type": "bandRxgainMap",
          "description": "Band和增益值对应关系"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "MULTI_BAND_RXGAIN_QUERY_ACK",
      "messageId": "0xF0CB"
    }
  },
  "RX口功率值查询": {
    "name": "RX口功率值查询",
    "id": "0xF0AB",
    "description": [
      "该接口用于获取RX口的功率值，仅在小区去激活状态有效。"
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GET_RX_PARAMS",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0AC)"
        },
        {
          "name": "RxPwrVal",
          "type": "F32",
          "description": "RX口功率值，单位:dbm"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "GET_RX_PARAMS_ACK",
      "messageId": "0xF0AC"
    }
  },
  "扫频/同步端口查询": {
    "name": "扫频/同步端口查询",
    "id": "0xF0AD",
    "description": [
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "REM_PORT_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0AE)"
        },
        {
          "name": "ScanPort",
          "type": "U8",
          "description": "扫频端口 1：Snf口 0：Rx口"
        },
        {
          "name": "SyncPort",
          "type": "U8",
          "description": "同步端口 1：Snf口 0：Rx口"
        },
        {
          "name": "Spare",
          "type": "U16",
          "description": ""
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "REM_PORT_QUERY_ACK",
      "messageId": "0xF0AE"
    }
  },
  "GPS观星数量及其信噪比查询": {
    "name": "GPS观星数量及其信噪比查询",
    "id": "0xF0E4",
    "description": [
      "应答消息（eNB->LMT）：",
      "wrsvssignalpair"
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GPS_SATELLITE_SIGNAL_LEVEL_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0E5)"
        },
        {
          "name": "SvNumber",
          "type": "U32",
          "description": "观测到的GPS卫星数量"
        },
        {
          "name": "SvSignalPair[14]",
          "type": "wrsvssignalpair",
          "description": "卫星编号及其信噪比"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "GPS_SATELLITE_SIGNAL_LEVEL_QUERY_ACK",
      "messageId": "0xF0E5"
    }
  },
  "异频频点列表查询": {
    "name": "异频频点列表查询",
    "id": "0xF0EA",
    "description": [
      "查询消息（LMT->eNB）：",
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "EUTRA_FREQ_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0EB)"
        },
        {
          "name": "EutraArfcnNumber",
          "type": "U32",
          "description": "异频频点数量"
        },
        {
          "name": "EutraArfcn[C_MAX_EUTRA_ARFCN]",
          "type": "U32",
          "description": ""
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "EUTRA_FREQ_QUERY_ACK",
      "messageId": "0xF0EB"
    }
  },
  "QRxLevMin查询": {
    "name": "QRxLevMin查询",
    "id": "0xF035",
    "description": [
      "查询消息（LMT->eNB）：",
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "QRXLEVMIN_VALUE_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF036)"
        },
        {
          "name": "s32QrxlevMin",
          "type": "S32",
          "description": ""
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "QRXLEVMIN_VALUE_QUERY_ACK",
      "messageId": "0xF036"
    }
  },
  "基站执行状态查询": {
    "name": "基站执行状态查询",
    "id": "0xF01A",
    "description": [
      "查询消息（LMT->eNB）：",
      "应答消息（eNB->LMT）：",
      "基站执行状态会实时上报，一般不需要查询。",
      "应答信息格式见[基站执行状态实时上报](#_基站执行状态实时上报(0xF019))。"
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GET_ENB_STATE",
      "messageId": null
    },
    "response": {
      "parameters": [],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "ENB_STATE_IND",
      "messageId": null
    }
  },
  "基站IP查询": {
    "name": "基站IP查询",
    "id": "0xF033",
    "description": [
      "查询消息（LMT->eNB）：",
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "ENB_IP_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF034)"
        },
        {
          "name": "u8EnbIp[4]",
          "type": "U8",
          "description": "基站IP"
        },
        {
          "name": "u8EnbSubMask[4]",
          "type": "U8",
          "description": "基站子网掩码"
        },
        {
          "name": "u8EnbGateWay[4]",
          "type": "U8",
          "description": "基站网管"
        },
        {
          "name": "u32EnbPort",
          "type": "U32",
          "description": "基站端口号"
        },
        {
          "name": "u8PcMonitorIp[4]",
          "type": "U8",
          "description": "上位机IP"
        },
        {
          "name": "u32PcMonitorPort",
          "type": "U32",
          "description": "上位机端口号"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "ENB_IP_QUERY_ACK",
      "messageId": "0xF034"
    }
  },
  "扫频频点配置查询": {
    "name": "扫频频点配置查询",
    "id": "0xF037",
    "description": [
      "查询消息（LMT->eNB）：",
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "REM_CFG_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF038)"
        },
        {
          "name": "wideRemSREnable",
          "type": "U32",
          "description": "是否开启全频段扫频"
        },
        {
          "name": "sysEarfcnNum",
          "type": "U32",
          "description": "扫频频点数目"
        },
        {
          "name": "sysEarfcn[10]",
          "type": "U32",
          "description": "频点，如38400等"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "REM_CFG_QUERY_ACK",
      "messageId": "0xF038"
    }
  },
  "轮循载波信息查询": {
    "name": "轮循载波信息查询",
    "id": "0xF0F2",
    "description": [
      "查询消息（LMT->eNB）：",
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "ROLL_CARRIER_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0F3)"
        },
        {
          "name": "queryResult",
          "type": "U8",
          "description": "查询结果"
        },
        {
          "name": "rollCarrierCfgNum",
          "type": "U8",
          "description": "轮循载波数，0表示无载波配置"
        },
        {
          "name": "bRollCarrierCfg",
          "type": "U8",
          "description": "0：无正在轮循的载波信息 1：有正在轮循的载波信息"
        },
        {
          "name": "Res[1]",
          "type": "U8",
          "description": "预留"
        },
        {
          "name": "stRollCarrierCfg[8]",
          "type": "wrRollCarrierCfg",
          "description": ""
        },
        {
          "name": "curRollCarrierCfg",
          "type": "wrRollCarrierCfg",
          "description": "正在轮循的载波信息"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "ROLL_CARRIER_QUERY_ACK",
      "messageId": "0xF0F3"
    }
  },
  "发射功率衰减偏移配置查询": {
    "name": "发射功率衰减偏移配置查询",
    "id": "0xF0F6",
    "description": [
      "查询消息（LMT->eNB）：",
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "PWR1_DEREASE_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0F7)"
        },
        {
          "name": "pwr1Derease",
          "type": "U8",
          "description": "配置0则为F015配置与F0A7生效值，配置1则在上面两条消息的基础上-1Db,2则为-2Db,19则为-19Db,20则输出功率为最小255"
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": "预留"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "PWR1_DEREASE_QUERY_ACK",
      "messageId": "0xF0F7"
    }
  },
  "Fdd共建站重定向配置查询": {
    "name": "Fdd共建站重定向配置查询",
    "id": "0xF0E8",
    "description": [
      "应答消息（eNB->LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "FDD_REDIRECTION_CFG_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF0E9)"
        },
        {
          "name": "OnOff",
          "type": "U32",
          "description": "重定向开关"
        },
        {
          "name": "UnicomEarfcn",
          "type": "U32",
          "description": "联通重定向频点"
        },
        {
          "name": "TelecomEarfcn",
          "type": "U32",
          "description": "电信重定向频点"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "FDD_REDIRECTION_CFG_QUERY_ACK",
      "messageId": "0xF0E9"
    }
  },
  "TAC配置": {
    "name": "TAC配置",
    "id": "0xF069",
    "description": [
      "此接口用于修改基站的当前TAC值。"
    ],
    "metadata": {
      "isEffectiveImmediately": "是",
      "persistsOnReboot": "否",
      "rawResponseMessageIndicator": "应答消息（eNB ->LMT）：O_FL_ENB_TO_LMT_TAC_MODIFY_REQ_ACK (0xF06A)"
    },
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF069)"
        },
        {
          "name": "TacValue",
          "type": "U32",
          "description": ""
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "TAC_MODIFY_REQ",
      "messageId": "0xF069"
    },
    "response": null
  },
  "随机接入成功率问询": {
    "name": "随机接入成功率问询",
    "id": "0xF065",
    "description": [
      "此接口用于调试测试阶段，客户端查询基站调度UE性能。一般情况下，",
      "RrcConnCmpNum/RrcConnReqNum可以达到90%左右。"
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "RA_ACCESS_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF066)"
        },
        {
          "name": "RrcConnReqNum",
          "type": "U32",
          "description": "RRC连接请求次数"
        },
        {
          "name": "RrcConnCmpNum",
          "type": "U32",
          "description": "RRC连接建立完成次数"
        },
        {
          "name": "Msg2SchedNum",
          "type": "U32",
          "description": "调度Msg2次数"
        },
        {
          "name": "Msg3SchedNum",
          "type": "U32",
          "description": "收到Msg3次数"
        },
        {
          "name": "RrcImsiNum",
          "type": "U32",
          "description": "获取IMSI个数"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "RA_ACCESS_QUERY_ACK",
      "messageId": "0xF066"
    }
  },
  "随机接入成功率清空请求": {
    "name": "随机接入成功率清空请求",
    "id": "0xF067",
    "description": [
      "基站收到客户端发送的此指令，会把RrcConnReqNum和RrcConnCmpNum清0，重新开始统计。"
    ],
    "metadata": {},
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF067)"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "RA_ACCESS_EMPTY_REQ",
      "messageId": "0xF067"
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF068)"
        },
        {
          "name": "CfgResult",
          "type": "U32",
          "description": "1：失败； 0：配置成功"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "RA_ACCESS_EMPTY_REQ_ACK",
      "messageId": "0xF068"
    }
  },
  "格林威治时间查询": {
    "name": "格林威治时间查询",
    "id": "0xF07B",
    "description": [
      "如果gps锁定，可以查询gps获取到的时间，时间格式是GMT。"
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GMT_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头 (0xF07C)"
        },
        {
          "name": "GmtTmie[32]",
          "type": "S8",
          "description": "格林威治时间"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "GMT_QUERY_ACK",
      "messageId": "0xF07C"
    }
  },
  "获取基站log": {
    "name": "获取基站log",
    "id": "0xF071",
    "description": [],
    "metadata": {},
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF071)"
        },
        {
          "name": "isCfgFtpServer",
          "type": "U8",
          "description": "0:不配置（使用取值范围，如下） 1:配置"
        },
        {
          "name": "FtpServerIp[16]",
          "type": "U8",
          "description": "FTP服务器IP, eg：“***********1”"
        },
        {
          "name": "Res [3]",
          "type": "U8",
          "description": "保留字节"
        },
        {
          "name": "FtpServerPort",
          "type": "U32",
          "description": "FTP服务器端口号，例如21"
        },
        {
          "name": "FtpLoginNam[20]",
          "type": "U8",
          "description": "Ftp用户名， “KKK”"
        },
        {
          "name": "FtpPassword[10]",
          "type": "U8",
          "description": "Ftp登录密码，”123456 ”"
        },
        {
          "name": "FtpServerFilePath[66]",
          "type": "U8",
          "description": "上传文件放置目录,不支持中文目录名,目录以/结尾。 Eg: 欲放置文件于FTP服务器根目录下的filePath文件夹，完整的路径为：“/filePath/”"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "GET_ENB_LOG",
      "messageId": "0xF071"
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF072)"
        },
        {
          "name": "CfgResult",
          "type": "U8",
          "description": "配置结果"
        },
        {
          "name": "failCause[23]",
          "type": "U8",
          "description": "指示失败的原因"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "GET_ENB_LOG_ACK",
      "messageId": "0xF072"
    }
  },
  "查询log打印级别": {
    "name": "查询log打印级别",
    "id": "0xF047",
    "description": [
      "应答消息（eNB -> LMT）："
    ],
    "metadata": {},
    "request": {
      "parameters": [],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SYS_LOG_LEVL_QUERY",
      "messageId": null
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF048)"
        },
        {
          "name": "queryResult",
          "type": "U8",
          "description": "查询结果"
        },
        {
          "name": "stkLogLevel",
          "type": "U8",
          "description": "STK Log级别"
        },
        {
          "name": "dbgLogLevel",
          "type": "U8",
          "description": "DBG Log级别"
        },
        {
          "name": "oamLogLevel",
          "type": "U8",
          "description": "OAM Log级别"
        },
        {
          "name": "GpsLogSwitch",
          "type": "U8",
          "description": "Gps log 开关"
        },
        {
          "name": "Res[3]",
          "type": "U8",
          "description": "预留"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "SYS_LOG_LEVL_QUERY_ACK",
      "messageId": "0xF048"
    }
  },
  "设置Log打印级别": {
    "name": "设置Log打印级别",
    "id": "0xF045",
    "description": [
      "基站版本发布默认log级别stkLogLevel为0，dbgLogLevel为2。",
      "调试测试定位问题时，一般stkLogLevel设置为4，dbgLogLevel设置为7。"
    ],
    "metadata": {},
    "request": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF045)"
        },
        {
          "name": "isSetStkLogLev",
          "type": "U8",
          "description": "0: 不设置 1：设置"
        },
        {
          "name": "stkLogLevel",
          "type": "U8",
          "description": "0：不打印log 1：critical 2：error 3：warning 4：info 5：debug 6：Max_Lev"
        },
        {
          "name": "isSetDbgLogLev",
          "type": "U8",
          "description": "0:不设置 1：设置"
        },
        {
          "name": "DbgLogLevel",
          "type": "U8",
          "description": "0：不打印log 1：fatal 2：error 3：event 4：warning 5：info 6：debug 7：Max_Lev"
        },
        {
          "name": "isSetOamLogLev",
          "type": "U8",
          "description": "0:不设置 1：设置"
        },
        {
          "name": "oamLogLevel",
          "type": "U8",
          "description": "0:不打印log 1: exception 2:call_stack 3:fatal 4: critical 5: warning 6:trace_info 7:trace_verbose 8:Max_lev"
        },
        {
          "name": "isSetGpsLogSwitch",
          "type": "U8",
          "description": "0:不设置 1：设置"
        },
        {
          "name": "gpsLogSwitch",
          "type": "U8",
          "description": "0: 关闭 1：打开"
        }
      ],
      "direction": "Server -> Board",
      "type": "Request",
      "messageCodeName": "SYS_LOG_LEVL_SET",
      "messageId": "0xF045"
    },
    "response": {
      "parameters": [
        {
          "name": "WrmsgHeaderInfo",
          "type": "wrMsgHeader",
          "description": "消息头(0xF046)"
        },
        {
          "name": "isSetStkLogLevOk",
          "type": "U8",
          "description": "STK Log配置结果"
        },
        {
          "name": "isSetDbgLogLevOk",
          "type": "U8",
          "description": "DBG Log配置结果"
        },
        {
          "name": "isSetOamLogLevOk",
          "type": "U8",
          "description": "OAM Log配置结果"
        },
        {
          "name": "isSetGpsLogSwitchOk",
          "type": "U8",
          "description": "Gps log 开关配置结果"
        }
      ],
      "direction": "Board -> Server",
      "type": "Response",
      "messageCodeName": "SYS_LOG_LEVL_SET_ACK",
      "messageId": "0xF046"
    }
  }
}

Starting LLM Tools Schema generation...
Generated LLM Tools Schema: [
  {
    "type": "function",
    "function": {
      "name": "sys_arfcn_cfg",
      "description": "此接口用于配置建立小区相关参数配置，在小区激活态配置此消息，基站会执行先去激活再激活的流程；在小区IDLE态下配置此消息，基站会直接执行激活小区的流程。 ServingCellCfgInfo: 备注1： 终端最大发射功率对应系统消息SIB1中P-Max，表示小区允许UE的最大发射功率，一般设置为23，表示23dBm。 备注2： 基站最大发射功率对应系统广播消息SIB2中的referenceSignalPower。此值的设置从加功放之后的总输出功率计算而来，用于终端计算路损，不会影响单板的输出功率。一般设置为20dBm（20W），此值相对于其他功率会比较大，但是经过测试，对基站性能影响不大，可以不用修改。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头（0xF003）"
          },
          "stServingCellCfgInfo": {
            "type": "string",
            "description": "小区信息配置"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "stServingCellCfgInfo"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "rem_ant_cfg",
      "description": "TDD模式： wholeBandRem字段指示是否开启全频段扫频，若开启全频段扫频(wholeBandRem=1)，板卡在扫完配置的频点后，继续搜索公网小区SIB5中配置的其他邻区频点。 基站单板本身支持的扫频范围是（(50MHZ~4GHz)，，但若整机系统中RX或者SINNIFFER口外接了限制接收频率的硬件（比如滤波器），则配置频点时应仅限于属于该频段的频点，且配置wholeBandRem为0。 \\*注：TDD模式下，空口同步、频偏校准和自配置流程会修改扫频频点配置 FDD模式： 此接口用于客户端在基站IDLE态时开始SCAN公网LTE小区参数的流程。无需配置Band ID，基站会根据频点自动计算。FDD模式只支持用SNF端口扫频，基站默认版本是SNF口。 wholeBandRem字段指示是否开启全频段扫频，若开启全频段扫频(wholeBandRem=1)，板卡在扫完配置的频点后，继续搜索公网小区SIB5中配置的其他邻区频点。 \\*注：FDD模式下，服务小区参数配置和频偏校准流程会修改扫频频点配置",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头（0xF009）"
          },
          "wholeBandRem": {
            "type": "integer",
            "description": "是否开启全频段扫频 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "sysEarfcnNum": {
            "type": "integer",
            "description": "扫频频点数目 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "sysEarfcn[10]": {
            "type": "integer",
            "description": "频点，如38400等 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "wholeBandRem",
          "sysEarfcnNum",
          "sysEarfcn[10]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "rem_ant_cfg",
      "description": "此接口仅用于配置TDD扫频端口，目前支持RX和SINNIFER 2个端口模式。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF07D)"
          },
          "RxorSnf": {
            "type": "integer",
            "description": "端口类型 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "RxorSnf"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "reboot_cfg",
      "description": "此接口用于客户端指示基站执行reboot操作。基站收到此消息，先回复ACK，再执行reboot。基站处于任何状态都会处理该消息。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头0xF00B"
          },
          "SelfActiveCfg": {
            "type": "integer",
            "description": "指示基站重启后是否采用现有参数配置自动激活小区，该字段只有在定位版本中生效，围栏版本不需要判断该字节。 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "SelfActiveCfg"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "set_admin_state_cfg",
      "description": "在基站IDLE状态下，可通过此消息指示基站采用当前小区配置参数激活小区，如果workAdminState配置为1，TDD基站则不进行同步流程，直接激活小区，如果workAdminState配置为2；TDD基站先执行同步流程，同步成功后再激活小区，如果同步失败，基站仍然回到IDLE状态。 在基站激活状态下，通过配置workAdminState为0，基站则会执行去激活小区的操作，进入IDLE状态。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF00D)"
          },
          "workAdminState": {
            "type": "integer",
            "description": "/ (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "workAdminState"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "sys_rxgain_cfg",
      "description": "该接口用于配置基站9361寄存器的接收增益，表示将接收到的来自UE的信号放大多少倍。接收增益配置值说明参考《[2.4 接收增益](#_接收增益)》。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF013)"
          },
          "Rxgain": {
            "type": "integer",
            "description": "接收增益 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "RxGainSaveFlag": {
            "type": "string",
            "description": "配置值是否保存到配置，重启之后也保留"
          },
          "RxOrSnfFlag": {
            "type": "string",
            "description": "配置该增益是修改rx口增益还是snf口增益 注：仅FDD有效 对于TDD，该字段无意义，基站不做判断。"
          },
          "Res[2]": {
            "type": "string",
            "description": "保留字节"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "Rxgain",
          "RxGainSaveFlag",
          "RxOrSnfFlag",
          "Res[2]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "sys_pwr1_derease_cfg",
      "description": "该接口用于配置基站发送通道的衰减值，用于客户校准整机输出功率。衰减值每加4，基站输出功率增加1dB衰减。无衰减时，即衰减值为0x00时，基站输出功率范围在-1dbm~-2dbm，每块单板会有差异。 基站实际输出功率 = 零衰减功率 - 衰减值（Pwr1Derease\\*0.25） 例如：基站输出功率为-1dB，当衰减值设置为0x28，输出功率为-11dBm。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF015)"
          },
          "Pwr1Derease": {
            "type": "integer",
            "description": "功率衰减，每步长代表0.25dB (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "IsSave": {
            "type": "string",
            "description": "配置值是否保存到配置，重启之后也保留"
          },
          "Res [3]": {
            "type": "string",
            "description": "保留字节"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "Pwr1Derease",
          "IsSave",
          "Res [3]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "ip_cfg",
      "description": "该接口用于修改基站的IP配置。 版本默认基站地址是“************#*************#***********#”。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF01B)"
          },
          "eNBIPStr[52]": {
            "type": "string",
            "description": "设置基站的IP"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "eNBIPStr[52]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "gps_pp1s_cfg",
      "description": "在TDD选择GPS同步模式下，此接口用于设置同步偏移量，在中国，一般band39,band40需要进行GPS同步偏移量调节,一般-700微秒（OffsetTime）左右数据帧头偏移（正值说明时域相对原始值向后移动，负值说明是时域对应原始值向前移动），具体各个BAND的偏移量以实际测量为准。 接口中设置的Gpspps1s = OffsetTime \\* （Gpspps1sToBW/微秒） OffsetTime：运营商网络此BAND相对于GPS的偏移量，单位微秒； Gpspps1sToBW/微秒：相关带宽下每微秒的偏移值，带宽是指本基带板的带宽； 1微秒的偏移情况下，Gpspps1s与带宽对应关系如下： 例如：基站配置20M带宽，BAND40偏移-700微妙，则接口中配置的Gpspps1s=-700\\*30.72。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF029)"
          },
          "Gpspps1s": {
            "type": "string",
            "description": "Gps pps1s偏移量"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "Gpspps1s"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "self_active_cfg_pwr_on",
      "description": "上电自激活配置： 用于配置基站上电启动时是否执行自动激活小区的流程，默认版本中上电不自动激活小区，进入IDLE状态。 Reboot配置： Wl模式下，用于配置除带宽改变时，reboot是否执行自动激活小区的流程，默认版本中reboot自动激活小区，进入active状态。 ;",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF03B)"
          },
          "SelfActiveCfg": {
            "type": "integer",
            "description": "/ (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "rebootSelfActiveCfg": {
            "type": "integer",
            "description": "仅WL版本有效 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "SelfActiveCfg",
          "rebootSelfActiveCfg"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "tdd_subframe_assignment_set",
      "description": "此消息接口用于配置TDD小区的子帧配比，小区去激活状态下配置立即生效。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF049)"
          },
          "u8TddSfAssignment;": {
            "type": "string",
            "description": "TDD子帧配比"
          },
          "u8TddSpecialSfPatterns": {
            "type": "string",
            "description": "TDD特殊子帧配比"
          },
          "Res[2]": {
            "type": "string",
            "description": "保留字节"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "u8TddSfAssignment;",
          "u8TddSpecialSfPatterns",
          "Res[2]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "gps_info_reset",
      "description": "基站启动时，会自动获取GPS经纬度信息，获取的经纬度信息可通过4.9章节的《[GPS经纬高度查询](#_Gps经纬高度查询)》接口查询，一旦获取到经纬度信息，基站会保存此次获取的值，下次重启将不再重复获取，因此如果基站移动了位置，请使用此接口清除上一次获取的经纬度信息。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF06D)"
          }
        },
        "required": [
          "WrmsgHeaderInfo"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "secondary_plmns_set",
      "description": "此接口用于配置基站广播SIB1 中PLMN LIST字段中的非主PLMN。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF060)"
          },
          "u8SecPLMNNum": {
            "type": "string",
            "description": "辅PLMN的数目"
          },
          "u8SecPLMNList[5][7]": {
            "type": "string",
            "description": "辅PLMN列表"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "u8SecPLMNNum",
          "u8SecPLMNList[5][7]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "selfcfg_cellpara_req",
      "description": "此接口用于指示基站在IDLE态开始小区自配置流程，小区自配置流程图参考《[小区自配置流程](#_小区自配置流程)》。 基站收到此消息，根据自配置频点列表搜索公网频点和小区信息，基站会根据扫频结果，选择本小区参数自动建立小区。 如果整机设备支持全频段，可以配置SelfBand为0xFF，基站将会对自配置频点列表中所有频点以及公网广播消息SIB5中的频点进行全频段扫频。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF04F)"
          },
          "SelfBand": {
            "type": "string",
            "description": "指定自配置的频段"
          },
          "Res[3]": {
            "type": "string",
            "description": "保留字节"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "SelfBand",
          "Res[3]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "rem_mode_cfg",
      "description": "此接口用于设置基站的同步方式，目前仅支持空口和GPS同步。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF023)"
          },
          "Remmode": {
            "type": "integer",
            "description": "TDD模式支持空口和GPS同步，FDD仅支持GPS，用于频率同步。 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "Remmode"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "selfcfg_arfcn_cfg_req",
      "description": "此接口用于配置小区自配置功能的扫频频点列表。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF051)"
          },
          "Cfgtype": {
            "type": "integer",
            "description": " (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "EarfcnValue": {
            "type": "integer",
            "description": "频点值 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "Cfgtype",
          "EarfcnValue"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "sys_arfcn_mod",
      "description": "此接口用于在小区激活态下，即时修改小区参数，但是此时修改的小区参数重启或者断电之后不会保存。如果当前小区没有激活，会返回配置失败。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头 (0xF080)"
          },
          "ulEarfcn": {
            "type": "integer",
            "description": "上行频点 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "dlEarfcn": {
            "type": "integer",
            "description": "下行频点 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "PLMN[7 ]": {
            "type": "string",
            "description": "plmn"
          },
          "Band": {
            "type": "string",
            "description": "频段"
          },
          "CellId": {
            "type": "integer",
            "description": "小区Id (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "UePMax": {
            "type": "integer",
            "description": "终端最大发射功率 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "ulEarfcn",
          "dlEarfcn",
          "PLMN[7 ]",
          "Band",
          "CellId",
          "UePMax"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "ntp_server_ip_cfg",
      "description": "此接口用于设置基站NTP时间同步的NTP服务器IP，系统启动时会自动进行NTP时间同步。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": ""
          },
          "ntpServerIp[20]": {
            "type": "string",
            "description": ""
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "ntpServerIp[20]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "time_to_reset_cfg",
      "description": "此接口用于配置基站是否开启定点重启功能，基站系统采用NTP同步方式获取系统格林威治时间，如果开启此功能，请设置正确的NTP服务器IP。 版本发布默认此功能关闭。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF086)"
          },
          "ResetSwitch": {
            "type": "string",
            "description": "定点重启开关"
          },
          "Res[3]": {
            "type": "string",
            "description": "保留字节"
          },
          "ResetTime[12]": {
            "type": "string",
            "description": "重启时间配置 例如：“23：15：15” 格林威治时间"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "ResetSwitch",
          "Res[3]",
          "ResetTime[12]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "imei_request_cfg",
      "description": "此接口用于配置基站是否开启获取UE的IMEI功能，由于IMEI的获取，基站会对接入的UE先释放让其重新接入，会影响高速抓号的成功率，因此版本默认是关闭功能，客户可根据自己的需要决定是否开启此功能。根据测试，获取UE IMEI相对于IMSI的比例大概是10~15%。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF08A)"
          },
          "ImeiEnable": {
            "type": "string",
            "description": "是否开启IMEI获取功能"
          },
          "Res[3]": {
            "type": "string",
            "description": "保留字节"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "ImeiEnable",
          "Res[3]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "select_freq_cfg",
      "description": "基站根据当前激活小区的BAND输出不同的GPIO信号，用于客户一个单板切换不同的BAND建立小区时根据该GPIO信号匹配不同的功放。输出GPIO信号的管脚以及配置描述见下图。目前V2 Board提供2根GPIO管脚，V3 Board以及后面型号的Board提供4根GPIO管脚，配置接口兼容支持，请根据不同的Board配置值范围。 PinBandRelation： 版本默认发布BAND与其PIN脚的关系见下图： V2单板默认关系： V3单板默认关系： V5-C单板默认关系： V6单板默认关系：",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF082)"
          },
          "PinBandRelaNum": {
            "type": "integer",
            "description": "指定结构体数组元素个数。 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "pinBandRelaMap[15]": {
            "type": "string",
            "description": "管脚频带关系表"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "PinBandRelaNum",
          "pinBandRelaMap[15]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "control_ue_list_cfg",
      "description": "此接口用于配置IMSI的黑名单和白名单，每次最大可以配置10个IMSI，基站可同时保存维护黑名单和白名单两套名单，最大支持各100个名单配置。根据测量UE的配置模式决定采用哪个名单。 备注1： 此消息会根据IgnoreUENum数组包，请根据消息头中MsgLen和IgnoreUENum解析数据",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF039)"
          },
          "ControlMovement": {
            "type": "string",
            "description": ""
          },
          "ControlUENum": {
            "type": "string",
            "description": "添加/删除UE数目"
          },
          "ControlUEProperty": {
            "type": "string",
            "description": ""
          },
          "ControlUEIdentity[10][C_MAX_IMSI_LEN]": {
            "type": "string",
            "description": "UE IMSI数组"
          },
          "ClearType": {
            "type": "string",
            "description": "清除黑白名单配置"
          },
          "RejCause": {
            "type": "string",
            "description": "黑/白名单对应的TAU REJECT原因值（仅对重定向模式子模式0生效，名单内外可以使用不同的RejCause）"
          },
          "Res": {
            "type": "string",
            "description": "保留字节"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "ControlMovement",
          "ControlUENum",
          "ControlUEProperty",
          "ControlUEIdentity[10][C_MAX_IMSI_LEN]",
          "ClearType",
          "RejCause",
          "Res"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "tau_attach_reject_cause_cfg",
      "description": "此接口用于配置基站把接入UE踢回公网时，回复UE的TAU REJECT或者ATTACH REJECT消息中的reject cause值，基站默认使用cause#15，一般此值不需要修改。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF057)"
          },
          "RejectCause": {
            "type": "integer",
            "description": "回复UE的TAU REJECT或者ATTACH REJECT消息中的reject cause值 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "RejectCause"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "redirect_info_cfg",
      "description": "此接口用于配置基站发送给UE的释放消息中是否携带重定向参数，默认不携带重定向参数。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF017)"
          },
          "OnOff": {
            "type": "integer",
            "description": "重定向开关 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "Earfcn": {
            "type": "integer",
            "description": "重定向频点 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "RedirectType": {
            "type": "integer",
            "description": "重定向类型 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "OnOff",
          "Earfcn",
          "RedirectType"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "sys_mode_cfg",
      "description": "此消息用于设置系统模式（TDD/FDD），基站收到此配置，记录启动模式， 客户端下发reboot指令或者重新上下电给基站，基站会根据配置的模式启动系统。 当sysMode=2时，系统根据硬件一个GPIO（PIN5）管脚的输入信号决定启动TDD还是FDD，PINI5悬空时启动TDD，PIN5接地时启动FDD，仅V3系列板卡支持此功能。 仅TDD和FDD共版版本才支持此设置，非共版版本基站会回复失败。 PIN5位置图如下： ![](data:image/png;base64...)",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "update_soft_version_cfg",
      "description": "此接口用于客户端配置升级基站版本使用，需要客户端建立FTP服务器。 UpgradeStatusId:",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF06F)"
          },
          "updateType": {
            "type": "string",
            "description": "升级版本类型"
          },
          "enbSoftFileName[102]": {
            "type": "string",
            "description": "字符串，基站软件版本名字：如 “BaiStation128D_ FDD_R002C0000G01B005.IMG”；"
          },
          "isReservedCfg": {
            "type": "string",
            "description": "是否保留配置（仅对基站软件系统有效）"
          },
          "enbSoftMD5 [36]": {
            "type": "string",
            "description": "针对升级基站软件计算的md5值,32字节长度。"
          },
          "uBootFileName[40]": {
            "type": "string",
            "description": "该字段为boot文件名，如：“u-boot-t2200-nand-1.0.15.img”"
          },
          "ubootMD5 [36]": {
            "type": "string",
            "description": "针对升级文件计算的md5值,32字节长度。"
          },
          "isCfgFtpServer": {
            "type": "string",
            "description": "是否重新配置FTP服务器地址"
          },
          "FtpServerIp[16]": {
            "type": "string",
            "description": "FTP服务器IP, eg: “***********1”，"
          },
          "Reserved[3]": {
            "type": "string",
            "description": "保留字节"
          },
          "FtpServerPort": {
            "type": "integer",
            "description": "FTP服务器端口号，eg: 21 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "FtpLoginNam[20]": {
            "type": "string",
            "description": "Ftp用户名，eg:“kkk ”"
          },
          "FtpPassword[10]": {
            "type": "string",
            "description": "Ftp登录密码, eg: “123456 ”"
          },
          "FtpServerFilePath[66]": {
            "type": "string",
            "description": "待升级文件所在FTP服务器路径，默认根目录。路径以/结尾。 eg：待升级文件位于FTP服务器根目录下的filePath文件夹，完整的路径为：“/filePath/”"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "updateType",
          "enbSoftFileName[102]",
          "isReservedCfg",
          "enbSoftMD5 [36]",
          "uBootFileName[40]",
          "ubootMD5 [36]",
          "isCfgFtpServer",
          "FtpServerIp[16]",
          "Reserved[3]",
          "FtpServerPort",
          "FtpLoginNam[20]",
          "FtpPassword[10]",
          "FtpServerFilePath[66]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "upload_imsi_file_cfg",
      "description": "此接口用于开启基站支持以IMSI文件上传的方式上报到客户端，使用FTP方式。 uploadImsiFileCfg:",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头定义(0xF077)"
          },
          "UploadImsiFileCfg": {
            "type": "string",
            "description": ""
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "UploadImsiFileCfg"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "ue_redirect_imsi_list_cfg",
      "description": "本接口用于重定向模式下，配置IMSI黑名单，单次配置最多20，累次配置总数达到1000，删除时间节点配置靠前的IMSI。 ClearImsiListFlag取1并且AddImsiNum非零同时配置，先清空列表再添加。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头定义(0xF08E)"
          },
          "ClearImsiListFlag": {
            "type": "string",
            "description": "0:保留当前所有配置 1:清空所有IMSI配置列表"
          },
          "AddImsiNum": {
            "type": "string",
            "description": "增加的IMSI条数;"
          },
          "ImsiStr[C_MAX_UE_REDIRECT_IMSI_ADD_NUM][C_MAX_IMSI_LEN]": {
            "type": "string",
            "description": "字符串数组，如： \"460011111111111\" 非有效UE ID位注意置为'\\0'。 (该字段为固定长度)"
          },
          "Res[2]": {
            "type": "string",
            "description": "预留"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "ClearImsiListFlag",
          "AddImsiNum",
          "ImsiStr[C_MAX_UE_REDIRECT_IMSI_ADD_NUM][C_MAX_IMSI_LEN]",
          "Res[2]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "rem_mode_cfg",
      "description": "该接口用于FDD 模式下（FDD的GPS同步功能只用于校准频偏，小区激活态下有效，同小区激活顺序无关）： 此消息接口用于当gps同步不成功时，重新配置gps去做同步，不需要重新建立小区。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头定义(0xF090)"
          }
        },
        "required": [
          "WrmsgHeaderInfo"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "ul_power_control_alpha_cfg",
      "description": "此消息接口用于当FDD开启配置TDD/FDD小区的SIB2中的上行功控系数，小区去激活状态下配置立即生效。 本接口配置TDD出厂配置默认值为70，FDD默认出厂值为80。在空旷地带测试抓号，建议该值配置成80。 注：配置的（0，40，50，60，70，80，90，100）分别对应sib2信息alpha值的（0，1，2，3，4，5，6，7）。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头定义(0xF092)"
          },
          "UlPowerAlpha": {
            "type": "string",
            "description": "Sib2中上行功控系数"
          },
          "Res[3]": {
            "type": "string",
            "description": "预留"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "UlPowerAlpha",
          "Res[3]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "gps_or_beidou_cfg",
      "description": "此消息接口用于配置gps芯片选择gps或者北斗，配置完重启生效。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF097)"
          },
          "u8FlagInUse": {
            "type": "string",
            "description": ""
          },
          "Res[3]": {
            "type": "string",
            "description": "保留字节"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "u8FlagInUse",
          "Res[3]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "pinx_switch_cfg",
      "description": "此接口用于控制V3单板PIN1管脚的电平输出，V5板IO6管脚输出控制，以及V6板IO5管脚输出控制。 单板自主判断当前板卡类型，若是V3则该接口控制PIN1输出，若是V5板卡，控制IO6电平输出，若是V6板卡，控制IO5电平输出。 V3对应板卡上扩展IO1里的IO1（J7-IO1）；V5对应板卡上IO6；V6对应板卡上IO5。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF09F)"
          },
          "PinxSwitch": {
            "type": "string",
            "description": "0: 输出低电平 1：输出高电平"
          },
          "Reserved[3]": {
            "type": "string",
            "description": "预留"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "PinxSwitch",
          "Reserved[3]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "multi_band_powerderease_cfg",
      "description": "该接口用于配置不同band的小区时，基站根据band，查询关系表，配置衰减值。 出厂默认关闭该功能。 bandPwrdereaseMap",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0A7)"
          },
          "NumElem": {
            "type": "string",
            "description": "0: 关闭功能 >0:配置相关关系及关系个数"
          },
          "Reserved[3]": {
            "type": "string",
            "description": "预留"
          },
          "BandPwrdereaseMap[32]": {
            "type": "string",
            "description": "Band和衰减值对应关系"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "NumElem",
          "Reserved[3]",
          "BandPwrdereaseMap[32]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "multi_band_rxgain_cfg",
      "description": "该接口用于配置不同band的小区时，基站根据band，查询关系表，配置接收增益值。 不支持增量配置。若建小区的band无法在表中查找到对应接收增益值，默认输出接收增益值0xFF。 出厂默认关闭该功能。 bandRxgainMap",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0C8)"
          },
          "NumElem": {
            "type": "string",
            "description": "0: 关闭功能 >0:配置相关关系及关系个数"
          },
          "Reserved[3]": {
            "type": "string",
            "description": "预留"
          },
          "BandRxgainMap[32]": {
            "type": "string",
            "description": "Band和增益值对应关系"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "NumElem",
          "Reserved[3]",
          "BandRxgainMap[32]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "sys_arfcn_cfg",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF05E)"
          },
          "UsingSnfSwitch": {
            "type": "string",
            "description": "0: 使用Rx同步 1：使用SNF同步 2: 取消异频同步"
          },
          "Reserved1[3]": {
            "type": "string",
            "description": "保留字节"
          },
          "SyncArfcn": {
            "type": "integer",
            "description": "同步频点 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "TimeOffsetPresent": {
            "type": "string",
            "description": "0：不需要配置 1：需要配置"
          },
          "Reserved2[3]": {
            "type": "string",
            "description": "保留字节"
          },
          "S32": {
            "type": "string",
            "description": "异频同步时偏值（不同band间）"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "UsingSnfSwitch",
          "Reserved1[3]",
          "SyncArfcn",
          "TimeOffsetPresent",
          "Reserved2[3]",
          "S32"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "msg4_power_boost_cfg",
      "description": "此接口用于配置MSG4功率是否抬升，TDD生效。默认出厂设置为不抬升，在不同环境下测试时，如出现接入成功率不理想，可以配置抬升MSG4功率。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0AF)"
          },
          "Msg4PowerBoost": {
            "type": "integer",
            "description": "0：不抬升（出厂默认值） 1：抬升 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "Msg4PowerBoost"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "gps_software_renovate_cfg",
      "description": "此接口主要用于当gps信号很好的场景下反复无法同步成功，搜不到星的场景，可以尝试将GPS固件彻底复位，复位时间比较长，等收到响应后，重启生效。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0D2)"
          }
        },
        "required": [
          "WrmsgHeaderInfo"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "set_qrxlevmin",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF021)"
          },
          "s8QrxlevMin": {
            "type": "string",
            "description": ""
          },
          "Reserved1[3]": {
            "type": "string",
            "description": "保留字节"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "s8QrxlevMin",
          "Reserved1[3]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "set_sys_tmr",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF01F)"
          },
          "Time[20]": {
            "type": "string",
            "description": "单板系统时间设置"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "Time[20]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "build_cell_cfg",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0CC)"
          },
          "buildFlag": {
            "type": "integer",
            "description": "0: 同步失败不建小区； 1：同步失败强制激活小区。（GPS同步失败1次，空口同步失败3次以上） (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "buildFlag"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "gps_loss_reboot_tmr_cfg",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0CE)"
          },
          "value": {
            "type": "integer",
            "description": "单位：分钟 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "value"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "gps_src_sel",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0C6)"
          },
          "gpsSel": {
            "type": "string",
            "description": "0:无源 1:有源 默认有源"
          },
          "Res[3]": {
            "type": "string",
            "description": ""
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "gpsSel",
          "Res[3]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "ftp_get_put_cfg",
      "description": "使用该接口，可以通过FTP下发管控黑白名单，黑名单文件命名格式black.txt,白名单文件名格式white.txt,文件中每个imis一行，文件中不要有空行，最多支持100000个imsi。 isCfgFtp: 0:FTP相关配置仅本次下发有效，重启后不生效 1:FTP相关配置保存在本地，下次不需要再次配置 actionType： 1:通过FTP配置管控黑白名单 2:通过FTP上传当前黑白名单配置文件 ftpGetPutCfg：",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0D0)"
          },
          "ftpCfg": {
            "type": "string",
            "description": "上传下载相关配置"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "ftpCfg"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "eutra_freq_cfg",
      "description": "此接口用于添加小区SIB5中异频信息，填写下行频点号。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0E2)"
          },
          "EutraArfcnNumber": {
            "type": "integer",
            "description": "配置下行频点数量 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "EutraArfcn[C_MAX_EUTRA_ARFCN]": {
            "type": "integer",
            "description": " (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "EutraArfcnNumber",
          "EutraArfcn[C_MAX_EUTRA_ARFCN]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "selfcfg_arfcn_cfg_req",
      "description": "基站收到此条消息，设置校准开关打开，重启基站，基站重启以后会开始频偏校准过程。 频偏校准过程相关流程如下： 然后客户端下发频偏校准开关（0xF0DA）,打开频偏校准功能； 基站收到0xF0DA消息会上报状态：“频偏校准开始”（0xF019），然后重启基站。 基站重启以后则开始频偏校准流程，上报状态：“频偏校准进行中”（0xF019）。校准过程中会有扫频状态和扫频结果上报。 频偏校准结束，上报状态：“频偏校准结束”（0xF019），同时上报频偏校准结果（0xF0DC），复位校准开关。 校准结束以后，需要客户端下发重启基站的指令，基站重启以后才能进行正常工作的状态。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0DA)"
          },
          "FreqOffsetSwitch": {
            "type": "string",
            "description": "0:关闭 1：打开"
          },
          "Res[3]": {
            "type": "string",
            "description": "保留字节"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "FreqOffsetSwitch",
          "Res[3]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "freq_offset_cfg",
      "description": "设置频偏。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF059)"
          },
          "FreqOffset": {
            "type": "integer",
            "description": "频偏 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "FreqOffset"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "agc_set",
      "description": "FDD有效。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF079)"
          },
          "AgcFlag": {
            "type": "integer",
            "description": "是否开启AGC (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "AgcFlag"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "imsi_trans_kenc_cfg_ack",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF08C)"
          },
          "ImsiKenc[16]": {
            "type": "string",
            "description": "IMSI加密密钥"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "ImsiKenc[16]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "roll_carrier_cfg",
      "description": "wrRollCarrierCfg:",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0F0)"
          },
          "rollCarrierCfgNum": {
            "type": "string",
            "description": "需要配置的轮循载波数，0表示无载波配置"
          },
          "Res[3]": {
            "type": "string",
            "description": "预留"
          },
          "stRollCarrierCfg[8]": {
            "type": "string",
            "description": "手动轮循配置参数"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "rollCarrierCfgNum",
          "Res[3]",
          "stRollCarrierCfg[8]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "pwr1_derease_cfg",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0F4)"
          },
          "pwr1Derease": {
            "type": "string",
            "description": "配置0则为F015配置与F0A7生效值，配置1则在上面两条消息的基础上-1Db,2则为-2Db,19则为-19Db,20则输出功率为最小255"
          },
          "Res[3]": {
            "type": "string",
            "description": "预留"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "pwr1Derease",
          "Res[3]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "redirect_info_cfg",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0E6)"
          },
          "OnOff": {
            "type": "integer",
            "description": "重定向开关 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "UnicomEarfcn": {
            "type": "integer",
            "description": "4G联通重定向频点 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "TelecomEarfcn": {
            "type": "integer",
            "description": "4G电信重定向频点 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "OnOff",
          "UnicomEarfcn",
          "TelecomEarfcn"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "ue_info_rpt_ack",
      "description": "小区激活以后，基站采集接入用户信息并立即上报给客户端，只有开启IMEI捕获功能的时，才会上报IMEI。 主控板用户接口： 围栏版本： 非主控板用户接口——围栏版本： 说明：对于4.7.28中的配置，按照UploadImsiType取1要求并且非主控模式才进行序列号分配，才进行ACK回复。 非主控板用户ACK接口：",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF096)"
          },
          "seqNum": {
            "type": "integer",
            "description": "序列号：等于O_FL_ENB_TO_LMT_UE_INFO_RPT消息中seqNum字段 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "seqNum"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "base_info_query",
      "description": "此消息用于客户端查询一些基站的基本信息，比如版本号，MAC地址，SN等。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF02B)"
          },
          "u32EnbBaseInfoType": {
            "type": "integer",
            "description": "查询信息的类型， (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "u32EnbBaseInfoType"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "get_arfcn",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "sync_info_query",
      "description": "此消息用于客户端查询基站当前的同步方式和同步状态。",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "cell_state_info_query",
      "description": "应答消息（eNB->LMT）： 此消息用于查询基站的小区状态信息。",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "rxgain_power_derease_query",
      "description": "应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "redirect_info_cfg_query",
      "description": "应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "self_active_cfg_pwr_on_query",
      "description": "应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "tdd_subframe_assignment_query",
      "description": "应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "gps_location_query",
      "description": "基站启动时，会自动获取GPS经纬度信息，客户端可通过此查询接口获取的经纬度信息。",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "tau_attach_reject_cause_query",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "gps1pps_query",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "selfcfg_arfcn_query",
      "description": "应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "select_freq_cfg_query",
      "description": "应答消息（eNB->LMT）： PinBandRelation：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "secondary_plmns_query",
      "description": "应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "control_list_query",
      "description": "应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF043)"
          },
          "ControlListType": {
            "type": "string",
            "description": "名单类型"
          },
          "Res[3]": {
            "type": "string",
            "description": "补充字节"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "ControlListType",
          "Res[3]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "meas_ue_cfg_query",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "upload_imsi_file_cfg_query",
      "description": "查询消息（LMT->eNB）： 应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "ntp_sync_state_query",
      "description": "查询消息（LMT->eNB）： 应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "gps_or_beidou_cfg_query",
      "description": "查询消息（LMT->eNB）： 应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "multi_band_rxgain_query",
      "description": "(0xF0CA) 该接口用于查询band和接收增益对应关系表。返回的结构体和配置的结构体一致。 bandRxgainMap",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "get_rx_params",
      "description": "该接口用于获取RX口的功率值，仅在小区去激活状态有效。",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "rem_port_query",
      "description": "应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "gps_satellite_signal_level_query",
      "description": "应答消息（eNB->LMT）： wrsvssignalpair",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "eutra_freq_query",
      "description": "查询消息（LMT->eNB）： 应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "qrxlevmin_value_query",
      "description": "查询消息（LMT->eNB）： 应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "get_enb_state",
      "description": "查询消息（LMT->eNB）： 应答消息（eNB->LMT）： 基站执行状态会实时上报，一般不需要查询。 应答信息格式见[基站执行状态实时上报](#_基站执行状态实时上报(0xF019))。",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "enb_ip_query",
      "description": "查询消息（LMT->eNB）： 应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "rem_cfg_query",
      "description": "查询消息（LMT->eNB）： 应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "roll_carrier_query",
      "description": "查询消息（LMT->eNB）： 应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "pwr1_derease_query",
      "description": "查询消息（LMT->eNB）： 应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "fdd_redirection_cfg_query",
      "description": "应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "tac_modify_req",
      "description": "此接口用于修改基站的当前TAC值。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF069)"
          },
          "TacValue": {
            "type": "integer",
            "description": " (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "TacValue"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "ra_access_query",
      "description": "此接口用于调试测试阶段，客户端查询基站调度UE性能。一般情况下， RrcConnCmpNum/RrcConnReqNum可以达到90%左右。",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "ra_access_empty_req",
      "description": "基站收到客户端发送的此指令，会把RrcConnReqNum和RrcConnCmpNum清0，重新开始统计。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF067)"
          }
        },
        "required": [
          "WrmsgHeaderInfo"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "gmt_query",
      "description": "如果gps锁定，可以查询gps获取到的时间，时间格式是GMT。",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "get_enb_log",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF071)"
          },
          "isCfgFtpServer": {
            "type": "string",
            "description": "0:不配置（使用取值范围，如下） 1:配置"
          },
          "FtpServerIp[16]": {
            "type": "string",
            "description": "FTP服务器IP, eg：“***********1”"
          },
          "Res [3]": {
            "type": "string",
            "description": "保留字节"
          },
          "FtpServerPort": {
            "type": "integer",
            "description": "FTP服务器端口号，例如21 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "FtpLoginNam[20]": {
            "type": "string",
            "description": "Ftp用户名， “KKK”"
          },
          "FtpPassword[10]": {
            "type": "string",
            "description": "Ftp登录密码，”123456 ”"
          },
          "FtpServerFilePath[66]": {
            "type": "string",
            "description": "上传文件放置目录,不支持中文目录名,目录以/结尾。 Eg: 欲放置文件于FTP服务器根目录下的filePath文件夹，完整的路径为：“/filePath/”"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "isCfgFtpServer",
          "FtpServerIp[16]",
          "Res [3]",
          "FtpServerPort",
          "FtpLoginNam[20]",
          "FtpPassword[10]",
          "FtpServerFilePath[66]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "sys_log_levl_query",
      "description": "应答消息（eNB -> LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "sys_log_levl_set",
      "description": "基站版本发布默认log级别stkLogLevel为0，dbgLogLevel为2。 调试测试定位问题时，一般stkLogLevel设置为4，dbgLogLevel设置为7。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF045)"
          },
          "isSetStkLogLev": {
            "type": "string",
            "description": "0: 不设置 1：设置"
          },
          "stkLogLevel": {
            "type": "string",
            "description": "0：不打印log 1：critical 2：error 3：warning 4：info 5：debug 6：Max_Lev"
          },
          "isSetDbgLogLev": {
            "type": "string",
            "description": "0:不设置 1：设置"
          },
          "DbgLogLevel": {
            "type": "string",
            "description": "0：不打印log 1：fatal 2：error 3：event 4：warning 5：info 6：debug 7：Max_Lev"
          },
          "isSetOamLogLev": {
            "type": "string",
            "description": "0:不设置 1：设置"
          },
          "oamLogLevel": {
            "type": "string",
            "description": "0:不打印log 1: exception 2:call_stack 3:fatal 4: critical 5: warning 6:trace_info 7:trace_verbose 8:Max_lev"
          },
          "isSetGpsLogSwitch": {
            "type": "string",
            "description": "0:不设置 1：设置"
          },
          "gpsLogSwitch": {
            "type": "string",
            "description": "0: 关闭 1：打开"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "isSetStkLogLev",
          "stkLogLevel",
          "isSetDbgLogLev",
          "DbgLogLevel",
          "isSetOamLogLev",
          "oamLogLevel",
          "isSetGpsLogSwitch",
          "gpsLogSwitch"
        ]
      }
    }
  }
]

--- Generated LLM Tools Schema ---
[
  {
    "type": "function",
    "function": {
      "name": "sys_arfcn_cfg",
      "description": "此接口用于配置建立小区相关参数配置，在小区激活态配置此消息，基站会执行先去激活再激活的流程；在小区IDLE态下配置此消息，基站会直接执行激活小区的流程。 ServingCellCfgInfo: 备注1： 终端最大发射功率对应系统消息SIB1中P-Max，表示小区允许UE的最大发射功率，一般设置为23，表示23dBm。 备注2： 基站最大发射功率对应系统广播消息SIB2中的referenceSignalPower。此值的设置从加功放之后的总输出功率计算而来，用于终端计算路损，不会影响单板的输出功率。一般设置为20dBm（20W），此值相对于其他功率会比较大，但是经过测试，对基站性能影响不大，可以不用修改。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头（0xF003）"
          },
          "stServingCellCfgInfo": {
            "type": "string",
            "description": "小区信息配置"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "stServingCellCfgInfo"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "rem_ant_cfg",
      "description": "TDD模式： wholeBandRem字段指示是否开启全频段扫频，若开启全频段扫频(wholeBandRem=1)，板卡在扫完配置的频点后，继续搜索公网小区SIB5中配置的其他邻区频点。 基站单板本身支持的扫频范围是（(50MHZ~4GHz)，，但若整机系统中RX或者SINNIFFER口外接了限制接收频率的硬件（比如滤波器），则配置频点时应仅限于属于该频段的频点，且配置wholeBandRem为0。 \\*注：TDD模式下，空口同步、频偏校准和自配置流程会修改扫频频点配置 FDD模式： 此接口用于客户端在基站IDLE态时开始SCAN公网LTE小区参数的流程。无需配置Band ID，基站会根据频点自动计算。FDD模式只支持用SNF端口扫频，基站默认版本是SNF口。 wholeBandRem字段指示是否开启全频段扫频，若开启全频段扫频(wholeBandRem=1)，板卡在扫完配置的频点后，继续搜索公网小区SIB5中配置的其他邻区频点。 \\*注：FDD模式下，服务小区参数配置和频偏校准流程会修改扫频频点配置",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头（0xF009）"
          },
          "wholeBandRem": {
            "type": "integer",
            "description": "是否开启全频段扫频 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "sysEarfcnNum": {
            "type": "integer",
            "description": "扫频频点数目 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "sysEarfcn[10]": {
            "type": "integer",
            "description": "频点，如38400等 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "wholeBandRem",
          "sysEarfcnNum",
          "sysEarfcn[10]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "rem_ant_cfg",
      "description": "此接口仅用于配置TDD扫频端口，目前支持RX和SINNIFER 2个端口模式。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF07D)"
          },
          "RxorSnf": {
            "type": "integer",
            "description": "端口类型 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "RxorSnf"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "reboot_cfg",
      "description": "此接口用于客户端指示基站执行reboot操作。基站收到此消息，先回复ACK，再执行reboot。基站处于任何状态都会处理该消息。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头0xF00B"
          },
          "SelfActiveCfg": {
            "type": "integer",
            "description": "指示基站重启后是否采用现有参数配置自动激活小区，该字段只有在定位版本中生效，围栏版本不需要判断该字节。 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "SelfActiveCfg"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "set_admin_state_cfg",
      "description": "在基站IDLE状态下，可通过此消息指示基站采用当前小区配置参数激活小区，如果workAdminState配置为1，TDD基站则不进行同步流程，直接激活小区，如果workAdminState配置为2；TDD基站先执行同步流程，同步成功后再激活小区，如果同步失败，基站仍然回到IDLE状态。 在基站激活状态下，通过配置workAdminState为0，基站则会执行去激活小区的操作，进入IDLE状态。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF00D)"
          },
          "workAdminState": {
            "type": "integer",
            "description": "/ (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "workAdminState"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "sys_rxgain_cfg",
      "description": "该接口用于配置基站9361寄存器的接收增益，表示将接收到的来自UE的信号放大多少倍。接收增益配置值说明参考《[2.4 接收增益](#_接收增益)》。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF013)"
          },
          "Rxgain": {
            "type": "integer",
            "description": "接收增益 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "RxGainSaveFlag": {
            "type": "string",
            "description": "配置值是否保存到配置，重启之后也保留"
          },
          "RxOrSnfFlag": {
            "type": "string",
            "description": "配置该增益是修改rx口增益还是snf口增益 注：仅FDD有效 对于TDD，该字段无意义，基站不做判断。"
          },
          "Res[2]": {
            "type": "string",
            "description": "保留字节"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "Rxgain",
          "RxGainSaveFlag",
          "RxOrSnfFlag",
          "Res[2]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "sys_pwr1_derease_cfg",
      "description": "该接口用于配置基站发送通道的衰减值，用于客户校准整机输出功率。衰减值每加4，基站输出功率增加1dB衰减。无衰减时，即衰减值为0x00时，基站输出功率范围在-1dbm~-2dbm，每块单板会有差异。 基站实际输出功率 = 零衰减功率 - 衰减值（Pwr1Derease\\*0.25） 例如：基站输出功率为-1dB，当衰减值设置为0x28，输出功率为-11dBm。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF015)"
          },
          "Pwr1Derease": {
            "type": "integer",
            "description": "功率衰减，每步长代表0.25dB (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "IsSave": {
            "type": "string",
            "description": "配置值是否保存到配置，重启之后也保留"
          },
          "Res [3]": {
            "type": "string",
            "description": "保留字节"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "Pwr1Derease",
          "IsSave",
          "Res [3]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "ip_cfg",
      "description": "该接口用于修改基站的IP配置。 版本默认基站地址是“************#*************#***********#”。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF01B)"
          },
          "eNBIPStr[52]": {
            "type": "string",
            "description": "设置基站的IP"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "eNBIPStr[52]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "gps_pp1s_cfg",
      "description": "在TDD选择GPS同步模式下，此接口用于设置同步偏移量，在中国，一般band39,band40需要进行GPS同步偏移量调节,一般-700微秒（OffsetTime）左右数据帧头偏移（正值说明时域相对原始值向后移动，负值说明是时域对应原始值向前移动），具体各个BAND的偏移量以实际测量为准。 接口中设置的Gpspps1s = OffsetTime \\* （Gpspps1sToBW/微秒） OffsetTime：运营商网络此BAND相对于GPS的偏移量，单位微秒； Gpspps1sToBW/微秒：相关带宽下每微秒的偏移值，带宽是指本基带板的带宽； 1微秒的偏移情况下，Gpspps1s与带宽对应关系如下： 例如：基站配置20M带宽，BAND40偏移-700微妙，则接口中配置的Gpspps1s=-700\\*30.72。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF029)"
          },
          "Gpspps1s": {
            "type": "string",
            "description": "Gps pps1s偏移量"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "Gpspps1s"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "self_active_cfg_pwr_on",
      "description": "上电自激活配置： 用于配置基站上电启动时是否执行自动激活小区的流程，默认版本中上电不自动激活小区，进入IDLE状态。 Reboot配置： Wl模式下，用于配置除带宽改变时，reboot是否执行自动激活小区的流程，默认版本中reboot自动激活小区，进入active状态。 ;",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF03B)"
          },
          "SelfActiveCfg": {
            "type": "integer",
            "description": "/ (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "rebootSelfActiveCfg": {
            "type": "integer",
            "description": "仅WL版本有效 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "SelfActiveCfg",
          "rebootSelfActiveCfg"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "tdd_subframe_assignment_set",
      "description": "此消息接口用于配置TDD小区的子帧配比，小区去激活状态下配置立即生效。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF049)"
          },
          "u8TddSfAssignment;": {
            "type": "string",
            "description": "TDD子帧配比"
          },
          "u8TddSpecialSfPatterns": {
            "type": "string",
            "description": "TDD特殊子帧配比"
          },
          "Res[2]": {
            "type": "string",
            "description": "保留字节"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "u8TddSfAssignment;",
          "u8TddSpecialSfPatterns",
          "Res[2]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "gps_info_reset",
      "description": "基站启动时，会自动获取GPS经纬度信息，获取的经纬度信息可通过4.9章节的《[GPS经纬高度查询](#_Gps经纬高度查询)》接口查询，一旦获取到经纬度信息，基站会保存此次获取的值，下次重启将不再重复获取，因此如果基站移动了位置，请使用此接口清除上一次获取的经纬度信息。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF06D)"
          }
        },
        "required": [
          "WrmsgHeaderInfo"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "secondary_plmns_set",
      "description": "此接口用于配置基站广播SIB1 中PLMN LIST字段中的非主PLMN。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF060)"
          },
          "u8SecPLMNNum": {
            "type": "string",
            "description": "辅PLMN的数目"
          },
          "u8SecPLMNList[5][7]": {
            "type": "string",
            "description": "辅PLMN列表"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "u8SecPLMNNum",
          "u8SecPLMNList[5][7]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "selfcfg_cellpara_req",
      "description": "此接口用于指示基站在IDLE态开始小区自配置流程，小区自配置流程图参考《[小区自配置流程](#_小区自配置流程)》。 基站收到此消息，根据自配置频点列表搜索公网频点和小区信息，基站会根据扫频结果，选择本小区参数自动建立小区。 如果整机设备支持全频段，可以配置SelfBand为0xFF，基站将会对自配置频点列表中所有频点以及公网广播消息SIB5中的频点进行全频段扫频。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF04F)"
          },
          "SelfBand": {
            "type": "string",
            "description": "指定自配置的频段"
          },
          "Res[3]": {
            "type": "string",
            "description": "保留字节"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "SelfBand",
          "Res[3]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "rem_mode_cfg",
      "description": "此接口用于设置基站的同步方式，目前仅支持空口和GPS同步。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF023)"
          },
          "Remmode": {
            "type": "integer",
            "description": "TDD模式支持空口和GPS同步，FDD仅支持GPS，用于频率同步。 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "Remmode"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "selfcfg_arfcn_cfg_req",
      "description": "此接口用于配置小区自配置功能的扫频频点列表。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF051)"
          },
          "Cfgtype": {
            "type": "integer",
            "description": " (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "EarfcnValue": {
            "type": "integer",
            "description": "频点值 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "Cfgtype",
          "EarfcnValue"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "sys_arfcn_mod",
      "description": "此接口用于在小区激活态下，即时修改小区参数，但是此时修改的小区参数重启或者断电之后不会保存。如果当前小区没有激活，会返回配置失败。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头 (0xF080)"
          },
          "ulEarfcn": {
            "type": "integer",
            "description": "上行频点 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "dlEarfcn": {
            "type": "integer",
            "description": "下行频点 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "PLMN[7 ]": {
            "type": "string",
            "description": "plmn"
          },
          "Band": {
            "type": "string",
            "description": "频段"
          },
          "CellId": {
            "type": "integer",
            "description": "小区Id (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "UePMax": {
            "type": "integer",
            "description": "终端最大发射功率 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "ulEarfcn",
          "dlEarfcn",
          "PLMN[7 ]",
          "Band",
          "CellId",
          "UePMax"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "ntp_server_ip_cfg",
      "description": "此接口用于设置基站NTP时间同步的NTP服务器IP，系统启动时会自动进行NTP时间同步。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": ""
          },
          "ntpServerIp[20]": {
            "type": "string",
            "description": ""
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "ntpServerIp[20]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "time_to_reset_cfg",
      "description": "此接口用于配置基站是否开启定点重启功能，基站系统采用NTP同步方式获取系统格林威治时间，如果开启此功能，请设置正确的NTP服务器IP。 版本发布默认此功能关闭。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF086)"
          },
          "ResetSwitch": {
            "type": "string",
            "description": "定点重启开关"
          },
          "Res[3]": {
            "type": "string",
            "description": "保留字节"
          },
          "ResetTime[12]": {
            "type": "string",
            "description": "重启时间配置 例如：“23：15：15” 格林威治时间"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "ResetSwitch",
          "Res[3]",
          "ResetTime[12]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "imei_request_cfg",
      "description": "此接口用于配置基站是否开启获取UE的IMEI功能，由于IMEI的获取，基站会对接入的UE先释放让其重新接入，会影响高速抓号的成功率，因此版本默认是关闭功能，客户可根据自己的需要决定是否开启此功能。根据测试，获取UE IMEI相对于IMSI的比例大概是10~15%。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF08A)"
          },
          "ImeiEnable": {
            "type": "string",
            "description": "是否开启IMEI获取功能"
          },
          "Res[3]": {
            "type": "string",
            "description": "保留字节"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "ImeiEnable",
          "Res[3]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "select_freq_cfg",
      "description": "基站根据当前激活小区的BAND输出不同的GPIO信号，用于客户一个单板切换不同的BAND建立小区时根据该GPIO信号匹配不同的功放。输出GPIO信号的管脚以及配置描述见下图。目前V2 Board提供2根GPIO管脚，V3 Board以及后面型号的Board提供4根GPIO管脚，配置接口兼容支持，请根据不同的Board配置值范围。 PinBandRelation： 版本默认发布BAND与其PIN脚的关系见下图： V2单板默认关系： V3单板默认关系： V5-C单板默认关系： V6单板默认关系：",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF082)"
          },
          "PinBandRelaNum": {
            "type": "integer",
            "description": "指定结构体数组元素个数。 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "pinBandRelaMap[15]": {
            "type": "string",
            "description": "管脚频带关系表"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "PinBandRelaNum",
          "pinBandRelaMap[15]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "control_ue_list_cfg",
      "description": "此接口用于配置IMSI的黑名单和白名单，每次最大可以配置10个IMSI，基站可同时保存维护黑名单和白名单两套名单，最大支持各100个名单配置。根据测量UE的配置模式决定采用哪个名单。 备注1： 此消息会根据IgnoreUENum数组包，请根据消息头中MsgLen和IgnoreUENum解析数据",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF039)"
          },
          "ControlMovement": {
            "type": "string",
            "description": ""
          },
          "ControlUENum": {
            "type": "string",
            "description": "添加/删除UE数目"
          },
          "ControlUEProperty": {
            "type": "string",
            "description": ""
          },
          "ControlUEIdentity[10][C_MAX_IMSI_LEN]": {
            "type": "string",
            "description": "UE IMSI数组"
          },
          "ClearType": {
            "type": "string",
            "description": "清除黑白名单配置"
          },
          "RejCause": {
            "type": "string",
            "description": "黑/白名单对应的TAU REJECT原因值（仅对重定向模式子模式0生效，名单内外可以使用不同的RejCause）"
          },
          "Res": {
            "type": "string",
            "description": "保留字节"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "ControlMovement",
          "ControlUENum",
          "ControlUEProperty",
          "ControlUEIdentity[10][C_MAX_IMSI_LEN]",
          "ClearType",
          "RejCause",
          "Res"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "tau_attach_reject_cause_cfg",
      "description": "此接口用于配置基站把接入UE踢回公网时，回复UE的TAU REJECT或者ATTACH REJECT消息中的reject cause值，基站默认使用cause#15，一般此值不需要修改。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF057)"
          },
          "RejectCause": {
            "type": "integer",
            "description": "回复UE的TAU REJECT或者ATTACH REJECT消息中的reject cause值 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "RejectCause"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "redirect_info_cfg",
      "description": "此接口用于配置基站发送给UE的释放消息中是否携带重定向参数，默认不携带重定向参数。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF017)"
          },
          "OnOff": {
            "type": "integer",
            "description": "重定向开关 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "Earfcn": {
            "type": "integer",
            "description": "重定向频点 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "RedirectType": {
            "type": "integer",
            "description": "重定向类型 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "OnOff",
          "Earfcn",
          "RedirectType"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "sys_mode_cfg",
      "description": "此消息用于设置系统模式（TDD/FDD），基站收到此配置，记录启动模式， 客户端下发reboot指令或者重新上下电给基站，基站会根据配置的模式启动系统。 当sysMode=2时，系统根据硬件一个GPIO（PIN5）管脚的输入信号决定启动TDD还是FDD，PINI5悬空时启动TDD，PIN5接地时启动FDD，仅V3系列板卡支持此功能。 仅TDD和FDD共版版本才支持此设置，非共版版本基站会回复失败。 PIN5位置图如下： ![](data:image/png;base64...)",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "update_soft_version_cfg",
      "description": "此接口用于客户端配置升级基站版本使用，需要客户端建立FTP服务器。 UpgradeStatusId:",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF06F)"
          },
          "updateType": {
            "type": "string",
            "description": "升级版本类型"
          },
          "enbSoftFileName[102]": {
            "type": "string",
            "description": "字符串，基站软件版本名字：如 “BaiStation128D_ FDD_R002C0000G01B005.IMG”；"
          },
          "isReservedCfg": {
            "type": "string",
            "description": "是否保留配置（仅对基站软件系统有效）"
          },
          "enbSoftMD5 [36]": {
            "type": "string",
            "description": "针对升级基站软件计算的md5值,32字节长度。"
          },
          "uBootFileName[40]": {
            "type": "string",
            "description": "该字段为boot文件名，如：“u-boot-t2200-nand-1.0.15.img”"
          },
          "ubootMD5 [36]": {
            "type": "string",
            "description": "针对升级文件计算的md5值,32字节长度。"
          },
          "isCfgFtpServer": {
            "type": "string",
            "description": "是否重新配置FTP服务器地址"
          },
          "FtpServerIp[16]": {
            "type": "string",
            "description": "FTP服务器IP, eg: “***********1”，"
          },
          "Reserved[3]": {
            "type": "string",
            "description": "保留字节"
          },
          "FtpServerPort": {
            "type": "integer",
            "description": "FTP服务器端口号，eg: 21 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "FtpLoginNam[20]": {
            "type": "string",
            "description": "Ftp用户名，eg:“kkk ”"
          },
          "FtpPassword[10]": {
            "type": "string",
            "description": "Ftp登录密码, eg: “123456 ”"
          },
          "FtpServerFilePath[66]": {
            "type": "string",
            "description": "待升级文件所在FTP服务器路径，默认根目录。路径以/结尾。 eg：待升级文件位于FTP服务器根目录下的filePath文件夹，完整的路径为：“/filePath/”"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "updateType",
          "enbSoftFileName[102]",
          "isReservedCfg",
          "enbSoftMD5 [36]",
          "uBootFileName[40]",
          "ubootMD5 [36]",
          "isCfgFtpServer",
          "FtpServerIp[16]",
          "Reserved[3]",
          "FtpServerPort",
          "FtpLoginNam[20]",
          "FtpPassword[10]",
          "FtpServerFilePath[66]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "upload_imsi_file_cfg",
      "description": "此接口用于开启基站支持以IMSI文件上传的方式上报到客户端，使用FTP方式。 uploadImsiFileCfg:",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头定义(0xF077)"
          },
          "UploadImsiFileCfg": {
            "type": "string",
            "description": ""
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "UploadImsiFileCfg"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "ue_redirect_imsi_list_cfg",
      "description": "本接口用于重定向模式下，配置IMSI黑名单，单次配置最多20，累次配置总数达到1000，删除时间节点配置靠前的IMSI。 ClearImsiListFlag取1并且AddImsiNum非零同时配置，先清空列表再添加。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头定义(0xF08E)"
          },
          "ClearImsiListFlag": {
            "type": "string",
            "description": "0:保留当前所有配置 1:清空所有IMSI配置列表"
          },
          "AddImsiNum": {
            "type": "string",
            "description": "增加的IMSI条数;"
          },
          "ImsiStr[C_MAX_UE_REDIRECT_IMSI_ADD_NUM][C_MAX_IMSI_LEN]": {
            "type": "string",
            "description": "字符串数组，如： \"460011111111111\" 非有效UE ID位注意置为'\\0'。 (该字段为固定长度)"
          },
          "Res[2]": {
            "type": "string",
            "description": "预留"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "ClearImsiListFlag",
          "AddImsiNum",
          "ImsiStr[C_MAX_UE_REDIRECT_IMSI_ADD_NUM][C_MAX_IMSI_LEN]",
          "Res[2]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "rem_mode_cfg",
      "description": "该接口用于FDD 模式下（FDD的GPS同步功能只用于校准频偏，小区激活态下有效，同小区激活顺序无关）： 此消息接口用于当gps同步不成功时，重新配置gps去做同步，不需要重新建立小区。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头定义(0xF090)"
          }
        },
        "required": [
          "WrmsgHeaderInfo"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "ul_power_control_alpha_cfg",
      "description": "此消息接口用于当FDD开启配置TDD/FDD小区的SIB2中的上行功控系数，小区去激活状态下配置立即生效。 本接口配置TDD出厂配置默认值为70，FDD默认出厂值为80。在空旷地带测试抓号，建议该值配置成80。 注：配置的（0，40，50，60，70，80，90，100）分别对应sib2信息alpha值的（0，1，2，3，4，5，6，7）。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头定义(0xF092)"
          },
          "UlPowerAlpha": {
            "type": "string",
            "description": "Sib2中上行功控系数"
          },
          "Res[3]": {
            "type": "string",
            "description": "预留"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "UlPowerAlpha",
          "Res[3]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "gps_or_beidou_cfg",
      "description": "此消息接口用于配置gps芯片选择gps或者北斗，配置完重启生效。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF097)"
          },
          "u8FlagInUse": {
            "type": "string",
            "description": ""
          },
          "Res[3]": {
            "type": "string",
            "description": "保留字节"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "u8FlagInUse",
          "Res[3]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "pinx_switch_cfg",
      "description": "此接口用于控制V3单板PIN1管脚的电平输出，V5板IO6管脚输出控制，以及V6板IO5管脚输出控制。 单板自主判断当前板卡类型，若是V3则该接口控制PIN1输出，若是V5板卡，控制IO6电平输出，若是V6板卡，控制IO5电平输出。 V3对应板卡上扩展IO1里的IO1（J7-IO1）；V5对应板卡上IO6；V6对应板卡上IO5。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF09F)"
          },
          "PinxSwitch": {
            "type": "string",
            "description": "0: 输出低电平 1：输出高电平"
          },
          "Reserved[3]": {
            "type": "string",
            "description": "预留"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "PinxSwitch",
          "Reserved[3]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "multi_band_powerderease_cfg",
      "description": "该接口用于配置不同band的小区时，基站根据band，查询关系表，配置衰减值。 出厂默认关闭该功能。 bandPwrdereaseMap",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0A7)"
          },
          "NumElem": {
            "type": "string",
            "description": "0: 关闭功能 >0:配置相关关系及关系个数"
          },
          "Reserved[3]": {
            "type": "string",
            "description": "预留"
          },
          "BandPwrdereaseMap[32]": {
            "type": "string",
            "description": "Band和衰减值对应关系"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "NumElem",
          "Reserved[3]",
          "BandPwrdereaseMap[32]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "multi_band_rxgain_cfg",
      "description": "该接口用于配置不同band的小区时，基站根据band，查询关系表，配置接收增益值。 不支持增量配置。若建小区的band无法在表中查找到对应接收增益值，默认输出接收增益值0xFF。 出厂默认关闭该功能。 bandRxgainMap",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0C8)"
          },
          "NumElem": {
            "type": "string",
            "description": "0: 关闭功能 >0:配置相关关系及关系个数"
          },
          "Reserved[3]": {
            "type": "string",
            "description": "预留"
          },
          "BandRxgainMap[32]": {
            "type": "string",
            "description": "Band和增益值对应关系"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "NumElem",
          "Reserved[3]",
          "BandRxgainMap[32]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "sys_arfcn_cfg",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF05E)"
          },
          "UsingSnfSwitch": {
            "type": "string",
            "description": "0: 使用Rx同步 1：使用SNF同步 2: 取消异频同步"
          },
          "Reserved1[3]": {
            "type": "string",
            "description": "保留字节"
          },
          "SyncArfcn": {
            "type": "integer",
            "description": "同步频点 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "TimeOffsetPresent": {
            "type": "string",
            "description": "0：不需要配置 1：需要配置"
          },
          "Reserved2[3]": {
            "type": "string",
            "description": "保留字节"
          },
          "S32": {
            "type": "string",
            "description": "异频同步时偏值（不同band间）"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "UsingSnfSwitch",
          "Reserved1[3]",
          "SyncArfcn",
          "TimeOffsetPresent",
          "Reserved2[3]",
          "S32"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "msg4_power_boost_cfg",
      "description": "此接口用于配置MSG4功率是否抬升，TDD生效。默认出厂设置为不抬升，在不同环境下测试时，如出现接入成功率不理想，可以配置抬升MSG4功率。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0AF)"
          },
          "Msg4PowerBoost": {
            "type": "integer",
            "description": "0：不抬升（出厂默认值） 1：抬升 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "Msg4PowerBoost"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "gps_software_renovate_cfg",
      "description": "此接口主要用于当gps信号很好的场景下反复无法同步成功，搜不到星的场景，可以尝试将GPS固件彻底复位，复位时间比较长，等收到响应后，重启生效。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0D2)"
          }
        },
        "required": [
          "WrmsgHeaderInfo"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "set_qrxlevmin",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF021)"
          },
          "s8QrxlevMin": {
            "type": "string",
            "description": ""
          },
          "Reserved1[3]": {
            "type": "string",
            "description": "保留字节"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "s8QrxlevMin",
          "Reserved1[3]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "set_sys_tmr",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF01F)"
          },
          "Time[20]": {
            "type": "string",
            "description": "单板系统时间设置"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "Time[20]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "build_cell_cfg",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0CC)"
          },
          "buildFlag": {
            "type": "integer",
            "description": "0: 同步失败不建小区； 1：同步失败强制激活小区。（GPS同步失败1次，空口同步失败3次以上） (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "buildFlag"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "gps_loss_reboot_tmr_cfg",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0CE)"
          },
          "value": {
            "type": "integer",
            "description": "单位：分钟 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "value"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "gps_src_sel",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0C6)"
          },
          "gpsSel": {
            "type": "string",
            "description": "0:无源 1:有源 默认有源"
          },
          "Res[3]": {
            "type": "string",
            "description": ""
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "gpsSel",
          "Res[3]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "ftp_get_put_cfg",
      "description": "使用该接口，可以通过FTP下发管控黑白名单，黑名单文件命名格式black.txt,白名单文件名格式white.txt,文件中每个imis一行，文件中不要有空行，最多支持100000个imsi。 isCfgFtp: 0:FTP相关配置仅本次下发有效，重启后不生效 1:FTP相关配置保存在本地，下次不需要再次配置 actionType： 1:通过FTP配置管控黑白名单 2:通过FTP上传当前黑白名单配置文件 ftpGetPutCfg：",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0D0)"
          },
          "ftpCfg": {
            "type": "string",
            "description": "上传下载相关配置"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "ftpCfg"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "eutra_freq_cfg",
      "description": "此接口用于添加小区SIB5中异频信息，填写下行频点号。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0E2)"
          },
          "EutraArfcnNumber": {
            "type": "integer",
            "description": "配置下行频点数量 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "EutraArfcn[C_MAX_EUTRA_ARFCN]": {
            "type": "integer",
            "description": " (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "EutraArfcnNumber",
          "EutraArfcn[C_MAX_EUTRA_ARFCN]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "selfcfg_arfcn_cfg_req",
      "description": "基站收到此条消息，设置校准开关打开，重启基站，基站重启以后会开始频偏校准过程。 频偏校准过程相关流程如下： 然后客户端下发频偏校准开关（0xF0DA）,打开频偏校准功能； 基站收到0xF0DA消息会上报状态：“频偏校准开始”（0xF019），然后重启基站。 基站重启以后则开始频偏校准流程，上报状态：“频偏校准进行中”（0xF019）。校准过程中会有扫频状态和扫频结果上报。 频偏校准结束，上报状态：“频偏校准结束”（0xF019），同时上报频偏校准结果（0xF0DC），复位校准开关。 校准结束以后，需要客户端下发重启基站的指令，基站重启以后才能进行正常工作的状态。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0DA)"
          },
          "FreqOffsetSwitch": {
            "type": "string",
            "description": "0:关闭 1：打开"
          },
          "Res[3]": {
            "type": "string",
            "description": "保留字节"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "FreqOffsetSwitch",
          "Res[3]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "freq_offset_cfg",
      "description": "设置频偏。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF059)"
          },
          "FreqOffset": {
            "type": "integer",
            "description": "频偏 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "FreqOffset"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "agc_set",
      "description": "FDD有效。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF079)"
          },
          "AgcFlag": {
            "type": "integer",
            "description": "是否开启AGC (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "AgcFlag"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "imsi_trans_kenc_cfg_ack",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF08C)"
          },
          "ImsiKenc[16]": {
            "type": "string",
            "description": "IMSI加密密钥"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "ImsiKenc[16]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "roll_carrier_cfg",
      "description": "wrRollCarrierCfg:",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0F0)"
          },
          "rollCarrierCfgNum": {
            "type": "string",
            "description": "需要配置的轮循载波数，0表示无载波配置"
          },
          "Res[3]": {
            "type": "string",
            "description": "预留"
          },
          "stRollCarrierCfg[8]": {
            "type": "string",
            "description": "手动轮循配置参数"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "rollCarrierCfgNum",
          "Res[3]",
          "stRollCarrierCfg[8]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "pwr1_derease_cfg",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0F4)"
          },
          "pwr1Derease": {
            "type": "string",
            "description": "配置0则为F015配置与F0A7生效值，配置1则在上面两条消息的基础上-1Db,2则为-2Db,19则为-19Db,20则输出功率为最小255"
          },
          "Res[3]": {
            "type": "string",
            "description": "预留"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "pwr1Derease",
          "Res[3]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "redirect_info_cfg",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF0E6)"
          },
          "OnOff": {
            "type": "integer",
            "description": "重定向开关 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "UnicomEarfcn": {
            "type": "integer",
            "description": "4G联通重定向频点 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "TelecomEarfcn": {
            "type": "integer",
            "description": "4G电信重定向频点 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "OnOff",
          "UnicomEarfcn",
          "TelecomEarfcn"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "ue_info_rpt_ack",
      "description": "小区激活以后，基站采集接入用户信息并立即上报给客户端，只有开启IMEI捕获功能的时，才会上报IMEI。 主控板用户接口： 围栏版本： 非主控板用户接口——围栏版本： 说明：对于4.7.28中的配置，按照UploadImsiType取1要求并且非主控模式才进行序列号分配，才进行ACK回复。 非主控板用户ACK接口：",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF096)"
          },
          "seqNum": {
            "type": "integer",
            "description": "序列号：等于O_FL_ENB_TO_LMT_UE_INFO_RPT消息中seqNum字段 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "seqNum"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "base_info_query",
      "description": "此消息用于客户端查询一些基站的基本信息，比如版本号，MAC地址，SN等。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF02B)"
          },
          "u32EnbBaseInfoType": {
            "type": "integer",
            "description": "查询信息的类型， (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "u32EnbBaseInfoType"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "get_arfcn",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "sync_info_query",
      "description": "此消息用于客户端查询基站当前的同步方式和同步状态。",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "cell_state_info_query",
      "description": "应答消息（eNB->LMT）： 此消息用于查询基站的小区状态信息。",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "rxgain_power_derease_query",
      "description": "应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "redirect_info_cfg_query",
      "description": "应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "self_active_cfg_pwr_on_query",
      "description": "应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "tdd_subframe_assignment_query",
      "description": "应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "gps_location_query",
      "description": "基站启动时，会自动获取GPS经纬度信息，客户端可通过此查询接口获取的经纬度信息。",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "tau_attach_reject_cause_query",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "gps1pps_query",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "selfcfg_arfcn_query",
      "description": "应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "select_freq_cfg_query",
      "description": "应答消息（eNB->LMT）： PinBandRelation：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "secondary_plmns_query",
      "description": "应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "control_list_query",
      "description": "应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF043)"
          },
          "ControlListType": {
            "type": "string",
            "description": "名单类型"
          },
          "Res[3]": {
            "type": "string",
            "description": "补充字节"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "ControlListType",
          "Res[3]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "meas_ue_cfg_query",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "upload_imsi_file_cfg_query",
      "description": "查询消息（LMT->eNB）： 应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "ntp_sync_state_query",
      "description": "查询消息（LMT->eNB）： 应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "gps_or_beidou_cfg_query",
      "description": "查询消息（LMT->eNB）： 应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "multi_band_rxgain_query",
      "description": "(0xF0CA) 该接口用于查询band和接收增益对应关系表。返回的结构体和配置的结构体一致。 bandRxgainMap",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "get_rx_params",
      "description": "该接口用于获取RX口的功率值，仅在小区去激活状态有效。",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "rem_port_query",
      "description": "应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "gps_satellite_signal_level_query",
      "description": "应答消息（eNB->LMT）： wrsvssignalpair",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "eutra_freq_query",
      "description": "查询消息（LMT->eNB）： 应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "qrxlevmin_value_query",
      "description": "查询消息（LMT->eNB）： 应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "get_enb_state",
      "description": "查询消息（LMT->eNB）： 应答消息（eNB->LMT）： 基站执行状态会实时上报，一般不需要查询。 应答信息格式见[基站执行状态实时上报](#_基站执行状态实时上报(0xF019))。",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "enb_ip_query",
      "description": "查询消息（LMT->eNB）： 应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "rem_cfg_query",
      "description": "查询消息（LMT->eNB）： 应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "roll_carrier_query",
      "description": "查询消息（LMT->eNB）： 应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "pwr1_derease_query",
      "description": "查询消息（LMT->eNB）： 应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "fdd_redirection_cfg_query",
      "description": "应答消息（eNB->LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "tac_modify_req",
      "description": "此接口用于修改基站的当前TAC值。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF069)"
          },
          "TacValue": {
            "type": "integer",
            "description": " (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "TacValue"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "ra_access_query",
      "description": "此接口用于调试测试阶段，客户端查询基站调度UE性能。一般情况下， RrcConnCmpNum/RrcConnReqNum可以达到90%左右。",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "ra_access_empty_req",
      "description": "基站收到客户端发送的此指令，会把RrcConnReqNum和RrcConnCmpNum清0，重新开始统计。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF067)"
          }
        },
        "required": [
          "WrmsgHeaderInfo"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "gmt_query",
      "description": "如果gps锁定，可以查询gps获取到的时间，时间格式是GMT。",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "get_enb_log",
      "description": "",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF071)"
          },
          "isCfgFtpServer": {
            "type": "string",
            "description": "0:不配置（使用取值范围，如下） 1:配置"
          },
          "FtpServerIp[16]": {
            "type": "string",
            "description": "FTP服务器IP, eg：“***********1”"
          },
          "Res [3]": {
            "type": "string",
            "description": "保留字节"
          },
          "FtpServerPort": {
            "type": "integer",
            "description": "FTP服务器端口号，例如21 (无符号32位整数, 范围: 0 ~ 4294967295)",
            "minimum": 0,
            "maximum": 4294967295
          },
          "FtpLoginNam[20]": {
            "type": "string",
            "description": "Ftp用户名， “KKK”"
          },
          "FtpPassword[10]": {
            "type": "string",
            "description": "Ftp登录密码，”123456 ”"
          },
          "FtpServerFilePath[66]": {
            "type": "string",
            "description": "上传文件放置目录,不支持中文目录名,目录以/结尾。 Eg: 欲放置文件于FTP服务器根目录下的filePath文件夹，完整的路径为：“/filePath/”"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "isCfgFtpServer",
          "FtpServerIp[16]",
          "Res [3]",
          "FtpServerPort",
          "FtpLoginNam[20]",
          "FtpPassword[10]",
          "FtpServerFilePath[66]"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "sys_log_levl_query",
      "description": "应答消息（eNB -> LMT）：",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "sys_log_levl_set",
      "description": "基站版本发布默认log级别stkLogLevel为0，dbgLogLevel为2。 调试测试定位问题时，一般stkLogLevel设置为4，dbgLogLevel设置为7。",
      "parameters": {
        "type": "object",
        "properties": {
          "WrmsgHeaderInfo": {
            "type": "string",
            "description": "消息头(0xF045)"
          },
          "isSetStkLogLev": {
            "type": "string",
            "description": "0: 不设置 1：设置"
          },
          "stkLogLevel": {
            "type": "string",
            "description": "0：不打印log 1：critical 2：error 3：warning 4：info 5：debug 6：Max_Lev"
          },
          "isSetDbgLogLev": {
            "type": "string",
            "description": "0:不设置 1：设置"
          },
          "DbgLogLevel": {
            "type": "string",
            "description": "0：不打印log 1：fatal 2：error 3：event 4：warning 5：info 6：debug 7：Max_Lev"
          },
          "isSetOamLogLev": {
            "type": "string",
            "description": "0:不设置 1：设置"
          },
          "oamLogLevel": {
            "type": "string",
            "description": "0:不打印log 1: exception 2:call_stack 3:fatal 4: critical 5: warning 6:trace_info 7:trace_verbose 8:Max_lev"
          },
          "isSetGpsLogSwitch": {
            "type": "string",
            "description": "0:不设置 1：设置"
          },
          "gpsLogSwitch": {
            "type": "string",
            "description": "0: 关闭 1：打开"
          }
        },
        "required": [
          "WrmsgHeaderInfo",
          "isSetStkLogLev",
          "stkLogLevel",
          "isSetDbgLogLev",
          "DbgLogLevel",
          "isSetOamLogLev",
          "oamLogLevel",
          "isSetGpsLogSwitch",
          "gpsLogSwitch"
        ]
      }
    }
  }
]

u32FrameHeader  U32  0x5555AAAA  消息帧头
u16MsgType      U16  MessageID   消息ID
u16MsgLength    U16  /           通信信息是可变长度
u16frame        U16  0xFF00:FDD  用于指示当前系统工作模式
                     0x00FF:TDD
u16SubSysCode   U16  1~65535     (1)最高1bit用于指示基站发送给客户端的数据是否传输完成，0：传输完成；1代表传输未完成。
                                 (2)低15bit：消息传输的TransId，0是无效值，1~0x8FFFF。
u8BoardSn[20]   U8   单板SN字符   基站单板的SN号，每块基站单板都不同。
```
  ,请根据以上文档，利用MCP SERVER 实现功能: 列出所有当前的功能; 能根据用户输入自然语言对该基站功能和配置的查询对话，例如：能帮我查询基站的SN吗？它现在的温度是多少？