const dgram = require('dgram');
const { encryptMessage, decryptMessage } = require('./protocol_crypto');
const {
    buildHeartbeatAck,
    buildAuthSerialQuery,
    buildAuthKeyCfg,
    buildSecondaryPlmnsSet,
    buildBaseInfoQuery,
    MSG_TYPE_HEARTBEAT_IND,
    MSG_TYPE_HEARTBEAT_ACK,
    MSG_TYPE_AUTH_SERIAL_QUERY,
    MSG_TYPE_AUTH_SERIAL_QUERY_ACK,
    MSG_TYPE_AUTH_KEY_CFG,
    MSG_TYPE_AUTH_KEY_CFG_ACK,
    MSG_TYPE_SECONDARY_PLMNS_SET,
    MSG_TYPE_SECONDARY_PLMNS_SET_ACK,
    MSG_TYPE_BASE_INFO_QUERY,
    MSG_TYPE_BASE_INFO_QUERY_ACK,
} = require('./protocol_pack');
const { connect, StringCodec } = require('nats'); // 引入 nats 客户端

class UdpServer {
    constructor() {
        this.server = null;
        this.isListening = false;
        this.devices = new Map(); // key: ip, value: {ip, port, sn, hasSN, heartbeatCount, ...}
        this.natsConnection = null;
        this.sc = StringCodec();
        this.enableLogging = true;
    }

    /**
     * Sets the function to send upgrade completion messages via SSE.
     * @param {function} sender - The function to send messages (from index.js).
     */
    setUpgradeCompletionSender(sender) {
        this.upgradeCompletionSender = sender;
    }

    /**
     * 启动UDP服务，支持多设备
     * @param {string} listenIp
     * @param {number} listenPort
     * @param {Array} boards [{ip, port}]
     */
    start(listenIp, listenPort, boards) {
        if (this.isListening) {
            console.log('UDP server is already listening.');
            return;
        }
        this.devices = new Map();
        if (Array.isArray(boards) && boards.length > 0) {
            boards.forEach(b => {
                // Validate board configuration
                if (!b.ip || !b.port) {
                    console.warn('[UDP] 跳过无效的基站配置:', b);
                    return;
                }
                this.devices.set(b.ip, {
                    ip: b.ip,
                    port: b.port,
                    sn: null,
                    hasSN: false,
                    heartbeatCount: 0,
                    lastHeartbeat: null,
                    authenticated: false,
                    lastAuthDate: null,
                    authSerial: null,
                    authCode: null
                });
            });

            const simplifiedDevices = Array.from(this.devices.values()).map(device => ({
                ip: device.ip,
                port: device.port,
                sn: device.sn,
                hasSN: device.hasSN,
                heartbeatCount: device.heartbeatCount
            }));
            console.log('[UDP] 设备列表:', simplifiedDevices);
        } else {
            console.log('[UDP] 启动UDP服务器，未配置基站设备，将监听所有传入连接');
        }

        this.server = require('dgram').createSocket('udp4');
        this.server.on('error', (err) => {
            console.error(`[UDP Server] Error:\n${err.stack}`);
            this.server.close();
            this.isListening = false;
        });
        this.server.on('message', (msg, rinfo) => {
            const device = this.devices.get(rinfo.address);
            if (!device) {
                if (this.enableLogging) console.log(`[UDP] 未知设备: ${rinfo.address}`);
                return;
            }

            // [新增] 获取当前基站的SN模式，如果未知则默认不带SN
            // const currentBoardHasSN = this.boardSnMode.get(rinfo.address) || false; // Replaced by device.hasSN
            
            // [修改] 将 hasSN 标志传递给 decryptMessage
            const decrypted = decryptMessage(msg, device.hasSN);
            
            // MCP Server 集成
            if (this.natsConnection) {
                // [修改] 发布到 NATS 时附带 hasSN 标志和源IP
                this.natsConnection.publish('base_station.raw_response', this.sc.encode(JSON.stringify({
                    ip: rinfo.address,
                    hasSN: device.hasSN,
                    buffer: decrypted.toString('hex') // 将Buffer转为hex字符串传输
                })));
                // console.log(`  Published decrypted buffer to NATS topic 'base_station.raw_response' with hasSN=${device.hasSN}.`);
            }
            
            // 保留通用解密日志
            // console.log(`  Decrypted (Hex): ${decrypted.toString('hex')}`);
            
            // 解析消息头 (总是解析，但日志有条件打印)
            const frameHeader = decrypted.readUInt32LE(0);
            const msgType = decrypted.readUInt16LE(4);
            const msgLength = decrypted.readUInt16LE(6);
            const frame = decrypted.readUInt16LE(8);
            const subSysCode = decrypted.readUInt16LE(10);
            const transDone = (subSysCode & 0x8000) >> 15; // 最高1位
            const transId = subSysCode & 0x7FFF; // 低15位

            if (this.enableLogging) {
            if (msgType !== MSG_TYPE_HEARTBEAT_IND && msgType !== MSG_TYPE_HEARTBEAT_ACK) {
                console.log('  --- Message Header Parse ---');
                console.log(`  Frame Header: 0x${frameHeader.toString(16)} (${frameHeader === 0x5555AAAA ? 'Correct' : 'Incorrect'})`);
                console.log(`  Message Type: 0x${msgType.toString(16)}`);
                console.log(`  Message Length: ${msgLength}`);
                console.log(`  Frame: 0x${frame.toString(16)}`);
                console.log(`  SubSysCode: 0x${subSysCode.toString(16)}`);
                console.log(`    └─ Transmit Done Flag: ${transDone} (${transDone === 0 ? 'Complete' : 'Incomplete'})`);
                console.log(`    └─ TransId: ${transId}`);
            }
}
            if (msgType === MSG_TYPE_HEARTBEAT_IND) { // 心跳指示
                if (this.enableLogging) console.log('--- 心跳报文解析 (0xF010) ---');
                device.heartbeatCount++;

                // 确保消息长度至少为56字节 (32字节头部 + 24字节心跳体)
                const heartbeatLength = msg.length; // 使用原始消息长度判断
                let hasSNInHeartbeat = false;
                if (heartbeatLength === 56) { // 56字节的心跳包通常带SN
                    hasSNInHeartbeat = true;
                } else if (heartbeatLength === 36) { // 36字节的心跳包不带SN
                    hasSNInHeartbeat = false;
                } else {
                    console.warn(`[UDP Server] WARN: Unexpected heartbeat length: ${heartbeatLength} bytes. Assuming no SN.`);
                    hasSNInHeartbeat = false;
                }

                // [新增] 更新该基站的SN模式
                device.hasSN = hasSNInHeartbeat;
                if (hasSNInHeartbeat) {
                    const snBuffer = decrypted.slice(12, 32);
                    const boardSn = snBuffer.toString('hex');
                    const isSnAllZeros = snBuffer.every(byte => byte === 0);
                    if (!isSnAllZeros) {
                        device.sn = boardSn;
                        if (this.enableLogging) console.log(`[UDP Server] Board ${rinfo.address} SN updated to 0x${boardSn} based on heartbeat.`);
                    } else {
                        if (this.enableLogging) console.log(`[UDP Server] Board ${rinfo.address} received all-zero SN in heartbeat.`);
                    }
                }
                if (decrypted.length < 56) {
                    if (this.enableLogging) console.warn(`WARN: 心跳报文长度不足56字节，实际长度: ${decrypted.length}B`);
                }

                // 解析SN号 (32字节头部，SN从第12字节开始，共20字节)
                const snBuffer = decrypted.slice(12, 32);
                const boardSn = snBuffer.toString('hex');
                if (this.enableLogging) console.log(`单板SN (u8BoardSn): 0x${boardSn}`);

                // 解析心跳包体 (从第32字节开始)
                const cellState = decrypted.readUInt16LE(32);
                const band = decrypted.readUInt16LE(34);
                const ulEarfcn = decrypted.readUInt32LE(36);
                const dlEarfcn = decrypted.readUInt32LE(40);
                // PLMN字段是7个字节的字符数组，以' '结尾
                const plmn = decrypted.slice(44, 51).toString('ascii').replace(/ .*$/, '');
                const bandwidth = decrypted.readUInt8(51);
                const pci = decrypted.readUInt16LE(52);
                const tac = decrypted.readUInt16LE(54);

                // [新增] 更新设备状态信息
                device.lastHeartbeat = new Date().toISOString();
                device.cellState = cellState;
                device.band = band;
                device.ulEarfcn = ulEarfcn;
                device.dlEarfcn = dlEarfcn;
                device.plmn = plmn;
                device.bandwidth = bandwidth;
                device.pci = pci;
                device.tac = tac;

                // [新增] 发布基站状态信息到NATS，供MCP Server使用
                if (this.natsConnection) {
                    const baseStationStatus = {
                        ip: rinfo.address,
                        port: rinfo.port,
                        sn: device.sn,
                        hasSN: device.hasSN,
                        heartbeatCount: device.heartbeatCount,
                        lastHeartbeat: device.lastHeartbeat,
                        cellState: cellState,
                        cellStateText: (() => {
                            switch(cellState) {
                                case 0: return '小区IDLE态';
                                case 1: return '扫频/同步进行中';
                                case 2: return '小区激活中';
                                case 3: return '小区激活态';
                                case 4: return '小区去激活中';
                                case 5: return '同步成功，REM处于ON状态';
                                case 6: return '同步中';
                                default: return '未知状态';
                            }
                        })(),
                        band: band,
                        ulEarfcn: ulEarfcn,
                        dlEarfcn: dlEarfcn,
                        plmn: plmn,
                        bandwidth: bandwidth,
                        bandwidthText: (() => {
                            switch(bandwidth) {
                                case 25: return '5M';
                                case 50: return '10M';
                                case 75: return '15M';
                                case 100: return '20M';
                                default: return '未知带宽';
                            }
                        })(),
                        pci: pci,
                        tac: tac,
                        timestamp: Date.now()
                    };

                    this.natsConnection.publish('base_station.status_update', this.sc.encode(JSON.stringify(baseStationStatus)));
                    if (this.enableLogging) console.log(`[UDP Server] Published base station status for ${rinfo.address} to NATS.`);
                }

                if (this.enableLogging) {
                    console.log(`小区状态 (CellState): ${cellState} (${(() => {
                        switch(cellState) {
                            case 0: return '小区IDLE态';
                            case 1: return '扫频/同步进行中';
                            case 2: return '小区激活中';
                            case 3: return '小区激活态';
                            case 4: return '小区去激活中';
                            case 5: return '同步成功，REM处于ON状态';
                            case 6: return '同步中';
                            default: return '未知状态';
                        }
                    })()})`);
                    console.log(`BAND (Band): ${band}`);
                    console.log(`上行频点 (ulEarfcn): ${ulEarfcn}`);
                    console.log(`下行频点 (dlEarfcn): ${dlEarfcn}`);
                    console.log(`PLMN: '${plmn}'`);
                    console.log(`系统带宽 (Bandwidth): ${bandwidth} (${(() => {
                        switch(bandwidth) {
                            case 25: return '5M';
                            case 50: return '10M';
                            case 75: return '15M';
                            case 100: return '20M';
                            default: return '未知带宽';
                        }
                    })()})`);
                    console.log(`物理小区ID (PCI): ${pci}`);
                    console.log(`跟踪区码 (TAC): ${tac}`);
                }

                // 发送心跳应答
                const ack = buildHeartbeatAck(snBuffer); // Use the SN from the heartbeat, even if it's all zeros
                const encryptedAck = encryptMessage(ack, hasSNInHeartbeat); 
                this.server.send(encryptedAck, rinfo.port, rinfo.address);
                if (this.enableLogging) console.log('已发送心跳应答。');

                // 检查SN号是否为全0（表示未携带有效SN）
                const isSnAllZeros = snBuffer.every(byte => byte === 0);
                if (isSnAllZeros) {
                    if (this.enableLogging) console.log('心跳报文未携带SN号，发送基站信息查询消息...');
                    // 使用从心跳报文中提取的SN (即使是全0) 来构建查询请求
                    this.sendBaseInfoQuery(snBuffer, rinfo);
                } else {
                    // 收到3个心跳后发送授权查询消息 (如果SN已携带)
                    if (device.heartbeatCount === 2) {
                        const today = new Date().toISOString().slice(0, 10);
                        const lastAuthDate = device.lastAuthDate;
                        if (lastAuthDate === today) {
                            if (this.enableLogging) console.log(`[AUTH] SN 0x${boardSn} 今日已成功授权，跳过授权查询流程。`);
                            device.heartbeatCount = 0; // Reset heartbeat count even if skipped
                        } else {
                            if (this.enableLogging) console.log('收到3个心跳报文，准备发送设备序号查询消息...');
                            this.sendAuthSerialQuery(snBuffer, rinfo);
                            device.heartbeatCount = 0;
                        }
                    }
                }
            } else if (msgType === MSG_TYPE_BASE_INFO_QUERY_ACK) { // 新增：基站信息查询应答
                if (this.enableLogging) console.log('--- 基站信息查询应答解析 (0xF02C) ---'); // 消息ID是0xF02C
                // 消息头32字节 + SN (20字节) + 其他信息...
                // 这里假设基站信息应答报文至少包含头部和SN
                if (decrypted.length < 32 + 20) { 
                    if (this.enableLogging) console.warn(`WARN: 基站信息查询应答报文长度不足52字节，实际长度: ${decrypted.length}B`);
                    return;
                }

                const snFromBaseInfoAck = decrypted.slice(12, 32); // 从应答报文头中提取SN号
                const boardSnHex = snFromBaseInfoAck.toString('hex');
                device.sn = boardSnHex; // 更新 device.sn
                if (this.enableLogging) console.log(`从基站信息应答中获取到SN: 0x${boardSnHex}，并已更新设备SN。`);

                // 获取到SN后，继续发送授权查询消息
                const today = new Date().toISOString().slice(0, 10);
                const lastAuthDate = device.lastAuthDate;
                if (lastAuthDate === today) {
                    if (this.enableLogging) console.log(`[AUTH] SN 0x${boardSnHex} 今日已成功授权，跳过授权查询流程。`);
                } else {
                    if (this.enableLogging) console.log('已获取到SN，准备发送设备序号查询消息...');
                    this.sendAuthSerialQuery(snFromBaseInfoAck, rinfo);
                }

            } else if (msgType === MSG_TYPE_AUTH_SERIAL_QUERY_ACK) { // 设备序号查询应答
                if (this.enableLogging) console.log('--- 设备序号查询应答解析 (0xF112) ---');
                // 消息头32字节 + 消息体68字节 = 100字节
                if (decrypted.length < 100) {
                    if (this.enableLogging) console.warn(`WARN: 设备序号查询应答报文长度不足100字节，实际长度: ${decrypted.length}B`);
                    return;
                }

                // 设备序号，长度为64，字符串以' '结尾 (从偏移量32开始，共65字节)
                const authSerialBuffer = decrypted.slice(32, 32 + 65);
                const authSerial = authSerialBuffer.toString('ascii').replace(/ .*$/, '');
                
                // 保留字节 (从偏移量32+65=97开始，共3字节)
                const resBytes = decrypted.slice(97, 97 + 3).toString('hex');

                if (this.enableLogging) {
                    console.log(`设备序号 (authSerial): '${authSerial}'`);
                    console.log(`保留字节 (res): 0x${resBytes}`);
                }

                // 重新从应答报文头中提取SN号 (32字节头部，SN从第12字节开始，共20字节)
                const snFromAck = decrypted.slice(12, 32);

                // **新的逻辑：将 authSerial 和 SN 发送到 NATS**
                if (this.natsConnection) {
                    const message = JSON.stringify({ sn: snFromAck.toString('hex'), authSerial: authSerial });
                    this.natsConnection.publish("au_query", this.sc.encode(message));
                    if (this.enableLogging) console.log(`[NATS] Published au_query with SN: ${snFromAck.toString('hex')}`);
                    if (this.enableLogging) console.log(`[NATS] Device SN at au_query publish: ${device.sn}`);
                } else {
                    if (this.enableLogging) console.warn('[NATS] NATS connection not established, cannot publish au_query.');
                }

            } else if (msgType === MSG_TYPE_AUTH_KEY_CFG_ACK) { // 授权密钥配置应答
                // console.log('--- 授权密钥配置应答解析 (0xF114) ---');
                // 消息头32字节 + cfgResult (1字节) + authResult (1字节) = 34字节
                if (decrypted.length < 34) {
                    // console.warn(`WARN: 授权密钥配置应答报文长度不足34字节，实际长度: ${decrypted.length}B`);
                    return;
                }

                const snFromAck = decrypted.slice(12, 32); // 提前提取SN号
                const cfgResult = decrypted.readUInt8(32); // 配置结果
                const authResult = decrypted.readUInt8(33); // 授权结果

                if (this.enableLogging) {
                    console.log(`配置结果 (cfgResult): ${cfgResult} (${cfgResult === 0 ? '成功' : '失败'})`);
                    console.log(`配置结果 (authResult): ${authResult} (${authResult === 0 ? '成功' : '失败'})`);
                }

                // 如果配置和授权都成功，记录当前SN和日期
                if (cfgResult === 0 && authResult === 0) {
                    const snFromAckHex = snFromAck.toString('hex');
                    const today = new Date().toISOString().slice(0, 10); // 获取 YYYY-MM-DD 格式的日期
                    device.authenticated = true;
                    device.lastAuthDate = today;
                    if (this.enableLogging) console.log(`[AUTH] SN 0x${snFromAckHex} 授权成功，已记录。`);
                }

                // 收到授权密钥配置应答后，发送辅助PLMN配置消息
                // console.log('收到配置应答，准备发送辅助PLMN配置消息...');
                const plmnsToSend = [
                    { mcc: '404', mnc: '45' },
                    { mcc: '404', mnc: '11' },
                    { mcc: '405', mnc: '879' },
                    { mcc: '404', mnc: '55' },
                    { mcc: '460', mnc: '01' },
                    { mcc: '460', mnc: '00' }
                ];
                const tempRinfo = { address: device.ip, port: device.port }; // 使用已有的板卡IP和端口
                this.sendSecondaryPlmnsSet(snFromAck, plmnsToSend, tempRinfo);

            } else if (msgType === MSG_TYPE_SECONDARY_PLMNS_SET_ACK) { // 辅助PLMN配置应答
                if (this.enableLogging) console.log('--- 辅助PLMN配置应答解析 (0xF116) ---');
                // 消息头32字节 + cfgResult (1字节) + authResult (1字节) = 34字节
                if (decrypted.length < 34) {
                    if (this.enableLogging) console.warn(`WARN: 辅助PLMN配置应答报文长度不足34字节，实际长度: ${decrypted.length}B`);
                    return;
                }

                const cfgResult = decrypted.readUInt8(32); // 配置结果
                const authResult = decrypted.readUInt8(33); // 授权结果

                if (this.enableLogging) {
                    console.log(`配置结果 (cfgResult): ${cfgResult} (${cfgResult === 0 ? '成功' : '失败'})`);
                    console.log(`授权结果 (authResult): ${authResult} (${authResult === 0 ? '成功' : '失败'})`);
                }

                // 收到辅助PLMN配置应答后，根据结果通过SSE向前端发布升级完成或失败消息
                if (this.upgradeCompletionSender) {
                    let message;
                    if (cfgResult === 0 && authResult === 0) { // 只有配置和授权都成功才发送成功消息
                        message = {
                            status: 'success',
                            text: '升级完成，系统业务验证正常的，请重启使用APP'
                        };
                    } else {
                        message = {
                            status: 'failure',
                            text: `升级失败，配置结果: ${cfgResult}, 授权结果: ${authResult}。请检查日志。`
                        };
                    }
                    this.upgradeCompletionSender(message);
                    if (this.enableLogging) console.log(`[SSE] Published upgrade_completion: ${JSON.stringify(message)}`);
                } else {
                    if (this.enableLogging) console.warn('[SSE] SSE sender not configured, cannot publish upgrade_completion.');
                }

            } else if (msgType === 0xF032) {
                // 接收增益和发射功率查询应答 (O_FL_ENB_TO_LMT_RXGAIN_POWER_DEREASE_QUERY_ACK)
                if (decrypted.length < 44) {
                    if (this.enableLogging) console.warn(`WARN: 功率查询应答报文长度不足44字节，实际长度: ${decrypted.length}B`);
                    return;
                }

                const u8RxGainValueFromReg = decrypted.readUInt8(32);
                const u8RxGainValueFromMib = decrypted.readUInt8(33);
                const u8PowerDereaseValueFromReg = decrypted.readUInt8(34);
                const u8PowerDereaseValueFromMib = decrypted.readUInt8(35);
                const u8AgcFlag = decrypted.readUInt8(36);
                const u8SnfRxGainValueFromReg = decrypted.readUInt8(37);
                const u8SnfRxGainValueFromMib = decrypted.readUInt8(38);
                const pwrDecreDelta = decrypted.readInt32LE(40);

                if (this.enableLogging) {
                    console.log(`接收增益和发射功率查询应答:`);
                    console.log(`  接收增益(寄存器): ${u8RxGainValueFromReg}`);
                    console.log(`  接收增益(数据库): ${u8RxGainValueFromMib}`);
                    console.log(`  功率衰减(寄存器): ${u8PowerDereaseValueFromReg}`);
                    console.log(`  功率衰减(数据库): ${u8PowerDereaseValueFromMib}`);
                    console.log(`  AGC开关: ${u8AgcFlag}`);
                    console.log(`  功率衰减增量: ${pwrDecreDelta}`);
                }

                // 计算实际功率衰减值 (转换为dB)
                const actualPowerDb = u8PowerDereaseValueFromReg * 0.25;

                // 通过SSE向前端发送功率查询结果
                if (this.upgradeCompletionSender) {
                    const message = {
                        type: 'power_query_response',
                        status: 'success',
                        data: {
                            targetIp: rinfo.address,
                            powerAttenuationDb: actualPowerDb,
                            rawValue: u8PowerDereaseValueFromReg,
                            savedValue: u8PowerDereaseValueFromMib,
                            rxGain: u8RxGainValueFromReg,
                            agcEnabled: u8AgcFlag === 1
                        }
                    };
                    this.upgradeCompletionSender(message);
                    if (this.enableLogging) console.log(`[SSE] Published power_query_response: ${JSON.stringify(message)}`);
                } else {
                    if (this.enableLogging) console.warn('[SSE] SSE sender not configured, cannot publish power_query_response.');
                }

            } else if (msgType === 0xF0F7) {
                // 发射功率衰减偏移配置查询应答 (O_FL_ENB_TO_LMT_PWR1_DEREASE_QUERY_ACK)
                if (decrypted.length < 36) {
                    if (this.enableLogging) console.warn(`WARN: 功率偏移查询应答报文长度不足36字节，实际长度: ${decrypted.length}B`);
                    return;
                }

                const pwr1Derease = decrypted.readUInt8(32);

                if (this.enableLogging) {
                    console.log(`发射功率衰减偏移配置查询应答:`);
                    console.log(`  功率衰减偏移: ${pwr1Derease}`);
                }

                // 通过SSE向前端发送功率偏移查询结果
                if (this.upgradeCompletionSender) {
                    const message = {
                        type: 'power_offset_query_response',
                        status: 'success',
                        data: {
                            targetIp: rinfo.address,
                            powerOffset: pwr1Derease
                        }
                    };
                    this.upgradeCompletionSender(message);
                    if (this.enableLogging) console.log(`[SSE] Published power_offset_query_response: ${JSON.stringify(message)}`);
                } else {
                    if (this.enableLogging) console.warn('[SSE] SSE sender not configured, cannot publish power_offset_query_response.');
                }

            } else {
                if (this.enableLogging) console.log(`--- 未知消息类型 (0x${msgType.toString(16)})，长度: ${msgLength} ---`);
            }
        });
        this.server.on('listening', () => {
            const address = this.server.address();
            console.log(`[UDP Server] Listening ${address.address}:${address.port}`);
            this.isListening = true;
        });
        this.server.bind(listenPort, listenIp);
        this.initNatsConnection();
    }

    /**
     * Initializes the NATS connection and subscribes to auth_response.
     */
    async initNatsConnection() {
        try {
            this.natsConnection = await connect({ servers: "nats://************:4222" });
            console.log(`[UDP Server] Connected to NATS at }`); //${this.natsConnection.getServer()

            // 订阅 'au_response' 主题
            const sub = this.natsConnection.subscribe("au_response");
            console.log("[UDP Server] Subscribed to 'au_response' NATS topic.");

            (async () => {
                for await (const m of sub) {
                    const msg = JSON.parse(this.sc.decode(m.data));
                    try {
                        const { sn, authCode } = msg;
                        if (this.enableLogging) console.log(`[UDP Server][NATS] Parsed au_response - SN: ${sn}, AuthCode: ${authCode}`);

                        let targetDevice = null;
                        for (const device of this.devices.values()) {
                            if (this.enableLogging) console.log(`[UDP Server][NATS] Comparing device.sn: ${device.sn} with NATS sn: ${sn}`);
                            if (device.sn === sn) {
                                targetDevice = device;
                                break;
                            }
                        }

                        if (targetDevice) {
                            const snBuffer = Buffer.from(sn, 'hex');
                            const rinfo = { address: targetDevice.ip, port: targetDevice.port }; // Construct rinfo for the target device
                            this.sendAuthKeyCfg(snBuffer, authCode, rinfo);
                            if (this.enableLogging) console.log(`[UDP Server][NATS] Found device ${targetDevice.ip} for SN ${sn} and sent AuthKeyCfg.`);
                        } else {
                            if (this.enableLogging) console.warn(`[UDP Server][NATS] Received auth_response for SN ${sn}, but no matching device was found.`);
                        }

                    } catch (parseError) {
                        if (this.enableLogging) console.error(`[UDP Server][NATS] Error parsing au_response message: ${parseError.message}`);
                    }
                }
            })();

            // 订阅来自 MCP Server 的发包指令
            const mcpSub = this.natsConnection.subscribe("mcp.send_packet");
            console.log("[UDP Server] Subscribed to 'mcp.send_packet' NATS topic.");
            (async () => {
                for await (const m of mcpSub) {
                    try {
                        const message = JSON.parse(m.data);
                        const { targetIp, packet } = message;
                        if (!targetIp || !packet) {
                            console.error('[UDP Server][NATS] Invalid mcp.send_packet message format. Expecting { targetIp, packet }.');
                            continue;
                        }
                        const packetBuffer = Buffer.from(packet, 'hex');
                        console.log(`\n--- [UDP Server] Received Packet from MCP Server (via NATS) for ${targetIp} ---`);
                        console.log(`  Packet (Hex): ${packet}`);
                        console.log(`  Length: ${packetBuffer.length} bytes`);
                        this.sendMessageToBoard(packetBuffer, targetIp);
                    } catch (e) {
                        console.error(`[UDP Server][NATS] Error processing mcp.send_packet: ${e.message}`);
                    }
                }
            })();

        } catch (err) {
            console.error(`[UDP Server] Error connecting to NATS: ${err.message}`);
        }
    }

    /**
     * Sends an O_FL_LMT_TO_ENB_AUTH_SERIAL_QUERY message to the embedded board.
     * @param {Buffer} sn - The 20-byte SN from the heartbeat message.
     * @param {Object} rinfo - The rinfo object from the received heartbeat message.
     */
    sendAuthSerialQuery(sn, rinfo) {
        if (!this.isListening || !rinfo || !rinfo.address) {
            if (this.enableLogging) console.error('UDP server not listening or rinfo missing for sending auth serial query.');
            return;
        }
        const device = this.devices.get(rinfo.address);
        if (!device) {
            if (this.enableLogging) console.error(`[UDP] sendAuthSerialQuery: Device with IP ${rinfo.address} not found.`);
            return;
        }

        const queryMessage = buildAuthSerialQuery(sn);
        const encryptedQuery = encryptMessage(queryMessage);

        this.server.send(encryptedQuery, device.port, device.ip, (err) => {
            if (err) {
                if (this.enableLogging) console.error(`Error sending O_FL_LMT_TO_ENB_SERIAL_QUERY to ${device.ip}:${device.port}: ${err.stack}`);
            } else {
                if (this.enableLogging) console.log(`O_FL_LMT_TO_ENB_SERIAL_QUERY sent to ${device.ip}:${device.port}`);
            }
        });
    }

    /**
     * Sends an O_FL_LMT_TO_ENB_AUTH_KEY_CFG message to the embedded board.
     * @param {Buffer} sn - The 20-byte SN from the heartbeat message.
     * @param {string} authCode - The authorization code string.
     * @param {Object} rinfo - The rinfo object from the received heartbeat message.
     */
    sendAuthKeyCfg(sn, authCode, rinfo) {
        if (!this.isListening || !rinfo || !rinfo.address) {
            if (this.enableLogging) console.error('UDP server not listening or rinfo missing for sending auth key config.');
            return;
        }
        const device = this.devices.get(rinfo.address);
        if (!device) {
            if (this.enableLogging) console.error(`[UDP] sendAuthKeyCfg: Device with IP ${rinfo.address} not found.`);
            return;
        }

        const configMessage = buildAuthKeyCfg(sn, authCode);
        const encryptedConfig = encryptMessage(configMessage);

        this.server.send(encryptedConfig, device.port, device.ip, (err) => {
            if (err) {
                if (this.enableLogging) console.error(`Error sending O_FL_LMT_TO_ENB_KEY_CFG to ${device.ip}:${device.port}: ${err.stack}`);
            }
        });
    }

    /**
     * Sends an O_FL_LMT_TO_ENB_SECONDARY_PLMNS_SET message to the embedded board.
     * @param {Buffer} sn - The 20-byte SN from the heartbeat message.
     * @param {Array<Object>} plmns - An array of PLMN objects, each with mcc and mnc properties.
     * @param {Object} rinfo - The rinfo object to send the message to.
     */
    sendSecondaryPlmnsSet(sn, plmns, rinfo) {
        if (!this.isListening || !rinfo || !rinfo.address) {
            if (this.enableLogging) console.error('UDP server not listening or rinfo missing for sending secondary PLMNs set.');
            return;
        }
        const device = this.devices.get(rinfo.address);
        if (!device) {
            if (this.enableLogging) console.error(`[UDP] sendSecondaryPlmnsSet: Device with IP ${rinfo.address} not found.`);
            return;
        }

        const configMessage = buildSecondaryPlmnsSet(sn, plmns);
        const encryptedConfig = encryptMessage(configMessage);

        this.server.send(encryptedConfig, device.port, device.ip, (err) => {
            if (err) {
                if (this.enableLogging) console.error(`Error sending O_FL_LMT_TO_ENB_SECONDARY_PLMNS_SET to ${device.ip}:${device.port}: ${err.stack}`);
            } else {
                if (this.enableLogging) console.log(`O_FL_LMT_TO_ENB_SECONDARY_PLMNS_SET sent to ${device.ip}:${device.port}`);
            }
        });
    }

    /**
     * Sends an O_FL_LMT_TO_ENB_BASE_INFO_QUERY message to the embedded board.
     * @param {Buffer} sn - The 20-byte SN from the heartbeat message or a dummy SN.
     * @param {Object} rinfo - The rinfo object from the received message.
     */
    sendBaseInfoQuery(sn, rinfo) {
        if (!this.isListening || !rinfo || !rinfo.address) {
            if (this.enableLogging) console.error('UDP server not listening or rinfo missing for sending base info query.');
            return;
        }
        const device = this.devices.get(rinfo.address);
        if (!device) {
            if (this.enableLogging) console.error(`[UDP] sendBaseInfoQuery: Device with IP ${rinfo.address} not found.`);
            return;
        }

        const queryMessage = buildBaseInfoQuery(sn);
        const encryptedQuery = encryptMessage(queryMessage);

        this.server.send(encryptedQuery, device.port, device.ip, (err) => {
            if (err) {
                if (this.enableLogging) console.error(`Error sending O_FL_LMT_TO_ENB_BASE_INFO_QUERY to ${device.ip}:${device.port}: ${err.stack}`);
            } else {
                if (this.enableLogging) console.log(`O_FL_LMT_TO_ENB_BASE_INFO_QUERY sent to ${device.ip}:${device.port}`);
            }
        });
    }

    /**
     * Sends a UDP message to a specific embedded board.
     * @param {string|Buffer} message - The message to send.
     * @param {string} targetIp - The IP address of the target board.
     */
    sendMessageToBoard(message, targetIp) {
        if (!this.isListening) {
            console.error('UDP server not listening. Cannot send UDP message.');
            return;
        }
        const device = this.devices.get(targetIp);
        if (!device) {
            console.error(`[UDP] sendMessageToBoard: Device with IP ${targetIp} not found.`);
            return;
        }

        const buffer = Buffer.isBuffer(message) ? message : Buffer.from(message);
        const hasSN = device.hasSN;
        let finalBuffer;
        let sn;

        if (hasSN) {
            sn = device.sn;
            if (!sn || sn.length !== 40) { // SN is 40 hex chars (20 bytes)
                console.error(`[UDP] Device ${targetIp} has invalid SN, cannot build packet.`);
                return;
            }
            const snBuffer = Buffer.from(sn, 'hex');
            const header = Buffer.from(buffer.slice(0, 12));
            const body = buffer.slice(12);
            let origLen = header.readUInt16LE(6);
            header.writeUInt16LE(origLen + 20, 6);
            finalBuffer = Buffer.concat([header, snBuffer, body]);
        } else {
            finalBuffer = buffer;
        }

        const encrypted = encryptMessage(finalBuffer, hasSN);

        console.log(`\n--- [UDP Server] Sending UDP Packet to Board ---`);
        console.log(`  Target: ${device.ip}:${device.port}`);
        console.log(`  hasSN: ${hasSN}`);
        if (hasSN) console.log(`  SN: ${sn}`);
        console.log(`  Original (Hex): ${buffer.toString('hex')}`);
        console.log(`  Final (Hex): ${finalBuffer.toString('hex')}`);
        console.log(`  Encrypted (Hex): ${encrypted.toString('hex')}`);
        console.log(`  Length: ${encrypted.length} bytes`);

        this.server.send(encrypted, device.port, device.ip, (err) => {
            if (err) {
                console.error(`[UDP Server] Error sending UDP message to ${device.ip}:${device.port}: ${err.stack}`);
            } else {
                console.log(`--- [UDP Server] UDP Packet Sent to ${device.ip}:${device.port} ---`);
            }
        });
    }

    /**
     * Stops the UDP server.
     */
    stop() {
        if (this.isListening) {
            this.server.close(() => {
                console.log('UDP server stopped.');
                this.isListening = false;
            });
        }
        if (this.natsConnection) {
            this.natsConnection.close();
            console.log('NATS connection closed.');
        }
    }

    /**
     * Dynamically adds a new device to the UDP server.
     * @param {object} boardInfo - { ip, port }
     */
    addDevice(boardInfo) {
        if (!boardInfo || !boardInfo.ip || !boardInfo.port) {
            console.error('[UDP] addDevice: Invalid board info provided.');
            return false;
        }
        if (this.devices.has(boardInfo.ip)) {
            console.warn(`[UDP] addDevice: Device with IP ${boardInfo.ip} already exists. Updating port.`);
            const existingDevice = this.devices.get(boardInfo.ip);
            existingDevice.port = boardInfo.port;
        } else {
            this.devices.set(boardInfo.ip, {
                ip: boardInfo.ip,
                port: boardInfo.port,
                sn: null,
                hasSN: false,
                heartbeatCount: 0,
                lastHeartbeat: null,
                authenticated: false,
                lastAuthDate: null,
                authSerial: null,
                authCode: null
            });
            console.log(`[UDP] addDevice: Device with IP ${boardInfo.ip} added.`);
        }
        return true;
    }

    /**
     * Dynamically removes a device from the UDP server.
     * @param {string} ip - The IP address of the device to remove.
     */
    removeDevice(ip) {
        if (this.devices.has(ip)) {
            this.devices.delete(ip);
            console.log(`[UDP] removeDevice: Device with IP ${ip} removed.`);
            return true;
        } else {
            console.warn(`[UDP] removeDevice: Device with IP ${ip} not found.`);
            return false;
        }
    }

    /**
     * Returns the status of all connected devices.
     * @returns {Array<Object>} An array of device status objects.
     */
    getDevicesStatus() {
        return Array.from(this.devices.values());
    }
}

module.exports = { UdpServer };