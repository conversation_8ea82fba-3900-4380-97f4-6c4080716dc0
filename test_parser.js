const ProtocolParser = require('./protocol_parser');

const PROTOCOL_FILE_PATH = './基站通信接口.md';

async function testProtocolParser() {
    const parser = new ProtocolParser(PROTOCOL_FILE_PATH);
    try {
        console.log("Starting protocol parsing...");
        const metadata = await parser.parseProtocolDocument();
        console.log("\n--- Protocol Metadata Registry ---");
        console.log(JSON.stringify(metadata, null, 2));

        console.log("\nStarting LLM Tools Schema generation...");
        const llmToolsSchema = parser.generateLlmToolsSchema();
        console.log("\n--- Generated LLM Tools Schema ---");
        console.log(JSON.stringify(llmToolsSchema, null, 2));

    } catch (error) {
        console.error("Test failed:", error);
    }
}

testProtocolParser(); 