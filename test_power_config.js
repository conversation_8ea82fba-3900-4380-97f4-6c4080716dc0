// test_power_config.js - 测试基站功率配置功能
const { connect, StringCodec } = require('nats');
const { v4: uuidv4 } = require('uuid');

const sc = StringCodec();

async function testPowerConfiguration() {
  let natsConnection;
  try {
    console.log('[Power Config Test] 连接到NATS服务器...');
    natsConnection = await connect({ servers: "nats://************:4222" });
    console.log('[Power Config Test] NATS连接成功。');

    // 1. 获取可用的工具列表
    console.log('\n=== 步骤1: 获取MCP工具列表 ===');
    const toolsMsg = await natsConnection.request('mcp.get_llm_tools', '', { timeout: 5000 });
    const tools = JSON.parse(sc.decode(toolsMsg.data));
    
    console.log('[Power Config Test] 可用的工具:');
    tools.forEach(tool => {
      console.log(`  - ${tool.name}: ${tool.description}`);
    });

    // 2. 测试发射功率衰减配置
    console.log('\n=== 步骤2: 测试发射功率衰减配置 ===');
    await testTxPowerAttenuation(natsConnection);

    // 3. 测试接收增益配置
    console.log('\n=== 步骤3: 测试接收增益配置 ===');
    await testRxGainConfiguration(natsConnection);

    console.log('\n[Power Config Test] 所有测试完成。');

  } catch (err) {
    console.error('[Power Config Test] 测试失败:', err);
  } finally {
    if (natsConnection) {
      await natsConnection.close();
      console.log('\n[Power Config Test] NATS连接已关闭。');
    }
  }
}

/**
 * 测试发射功率衰减配置
 */
async function testTxPowerAttenuation(natsConnection) {
  const correlationId = uuidv4();
  
  // 配置参数：设置功率衰减为40 (40*0.25=10dB衰减)，并保存配置
  const toolCallRequest = {
    tool_name: 'configure_tx_power_attenuation',
    tool_args: {
      Pwr1Derease: 40,  // 40 * 0.25 = 10dB 衰减
      IsSave: 1,        // 保存配置，重启后保留
      Res: [0, 0, 0]    // 保留字节
    },
    correlation_id: correlationId,
  };

  console.log(`[Power Config Test] 发送发射功率衰减配置请求:`);
  console.log(`  功率衰减值: ${toolCallRequest.tool_args.Pwr1Derease} (${toolCallRequest.tool_args.Pwr1Derease * 0.25}dB)`);
  console.log(`  保存配置: ${toolCallRequest.tool_args.IsSave ? '是' : '否'}`);
  console.log(`  关联ID: ${correlationId}`);

  // 订阅响应
  const sub = natsConnection.subscribe('llm.tool_results');
  
  // 发送请求
  natsConnection.publish('llm.invoke_tool', sc.encode(JSON.stringify(toolCallRequest)));
  console.log(`[Power Config Test] 请求已发送，等待响应...`);
  
  // 等待响应
  const result = await waitForResponse(sub, correlationId, 10000);
  
  if (result.success) {
    console.log(`[Power Config Test] ✅ 发射功率衰减配置成功`);
    console.log(`  响应数据:`, JSON.stringify(result.response.tool_output, null, 2));
  } else {
    console.log(`[Power Config Test] ❌ 发射功率衰减配置失败: ${result.error}`);
  }
}

/**
 * 测试接收增益配置
 */
async function testRxGainConfiguration(natsConnection) {
  const correlationId = uuidv4();
  
  // 配置参数：设置接收增益为60dB，保存配置，使用RX口
  const toolCallRequest = {
    tool_name: 'configure_rx_gain',
    tool_args: {
      Rxgain: 60,         // 接收增益60dB
      RxGainSaveFlag: 1,  // 保存配置
      RxOrSnfFlag: 0,     // 使用RX口
      Res: [0, 0]         // 保留字节
    },
    correlation_id: correlationId,
  };

  console.log(`[Power Config Test] 发送接收增益配置请求:`);
  console.log(`  接收增益: ${toolCallRequest.tool_args.Rxgain}dB`);
  console.log(`  保存配置: ${toolCallRequest.tool_args.RxGainSaveFlag ? '是' : '否'}`);
  console.log(`  端口类型: ${toolCallRequest.tool_args.RxOrSnfFlag === 0 ? 'RX' : 'SNF'}`);
  console.log(`  关联ID: ${correlationId}`);

  // 订阅响应
  const sub = natsConnection.subscribe('llm.tool_results');
  
  // 发送请求
  natsConnection.publish('llm.invoke_tool', sc.encode(JSON.stringify(toolCallRequest)));
  console.log(`[Power Config Test] 请求已发送，等待响应...`);
  
  // 等待响应
  const result = await waitForResponse(sub, correlationId, 10000);
  
  if (result.success) {
    console.log(`[Power Config Test] ✅ 接收增益配置成功`);
    console.log(`  响应数据:`, JSON.stringify(result.response.tool_output, null, 2));
  } else {
    console.log(`[Power Config Test] ❌ 接收增益配置失败: ${result.error}`);
  }
}

/**
 * 等待指定correlation_id的响应
 */
async function waitForResponse(subscription, correlationId, timeoutMs) {
  return new Promise((resolve) => {
    const timeout = setTimeout(() => {
      resolve({ success: false, error: '响应超时' });
    }, timeoutMs);

    (async () => {
      for await (const m of subscription) {
        try {
          const response = JSON.parse(sc.decode(m.data));
          if (response.correlation_id === correlationId) {
            clearTimeout(timeout);
            resolve({ success: true, response });
            break;
          }
        } catch (parseError) {
          console.error('[Power Config Test] 解析响应失败:', parseError);
        }
      }
    })();
  });
}

// 运行测试
if (require.main === module) {
  testPowerConfiguration();
}

module.exports = { testPowerConfiguration };
