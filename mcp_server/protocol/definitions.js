// mcp_server/protocol/definitions.js

/**
 * O_FL_LMT_TO_ENB_BASE_INFO_QUERY
 * 功能：查询基站基本信息，如版本号、SN等。
 */
const queryBaseInfo = {
  messageId: 0xF02B,
  messageName: 'O_FL_LMT_TO_ENB_BASE_INFO_QUERY',
  description: '查询基站的基本信息，例如设备型号、软硬件版本、SN号或板卡温度。',
  responseId: 0xF02C,
  parameters: [
    { 
      name: 'u32EnbBaseInfoType', 
      type: 'U32', 
      description: '查询信息的类型 (0:设备型号, 1:硬件版本, 2:软件版本, 3:SN号, 4:MAC地址, 5:uboot版本号, 6:板卡温度)' 
    }
  ],
  responseSchema: [
    { 
      name: 'u32EnbBaseInfoType', 
      type: 'U32', 
      description: '查询信息的类型 (与请求一致)' 
    },
    { 
      name: 'u8EnbbaseInfo', 
      type: 'STRING', 
      length: 100, 
      description: '基站返回的信息内容' 
    }
  ]
};

/**
 * O_FL_LMT_TO_ENB_REBOOT_CFG
 * 功能：指示基站执行重启操作。
 */
const configureReboot = {
  messageId: 0xF00B,
  messageName: 'O_FL_LMT_TO_ENB_REBOOT_CFG',
  description: '指示基站执行重启操作，并可配置重启后是否自动激活小区。',
  responseId: 0xF00C,
  parameters: [
    { 
      name: 'SelfActiveCfg', 
      type: 'U32', 
      description: '指示基站重启后是否自动激活小区 (0: 自动激活, 1: 不自动激活)' 
    }
  ]
};

/**
 * O_FL_LMT_TO_ENB_SECONDARY_PLMNS_SET
 * 功能：配置基站广播的辅PLMN列表。
 */
const configureSecondaryPlmns = {
  messageId: 0xF060,
  messageName: 'O_FL_LMT_TO_ENB_SECONDARY_PLMNS_SET',
  description: '配置基站广播SIB1中的非主PLMN列表。',
  responseId: 0xF061,
  parameters: [
    {
      name: 'u8SecPLMNNum',
      type: 'U8',
      description: '要配置的辅PLMN数目 (范围 1-5)'
    },
    {
      name: 'u8SecPLMNList',
      type: 'ARRAY',
      itemType: { type: 'STRING', length: 7 },
      countField: 'u8SecPLMNNum',
      maxCount: 5,
      description: '辅PLMN列表，每个PLMN是一个7字节的字符串 (例如 "46001")'
    }
  ]
};

/**
 * O_FL_LMT_TO_ENB_SYS_PWR1_DEREASE_CFG
 * 功能：配置基站发射功率衰减值，用于校准整机输出功率。
 */
const configureTxPowerAttenuation = {
  messageId: 0xF015,
  messageName: 'O_FL_LMT_TO_ENB_SYS_PWR1_DEREASE_CFG',
  description: '配置基站发射功率衰减值。衰减值每加4，基站输出功率增加1dB衰减。基站实际输出功率 = 零衰减功率 - 衰减值（Pwr1Derease*0.25）',
  responseId: 0xF016,
  parameters: [
    {
      name: 'Pwr1Derease',
      type: 'U32',
      description: '功率衰减值，取值范围0x00~0xFF，每步长代表0.25dB'
    },
    {
      name: 'IsSave',
      type: 'U8',
      description: '配置值是否保存到配置，重启之后也保留 (1: 重启保留配置, 0: 设备重启不保留配置)'
    },
    {
      name: 'Res',
      type: 'ARRAY',
      itemType: { type: 'U8', length: 1 },
      fixedCount: 3,
      description: '保留字节，填充为0'
    }
  ],
  responseSchema: [
    {
      name: 'result',
      type: 'U32',
      description: '配置结果 (0: 成功, 其他值: 失败)'
    }
  ]
};

/**
 * O_FL_LMT_TO_ENB_SYS_RxGAIN_CFG
 * 功能：配置基站接收增益
 */
const configureRxGain = {
  messageId: 0xF013,
  messageName: 'O_FL_LMT_TO_ENB_SYS_RxGAIN_CFG',
  description: '配置基站9361寄存器的接收增益，表示将接收到的来自UE的信号放大多少倍。',
  responseId: 0xF014,
  parameters: [
    {
      name: 'Rxgain',
      type: 'U32',
      description: '接收增益，取值范围0~127(dB)'
    },
    {
      name: 'RxGainSaveFlag',
      type: 'U8',
      description: '配置值是否保存到配置，重启之后也保留 (0: not save, 1: save)'
    },
    {
      name: 'RxOrSnfFlag',
      type: 'U8',
      description: '配置该增益是修改rx口增益还是snf口增益 (0：rx, 1：snf) 注：仅FDD有效，对于TDD该字段无意义'
    },
    {
      name: 'Res',
      type: 'ARRAY',
      itemType: { type: 'U8', length: 1 },
      fixedCount: 2,
      description: '保留字节，填充为0'
    }
  ],
  responseSchema: [
    {
      name: 'result',
      type: 'U32',
      description: '配置结果 (0: 成功, 其他值: 失败)'
    }
  ]
};


// 导出所有定义，键为LLM友好的名称
module.exports = {
  query_base_info: queryBaseInfo,
  configure_reboot: configureReboot,
  configure_secondary_plmns: configureSecondaryPlmns,
  configure_tx_power_attenuation: configureTxPowerAttenuation,
  configure_rx_gain: configureRxGain,
};
