// mcp_server/protocol/definitions.js

/**
 * O_FL_LMT_TO_ENB_BASE_INFO_QUERY
 * 功能：查询基站基本信息，如版本号、SN等。
 */
const queryBaseInfo = {
  messageId: 0xF02B,
  messageName: 'O_FL_LMT_TO_ENB_BASE_INFO_QUERY',
  description: '查询基站的基本信息，例如设备型号、软硬件版本、SN号或板卡温度。',
  responseId: 0xF02C,
  parameters: [
    { 
      name: 'u32EnbBaseInfoType', 
      type: 'U32', 
      description: '查询信息的类型 (0:设备型号, 1:硬件版本, 2:软件版本, 3:SN号, 4:MAC地址, 5:uboot版本号, 6:板卡温度)' 
    }
  ],
  responseSchema: [
    { 
      name: 'u32EnbBaseInfoType', 
      type: 'U32', 
      description: '查询信息的类型 (与请求一致)' 
    },
    { 
      name: 'u8EnbbaseInfo', 
      type: 'STRING', 
      length: 100, 
      description: '基站返回的信息内容' 
    }
  ]
};

/**
 * O_FL_LMT_TO_ENB_REBOOT_CFG
 * 功能：指示基站执行重启操作。
 */
const configureReboot = {
  messageId: 0xF00B,
  messageName: 'O_FL_LMT_TO_ENB_REBOOT_CFG',
  description: '指示基站执行重启操作，并可配置重启后是否自动激活小区。',
  responseId: 0xF00C,
  parameters: [
    { 
      name: 'SelfActiveCfg', 
      type: 'U32', 
      description: '指示基站重启后是否自动激活小区 (0: 自动激活, 1: 不自动激活)' 
    }
  ]
};

/**
 * O_FL_LMT_TO_ENB_SECONDARY_PLMNS_SET
 * 功能：配置基站广播的辅PLMN列表。
 */
const configureSecondaryPlmns = {
  messageId: 0xF060,
  messageName: 'O_FL_LMT_TO_ENB_SECONDARY_PLMNS_SET',
  description: '配置基站广播SIB1中的非主PLMN列表。',
  responseId: 0xF061,
  parameters: [
    {
      name: 'u8SecPLMNNum',
      type: 'U8',
      description: '要配置的辅PLMN数目 (范围 1-5)'
    },
    {
      name: 'u8SecPLMNList',
      type: 'ARRAY',
      itemType: { type: 'STRING', length: 7 },
      countField: 'u8SecPLMNNum',
      maxCount: 5,
      description: '辅PLMN列表，每个PLMN是一个7字节的字符串 (例如 "46001")'
    }
  ]
};


// 导出所有定义，键为LLM友好的名称
module.exports = {
  query_base_info: queryBaseInfo,
  configure_reboot: configureReboot,
  configure_secondary_plmns: configureSecondaryPlmns,
};
