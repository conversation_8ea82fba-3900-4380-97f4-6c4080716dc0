// mcp_server/protocol/tests/core.test.js
const { build } = require('../builder');
const { parse } = require('../parser');
const assert = require('assert');

console.log('Running MCP Server core tests...');

// 测试用例 1: 基站重启配置
function testConfigureReboot() {
  const commandName = 'configure_reboot';
  const args = { SelfActiveCfg: 1 };
  
  console.log(`  - Testing: ${commandName}`);
  
  // 1. 构建
  const buffer = build(commandName, args);
  
  // 2. 验证构建结果 (部分)
  assert.strictEqual(buffer.readUInt16LE(4), 0xF00B, 'Reboot: Message ID should be 0xF00B');
  assert.strictEqual(buffer.readUInt32LE(12), 1, 'Reboot: SelfActiveCfg should be 1');
  
  // 3. 解析 (模拟响应)
  // 假设响应ID是 0xF00C，且响应体为空或与请求结构不同。
  // 由于当前parser是基于请求结构简化的，我们暂时不测试解析。
  // 在未来的迭代中，当响应有独立schema时，需要在这里添加解析验证。
  console.log('    Build test PASSED.');
}

// 测试用例 2: 基站基本信息查询
function testQueryBaseInfo() {
  const commandName = 'query_base_info';
  const args = { u32EnbBaseInfoType: 3 }; // 查询SN号
  
  console.log(`  - Testing: ${commandName}`);
  
  // 1. 构建
  const buffer = build(commandName, args);
  
  // 2. 验证构建结果
  assert.strictEqual(buffer.readUInt16LE(4), 0xF02B, 'QueryInfo: Message ID should be 0xF02B');
  assert.strictEqual(buffer.readUInt32LE(12), 3, 'QueryInfo: u32EnbBaseInfoType should be 3');
  
  // 3. 解析 (模拟响应)
  // 假设我们收到一个响应，需要创建模拟的响应buffer
  const responseId = 0xF02C;
  // 假设响应体结构为：u32EnbBaseInfoType (U32), u8EnbbaseInfo[100] (STRING)
  // 这是一个更真实的场景，我们的简化parser无法处理，所以暂时跳过。
  console.log('    Build test PASSED.');
}

// 测试用例 3: 配置辅PLMN列表
function testConfigureSecondaryPlmns() {
    const commandName = 'configure_secondary_plmns';
    const args = {
        u8SecPLMNNum: 2,
        u8SecPLMNList: ["46001", "46002"]
    };

    console.log(`  - Testing: ${commandName}`);

    // 1. 构建
    const buffer = build(commandName, args);

    // 2. 验证构建结果
    assert.strictEqual(buffer.readUInt16LE(4), 0xF060, 'PLMN: Message ID should be 0xF060');
    assert.strictEqual(buffer.readUInt8(12), 2, 'PLMN: u8SecPLMNNum should be 2');
    assert.strictEqual(buffer.toString('ascii', 13, 13 + 7).replace(/\0.*$/, ''), "46001", 'PLMN: First PLMN should be "46001"');
    assert.strictEqual(buffer.toString('ascii', 20, 20 + 7).replace(/\0.*$/, ''), "46002", 'PLMN: Second PLMN should be "46002"');
    
    console.log('    Build test PASSED.');
}


// 运行所有测试
try {
  testConfigureReboot();
  testQueryBaseInfo();
  testConfigureSecondaryPlmns();
  console.log('\nAll core tests PASSED!');
} catch (error) {
  console.error('\nTests FAILED:');
  console.error(error);
  process.exit(1); // Exit with error code if tests fail
}
