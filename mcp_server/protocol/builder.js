// mcp_server/protocol/builder.js
const definitions = require('./definitions');

/**
 * 根据消息定义和参数构建二进制Buffer
 * @param {string} commandName - LLM友好的命令名 (例如 'configure_reboot')
 * @param {object} args - 从LLM传入的参数 (例如 { SelfActiveCfg: 0 })
 * @returns {Buffer} - 构建好的二进制消息体
 */
function build(commandName, args) {
  const definition = definitions[commandName];
  if (!definition) {
    throw new Error(`未找到命令 '${commandName}' 的定义`);
  }

  // 估算Buffer总长度，这里需要根据实际协议消息头长度进行调整
  // 假设一个通用的消息头长度为 12 字节
  const headerLength = 12; 
  let totalLength = headerLength;
  
  // 计算消息体长度
  definition.parameters.forEach(param => {
    if (param.type === 'U32' || param.type === 'S32') {
      totalLength += 4;
    } else if (param.type === 'U8' || param.type === 'S8') {
      totalLength += 1;
    } else if (param.type === 'STRING') {
      totalLength += param.length;
    } else if (param.type === 'ARRAY') {
      const itemCount = args[param.countField];
      totalLength += itemCount * param.itemType.length;
    }
  });

  const buffer = Buffer.alloc(totalLength);
  let offset = 0;

  // --- 填充消息头 (示例) ---
  // 实际实现需要根据 '基站通信协议加解密方案.md' 文档来精确构建
  // u32FrameHeader: 0x5555AAAA
  buffer.writeUInt32LE(0x5555AAAA, offset);
  offset += 4;
  // u16MsgType
  buffer.writeUInt16LE(definition.messageId, offset);
  offset += 2;
  // u16MsgLength
  buffer.writeUInt16LE(totalLength, offset);
  offset += 2;
  // u16frame (假设为0)
  buffer.writeUInt16LE(0, offset);
  offset += 2;
  // u16SubSysCode (TransId)
  // 生成一个1到32767 (0x7FFF) 之间的随机TransId
  const transId = Math.floor(Math.random() * 0x7FFF) + 1; 
  buffer.writeUInt16LE(transId, offset);
  offset += 2;

  // --- 填充消息体 ---
  definition.parameters.forEach(param => {
    const value = args[param.name];
    if (value === undefined) {
      // 在实际应用中可能需要更复杂的默认值或错误处理
      console.warn(`警告: 命令 '${commandName}' 的参数 '${param.name}' 未提供，将使用默认值0`);
    }

    switch (param.type) {
      case 'U32':
        buffer.writeUInt32LE(value || 0, offset);
        offset += 4;
        break;
      case 'S32':
        buffer.writeInt32LE(value || 0, offset);
        offset += 4;
        break;
      case 'U8':
        buffer.writeUInt8(value || 0, offset);
        offset += 1;
        break;
      case 'S8':
        buffer.writeInt8(value || 0, offset);
        offset += 1;
        break;
      case 'STRING':
        buffer.write(value || '', offset, param.length, 'ascii');
        offset += param.length;
        break;
      case 'ARRAY':
        if (param.itemType.type === 'STRING') {
          const list = value || [];
          list.forEach(item => {
            buffer.write(item, offset, param.itemType.length, 'ascii');
            offset += param.itemType.length;
          });
        } else {
          // 可以根据需要扩展支持其他数组类型
          throw new Error(`不支持的数组项目类型: ${param.itemType.type}`);
        }
        break;
      default:
        throw new Error(`未知的参数类型: ${param.type}`);
    }
  });

  return buffer;
}

module.exports = { build };
