// mcp_server/protocol/parser.js
const definitions = require('./definitions');

// 创建一个从 messageId 到 commandName 的反向映射，方便查找
const responseIdToCommandName = {};
for (const commandName in definitions) {
  const def = definitions[commandName];
  if (def.responseId) {
    responseIdToCommandName[def.responseId] = commandName;
  }
}

/**
 * 根据消息ID和二进制Buffer解析消息
 * @param {number} messageId - 从消息头中解析出的消息ID
 * @param {Buffer} buffer - 包含消息体的完整二进制Buffer (包含消息头)
 * @param {number} [offset=12] - 解析消息体的起始偏移量
 * @returns {object|null} - 解析后的JSON对象，如果找不到定义则返回null
 */
function parse(messageId, buffer, offset = 12) {
  const commandName = responseIdToCommandName[messageId];
  if (!commandName) {
    // 可能是一些不需要MCP Server处理的基站主动上报消息，例如心跳
    // console.log(`信息: 未找到针对响应ID 0x${messageId.toString(16)} 的解析定义，已跳过。`);
    return null;
  }
  
  const definition = definitions[commandName];
  // [修改] 使用 responseSchema 来解析，如果不存在则回退到 parameters
  const schemaToUse = definition.responseSchema || definition.parameters; 
  
  const result = {};
  // let offset = 12; // 跳过12字节的通用消息头
  // --- 解析消息体 ---
  schemaToUse.forEach(param => { // [修改] 遍历 responseSchema
    switch (param.type) {
      case 'U32':
        result[param.name] = buffer.readUInt32LE(offset);
        offset += 4;
        break;
      case 'S32':
        result[param.name] = buffer.readInt32LE(offset);
        offset += 4;
        break;
      case 'U8':
        result[param.name] = buffer.readUInt8(offset);
        offset += 1;
        break;
      case 'S8':
        result[param.name] = buffer.readInt8(offset);
        offset += 1;
        break;
      case 'STRING':
        // [修改] 确保读取正确长度的字符串，并去除空字符
        result[param.name] = buffer.toString('ascii', offset, offset + param.length).replace(/\0.*$/, '');
        offset += param.length;
        break;
      case 'ARRAY':
         if (param.itemType.type === 'STRING') {
          const list = [];
          const itemCount = result[param.countField]; 
          for(let i = 0; i < itemCount; i++) {
            list.push(buffer.toString('ascii', offset, offset + param.itemType.length).replace(/\0.*$/, ''));
            offset += param.itemType.length;
          }
          result[param.name] = list;
        } else {
          throw new Error(`不支持的数组项目类型: ${param.itemType.type}`);
        }
        break;
      case 'F64': // [新增] 支持F64类型
        result[param.name] = buffer.readDoubleLE(offset);
        offset += 8;
        break;
      default:
        throw new Error(`未知的参数类型: ${param.type}`);
    }
  });

  return result;
}

module.exports = { parse };
