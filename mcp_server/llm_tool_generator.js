// mcp_server/llm_tool_generator.js
const definitions = require('./protocol/definitions');

/**
 * 将协议定义转换为LLM的Function Calling Tool Schema
 * @returns {Array<object>}
 */
function generateTools() {
  const tools = [];

  for (const commandName in definitions) {
    const definition = definitions[commandName];
    
    const toolSchema = {
      type: 'function',
      function: {
        name: commandName,
        description: definition.description,
        parameters: {
          type: 'object',
          properties: {},
          required: [],
        },
      },
    };

    definition.parameters.forEach(param => {
      // 将我们的类型映射到JSON Schema类型
      let jsonSchemaType = 'string'; // 默认为string
      if (param.type === 'U32' || param.type === 'S32' || param.type === 'U8' || param.type === 'S8') {
        jsonSchemaType = 'integer';
      } else if (param.type === 'ARRAY') {
        jsonSchemaType = 'array';
      }

      toolSchema.function.parameters.properties[param.name] = {
        type: jsonSchemaType,
        description: param.description,
      };
      
      // 假设所有参数都是必需的
      toolSchema.function.parameters.required.push(param.name);

      // 如果是数组，可以添加items定义
      if (jsonSchemaType === 'array') {
          toolSchema.function.parameters.properties[param.name].items = {
              type: 'string', // 简化处理，所有数组项都视为字符串
              description: `数组项，类型: ${param.itemType.type}, 长度: ${param.itemType.length}`
          }
      }
    });
    
    tools.push(toolSchema);
  }

  return tools;
}

module.exports = { generateTools };
