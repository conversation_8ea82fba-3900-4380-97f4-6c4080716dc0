// mcp_server/app.js
console.log('[MCP Server] Starting...');

const natsClient = require('./nats_client');
const { build } = require('./protocol/builder');
const { parse } = require('./protocol/parser');
const definitions = require('./protocol/definitions');
const { generateTools } = require('./llm_tool_generator');

const pendingRequests = new Map();

// 任务 2.5: 查找匹配的请求 (已修改支持多基站)
function findAndRemoveMatchingRequest(responseMessageId, sourceIp) {
  let foundCorrelationId = null;

  for (const [correlationId, requestInfo] of pendingRequests.entries()) {
    // [修改] 同时匹配响应ID和源IP地址，确保多基站环境下的正确匹配
    if (requestInfo.responseId === responseMessageId && requestInfo.targetIp === sourceIp) {
      foundCorrelationId = correlationId;
      pendingRequests.delete(correlationId);
      break;
    }
  }

  return foundCorrelationId;
}

async function main() {
  console.log('[MCP Server] Initializing...');
  
  try {
    await natsClient.initialize();
  } catch (error) {
    console.error('[MCP Server] Failed to connect to NATS. Exiting.');
    process.exit(1);
  }
  
  // 订阅 LLM 工具调用主题
  natsClient.subscribe('llm.invoke_tool', (err, msg) => {
    if (err) { console.error('[MCP Server] Error receiving message on llm.invoke_tool:', err); return; }
    try {
      const request = JSON.parse(natsClient.sc.decode(msg.data));
      const { tool_name, tool_args, correlation_id, target_ip } = request;

      if (!tool_name || !tool_args || !correlation_id) {
        console.error('[MCP Server] Invalid message on llm.invoke_tool, missing fields:', request);
        return;
      }

      // [新增] 检查是否指定了目标IP
      if (!target_ip) {
        console.error('[MCP Server] Missing target_ip field. Multi-device environment requires target IP specification.');
        return;
      }

      console.log(`\n--- [MCP Server] Received LLM Tool Call ---`);
      console.log(`  Tool Name: ${tool_name}`);
      console.log(`  Target IP: ${target_ip}`);
      console.log(`  Correlation ID: ${correlation_id}`);
      console.log(`  Arguments: ${JSON.stringify(tool_args)}`);

      const packet = build(tool_name, tool_args);

      // [新增日志] 打印构建后的二进制数据包
      console.log(`  Built Binary Packet (Hex): ${packet.toString('hex')}`);
      console.log(`  Packet Length: ${packet.length} bytes`);

      const definition = definitions[tool_name];
      if (definition && definition.responseId) {
        pendingRequests.set(correlation_id, {
          commandName: tool_name,
          requestTime: Date.now(),
          responseId: definition.responseId,
          targetIp: target_ip  // [新增] 存储目标IP用于响应匹配
        });
      } else {
        pendingRequests.set(correlation_id, {
          commandName: tool_name,
          requestTime: Date.now(),
          responseId: null,
          targetIp: target_ip  // [新增] 存储目标IP用于响应匹配
        });
        console.warn(`[MCP Server] Command '${tool_name}' has no defined responseId. Cannot track response accurately.`);
      }

      // [修改] 发送消息时包含目标IP
      const mcpMessage = {
        targetIp: target_ip,
        packet: packet.toString('hex')
      };
      natsClient.publish('mcp.send_packet', JSON.stringify(mcpMessage));
      console.log(`--- [MCP Server] Published to 'mcp.send_packet' for ${target_ip} ---`);

    } catch (e) {
      console.error(`[MCP Server] Error processing tool call: ${e.message}`);
    }
  });
  
  // 订阅基站原始响应主题
  natsClient.subscribe('base_station.raw_response', (err, msg) => {
    if (err) { console.error('[MCP Server] Error receiving message on base_station.raw_response:', err); return; }
    try {
        // 修正：先解码为字符串，再JSON.parse
        const jsonStr = natsClient.sc.decode(msg.data);
        const { ip, hasSN, buffer } = JSON.parse(jsonStr);
        // buffer字段是hex字符串，转回Buffer
        const rawBuffer = Buffer.from(buffer, 'hex');
        const messageId = rawBuffer.readUInt16LE(4);

        console.log(`\n--- [MCP Server] Received Raw Base Station Response ---`);
        console.log(`  Message ID: 0x${messageId.toString(16)}`);
        console.log(`  Raw Buffer (Hex): ${rawBuffer.toString('hex')}`);
        console.log(`  Buffer Length: ${rawBuffer.length} bytes`);

        // [修正] 根据hasSN调整offset
        const offset = hasSN ? 32 : 12;
        const parsedData = parse(messageId, rawBuffer, offset);

        if (parsedData) {
            console.log(`  Parsed Data: ${JSON.stringify(parsedData)}`);
            
            // [修改] 传递源IP地址进行匹配
            const correlation_id = findAndRemoveMatchingRequest(messageId, sourceIp);

            if (correlation_id) {
                console.log(`  Matched response 0x${messageId.toString(16)} from ${sourceIp} to request ID ${correlation_id}`);
                const toolOutput = {
                    correlation_id: correlation_id,
                    tool_output: parsedData,
                    source_ip: sourceIp  // [新增] 在响应中包含源IP信息
                };
                natsClient.publish('llm.tool_results', JSON.stringify(toolOutput));
                console.log(`--- [MCP Server] Published Tool Output to 'llm.tool_results' ---`);
            } else {
                console.log(`  Received an unmatched response with ID 0x${messageId.toString(16)}. It might be an unsolicited message (e.g., alarm) or a late response.`);
            }
        } else {
            console.log(`  Could not parse response for message ID 0x${messageId.toString(16)}. No definition found.`);
        }
    } catch (e) {
        console.error(`[MCP Server] Error processing raw response: ${e.message}`);
    }
  });

  // 定期清理超时的请求
  setInterval(() => {
    const now = Date.now();
    const timeout = 30000; // 30秒超时
    for (const [correlationId, requestInfo] of pendingRequests.entries()) {
      if (now - requestInfo.requestTime > timeout) {
        console.warn(`[MCP Server] Request ${correlationId} timed out. Command: ${requestInfo.commandName}`);
        pendingRequests.delete(correlationId);
      }
    }
  }, 10000);
  
  // 提供工具定义接口
  natsClient.subscribe('mcp.get_llm_tools', (err, msg) => {
    if (err) { console.error('[MCP Server] Error receiving message on mcp.get_llm_tools:', err); return; }
    
    console.log('\n--- [MCP Server] Received Request for LLM Tools ---');
    const tools = generateTools();
    
    if (msg.reply) {
      natsClient.publish(msg.reply, JSON.stringify(tools));
      console.log(`--- [MCP Server] Replied with ${tools.length} tools ---`);
    }
  });
  
  console.log('[MCP Server] Running and waiting for messages.');
}

main().catch(err => {
  console.error('[MCP Server] Unhandled error during startup:', err);
  process.exit(1);
});

process.on('SIGINT', async () => {
  console.log('[MCP Server] SIGINT received. Attempting graceful shutdown...');
  
  const shutdownTimeout = 5000;
  const timeoutId = setTimeout(() => {
    console.error('[MCP Server] Graceful shutdown timed out. Forcing exit.');
    process.exit(1);
  }, shutdownTimeout);

  try {
    await natsClient.close();
    console.log('[MCP Server] NATS connection closed successfully.');
    clearTimeout(timeoutId);
    process.exit(0);
  } catch (error) {
    console.error('[MCP Server] Error during NATS connection close:', error.message);
    clearTimeout(timeoutId);
    process.exit(1);
  }
});
