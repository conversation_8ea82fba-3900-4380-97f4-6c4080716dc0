// mcp_server/app.js
console.log('[MCP Server] Starting...');

const natsClient = require('./nats_client');
const { build } = require('./protocol/builder');
const { parse } = require('./protocol/parser');
const definitions = require('./protocol/definitions');
const { generateTools } = require('./llm_tool_generator');

const pendingRequests = new Map();
const baseStationStatus = new Map(); // 存储基站状态信息，key: ip, value: 状态对象

// 任务 2.5: 查找匹配的请求 (已修改支持多基站)
function findAndRemoveMatchingRequest(responseMessageId, sourceIp) {
  let foundCorrelationId = null;

  for (const [correlationId, requestInfo] of pendingRequests.entries()) {
    // [修改] 同时匹配响应ID和源IP地址，确保多基站环境下的正确匹配
    if (requestInfo.responseId === responseMessageId && requestInfo.targetIp === sourceIp) {
      foundCorrelationId = correlationId;
      pendingRequests.delete(correlationId);
      break;
    }
  }

  return foundCorrelationId;
}

/**
 * 处理虚拟工具调用
 * @param {string} toolName - 工具名称
 * @param {object} toolArgs - 工具参数
 * @param {string} correlationId - 关联ID
 * @param {string} targetIp - 目标IP
 */
function handleVirtualTool(toolName, toolArgs, correlationId, targetIp) {
  let result = null;

  switch (toolName) {
    case 'query_base_station_status':
      result = handleBaseStationStatusQuery(toolArgs, targetIp);
      break;
    default:
      result = { error: `未知的虚拟工具: ${toolName}` };
  }

  // 直接返回结果，不需要等待UDP响应
  const toolOutput = {
    correlation_id: correlationId,
    tool_output: result,
    source_ip: targetIp
  };

  natsClient.publish('llm.tool_results', JSON.stringify(toolOutput));
  console.log(`[MCP Server] Published virtual tool result for ${toolName}`);
}

/**
 * 处理基站状态查询
 * @param {object} args - 查询参数
 * @param {string} targetIp - 目标IP
 * @returns {object} 查询结果
 */
function handleBaseStationStatusQuery(args, targetIp) {
  const { target_ip } = args;

  // 如果指定了特定IP，返回该基站状态
  if (target_ip && target_ip.trim() !== '') {
    const status = baseStationStatus.get(target_ip.trim());
    if (status) {
      return {
        success: true,
        data: [formatBaseStationStatus(status)]
      };
    } else {
      return {
        success: false,
        error: `未找到IP为 ${target_ip} 的基站状态信息`,
        suggestion: '请确保基站已连接并发送心跳报文'
      };
    }
  }

  // 返回所有基站状态
  const allStatus = Array.from(baseStationStatus.values()).map(formatBaseStationStatus);

  return {
    success: true,
    data: allStatus,
    total: allStatus.length,
    timestamp: new Date().toISOString()
  };
}

/**
 * 格式化基站状态信息
 * @param {object} status - 原始状态信息
 * @returns {object} 格式化后的状态信息
 */
function formatBaseStationStatus(status) {
  return {
    基站信息: {
      IP地址: status.ip,
      端口: status.port,
      SN号: status.sn || '未获取',
      是否携带SN: status.hasSN ? '是' : '否',
      心跳次数: status.heartbeatCount,
      最后心跳时间: status.lastHeartbeat
    },
    小区状态: {
      状态码: status.cellState,
      状态描述: status.cellStateText,
      PLMN: status.plmn,
      物理小区ID: status.pci,
      跟踪区码: status.tac
    },
    频率信息: {
      频段: status.band,
      上行频点: status.ulEarfcn,
      下行频点: status.dlEarfcn,
      系统带宽: status.bandwidth,
      带宽描述: status.bandwidthText
    },
    技术参数: {
      更新时间戳: status.timestamp,
      数据新鲜度: `${Math.round((Date.now() - status.timestamp) / 1000)}秒前`
    }
  };
}

async function main() {
  console.log('[MCP Server] Initializing...');
  
  try {
    await natsClient.initialize();
  } catch (error) {
    console.error('[MCP Server] Failed to connect to NATS. Exiting.');
    process.exit(1);
  }
  
  // 订阅 LLM 工具调用主题
  natsClient.subscribe('llm.invoke_tool', (err, msg) => {
    if (err) { console.error('[MCP Server] Error receiving message on llm.invoke_tool:', err); return; }
    try {
      const request = JSON.parse(natsClient.sc.decode(msg.data));
      const { tool_name, tool_args, correlation_id, target_ip } = request;

      if (!tool_name || !tool_args || !correlation_id) {
        console.error('[MCP Server] Invalid message on llm.invoke_tool, missing fields:', request);
        return;
      }

      // [新增] 检查是否指定了目标IP
      if (!target_ip) {
        console.error('[MCP Server] Missing target_ip field. Multi-device environment requires target IP specification.');
        return;
      }

      console.log(`\n--- [MCP Server] Received LLM Tool Call ---`);
      console.log(`  Tool Name: ${tool_name}`);
      console.log(`  Target IP: ${target_ip}`);
      console.log(`  Correlation ID: ${correlation_id}`);
      console.log(`  Arguments: ${JSON.stringify(tool_args)}`);

      const definition = definitions[tool_name];

      // [新增] 处理虚拟工具调用
      if (definition && definition.isVirtual) {
        console.log(`[MCP Server] Processing virtual tool: ${tool_name}`);
        handleVirtualTool(tool_name, tool_args, correlation_id, target_ip);
        return;
      }

      const packet = build(tool_name, tool_args);

      // [新增日志] 打印构建后的二进制数据包
      console.log(`  Built Binary Packet (Hex): ${packet.toString('hex')}`);
      console.log(`  Packet Length: ${packet.length} bytes`);

      if (definition && definition.responseId) {
        pendingRequests.set(correlation_id, {
          commandName: tool_name,
          requestTime: Date.now(),
          responseId: definition.responseId,
          targetIp: target_ip  // [新增] 存储目标IP用于响应匹配
        });
      } else {
        pendingRequests.set(correlation_id, {
          commandName: tool_name,
          requestTime: Date.now(),
          responseId: null,
          targetIp: target_ip  // [新增] 存储目标IP用于响应匹配
        });
        console.warn(`[MCP Server] Command '${tool_name}' has no defined responseId. Cannot track response accurately.`);
      }

      // [修改] 发送消息时包含目标IP
      const mcpMessage = {
        targetIp: target_ip,
        packet: packet.toString('hex')
      };
      natsClient.publish('mcp.send_packet', JSON.stringify(mcpMessage));
      console.log(`--- [MCP Server] Published to 'mcp.send_packet' for ${target_ip} ---`);

    } catch (e) {
      console.error(`[MCP Server] Error processing tool call: ${e.message}`);
    }
  });
  
  // 订阅基站原始响应主题
  natsClient.subscribe('base_station.raw_response', (err, msg) => {
    if (err) { console.error('[MCP Server] Error receiving message on base_station.raw_response:', err); return; }
    try {
        // 修正：先解码为字符串，再JSON.parse
        const jsonStr = natsClient.sc.decode(msg.data);
        const { ip, hasSN, buffer } = JSON.parse(jsonStr);
        // buffer字段是hex字符串，转回Buffer
        const rawBuffer = Buffer.from(buffer, 'hex');
        const messageId = rawBuffer.readUInt16LE(4);

        console.log(`\n--- [MCP Server] Received Raw Base Station Response ---`);
        console.log(`  Message ID: 0x${messageId.toString(16)}`);
        console.log(`  Raw Buffer (Hex): ${rawBuffer.toString('hex')}`);
        console.log(`  Buffer Length: ${rawBuffer.length} bytes`);

        // [修正] 根据hasSN调整offset
        const offset = hasSN ? 32 : 12;
        const parsedData = parse(messageId, rawBuffer, offset);

        if (parsedData) {
            console.log(`  Parsed Data: ${JSON.stringify(parsedData)}`);
            
            // [修改] 传递源IP地址进行匹配
            const correlation_id = findAndRemoveMatchingRequest(messageId, sourceIp);

            if (correlation_id) {
                console.log(`  Matched response 0x${messageId.toString(16)} from ${sourceIp} to request ID ${correlation_id}`);
                const toolOutput = {
                    correlation_id: correlation_id,
                    tool_output: parsedData,
                    source_ip: sourceIp  // [新增] 在响应中包含源IP信息
                };
                natsClient.publish('llm.tool_results', JSON.stringify(toolOutput));
                console.log(`--- [MCP Server] Published Tool Output to 'llm.tool_results' ---`);
            } else {
                console.log(`  Received an unmatched response with ID 0x${messageId.toString(16)}. It might be an unsolicited message (e.g., alarm) or a late response.`);
            }
        } else {
            console.log(`  Could not parse response for message ID 0x${messageId.toString(16)}. No definition found.`);
        }
    } catch (e) {
        console.error(`[MCP Server] Error processing raw response: ${e.message}`);
    }
  });

  // [新增] 订阅基站状态更新主题
  natsClient.subscribe('base_station.status_update', (err, msg) => {
    if (err) { console.error('[MCP Server] Error receiving message on base_station.status_update:', err); return; }
    try {
      const statusData = JSON.parse(natsClient.sc.decode(msg.data));
      const { ip } = statusData;

      // 更新基站状态缓存
      baseStationStatus.set(ip, statusData);

      console.log(`[MCP Server] Updated status for base station ${ip}:`);
      console.log(`  状态: ${statusData.cellStateText}`);
      console.log(`  频点: UL=${statusData.ulEarfcn}, DL=${statusData.dlEarfcn}`);
      console.log(`  PCI: ${statusData.pci}, 带宽: ${statusData.bandwidthText}`);

    } catch (e) {
      console.error(`[MCP Server] Error processing base station status update: ${e.message}`);
    }
  });

  // 定期清理超时的请求
  setInterval(() => {
    const now = Date.now();
    const timeout = 30000; // 30秒超时
    for (const [correlationId, requestInfo] of pendingRequests.entries()) {
      if (now - requestInfo.requestTime > timeout) {
        console.warn(`[MCP Server] Request ${correlationId} timed out. Command: ${requestInfo.commandName}`);
        pendingRequests.delete(correlationId);
      }
    }
  }, 10000);
  
  // 提供工具定义接口
  natsClient.subscribe('mcp.get_llm_tools', (err, msg) => {
    if (err) { console.error('[MCP Server] Error receiving message on mcp.get_llm_tools:', err); return; }
    
    console.log('\n--- [MCP Server] Received Request for LLM Tools ---');
    const tools = generateTools();
    
    if (msg.reply) {
      natsClient.publish(msg.reply, JSON.stringify(tools));
      console.log(`--- [MCP Server] Replied with ${tools.length} tools ---`);
    }
  });
  
  console.log('[MCP Server] Running and waiting for messages.');
}

main().catch(err => {
  console.error('[MCP Server] Unhandled error during startup:', err);
  process.exit(1);
});

process.on('SIGINT', async () => {
  console.log('[MCP Server] SIGINT received. Attempting graceful shutdown...');
  
  const shutdownTimeout = 5000;
  const timeoutId = setTimeout(() => {
    console.error('[MCP Server] Graceful shutdown timed out. Forcing exit.');
    process.exit(1);
  }, shutdownTimeout);

  try {
    await natsClient.close();
    console.log('[MCP Server] NATS connection closed successfully.');
    clearTimeout(timeoutId);
    process.exit(0);
  } catch (error) {
    console.error('[MCP Server] Error during NATS connection close:', error.message);
    clearTimeout(timeoutId);
    process.exit(1);
  }
});
