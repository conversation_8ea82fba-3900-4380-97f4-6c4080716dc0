// mcp_server/nats_client.js
const { connect, StringCodec } = require('nats');

let natsConnection;
const sc = StringCodec();

/**
 * 连接到 NATS 服务器
 * @returns {Promise<void>}
 */
async function initialize() {
  try {
    // 从 udp_server.js 和 auth_server/app.js 中可以看到NATS服务器地址
    natsConnection = await connect({ servers: "nats://113.45.77.38:4222" });
    console.log(`[NATS Client] Connected to ${natsConnection.getServer()}`);
    
    // 监听连接状态
    (async () => {
      for await (const status of natsConnection.status()) {
        console.info(`[NATS Client] Status changed to ${status.type}`);
      }
    })().then();

  } catch (err) {
    console.error(`[NATS Client] Error connecting to NATS: ${err.message}`);
    throw err; // 抛出错误，让主程序知道连接失败
  }
}

/**
 * 发布消息到指定的 NATS 主题
 * @param {string} subject - 主题
 * @param {Buffer|string} payload - 消息内容
 */
function publish(subject, payload) {
  if (!natsConnection) {
    console.error('[NATS Client] Cannot publish, not connected to NATS.');
    return;
  }
  const data = typeof payload === 'string' ? sc.encode(payload) : payload;
  natsConnection.publish(subject, data);
}

/**
 * 订阅一个 NATS 主题
 * @param {string} subject - 主题
 * @param {function(Error|null, any): void} callback - 收到消息时的回调函数
 * @returns {import('nats').Subscription}
 */
function subscribe(subject, callback) {
  if (!natsConnection) {
    console.error('[NATS Client] Cannot subscribe, not connected to NATS.');
    return null;
  }
  const subscription = natsConnection.subscribe(subject);
  (async () => {
    for await (const msg of subscription) {
      // 对于二进制数据，直接使用 msg.data (Buffer)
      // 对于字符串，使用 sc.decode(msg.data)
      callback(null, msg);
    }
  })().then();
  console.log(`[NATS Client] Subscribed to '${subject}'`);
  return subscription;
}

/**
 * 断开与 NATS 服务器的连接
 * @returns {Promise<void>}
 */
async function close() {
  if (natsConnection) {
    console.log('[NATS Client] Closing NATS connection...');
    await natsConnection.close();
  }
}

module.exports = {
  initialize,
  publish,
  subscribe,
  close,
  sc, // 导出 StringCodec 方便其他模块使用
};
