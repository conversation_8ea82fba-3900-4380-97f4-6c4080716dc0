# 基站通信协议加解密方案

## 1. 概述

本文档描述了一种基于消息头中TransId字段的简单加解密方案，用于保护基站与应用程序之间的通信，防止被Wireshark等工具轻易分析。该方案不依赖任何第三方库，同时保持了原有消息结构的完整性。

基站和应用程序之间的通信协议的报文格式是：消息+消息体。

基站的IP地址是前端网页定义的主机的ip，端口号默认为3345。注意，该端口号为接受端口，基站发送数据包给应用程序时为随机的端口号。
注意：auth失败，即使之前进行过成功auth流程，也会导致该基站板子视为没有进行auth。auth失败的原因：auth码不对或者其他的设备的码发到本设备。

## 2. 消息头结构

消息头有两种格式：

### 2.1 带SN号的消息头（32字节）
```
u32FrameHeader  U32  0x5555AAAA  消息帧头
u16MsgType      U16  MessageID   消息ID
u16MsgLength    U16  /           通信信息是可变长度
u16frame        U16  0xFF00:FDD  用于指示当前系统工作模式
                     0x00FF:TDD
u16SubSysCode   U16  1~65535     (1)最高1bit用于指示基站发送给客户端的数据是否传输完成，0：传输完成；1代表传输未完成。
                                 (2)低15bit：消息传输的TransId，0是无效值，1~0x8FFFF。
u8BoardSn[20]   U8   单板SN字符   基站单板的SN号，每块基站单板都不同。
```

### 2.2 不带SN号的消息头（12字节）
```
u32FrameHeader  U32  0x5555AAAA  消息帧头
u16MsgType      U16  MessageID   消息ID
u16MsgLength    U16  /           通信信息是可变长度
u16frame        U16  0xFF00:FDD  用于指示当前系统工作模式
                     0x00FF:TDD
u16SubSysCode   U16  1~65535     (1)最高1bit用于指示基站发送给客户端的数据是否传输完成，0：传输完成；1代表传输未完成。
                                 (2)低15bit：消息传输的TransId，0是无效值，1~0x8FFFF。
```

## 3. 加密原理

加密方案利用消息头中的TransId字段（u16SubSysCode的低15位）作为加密种子的一部分。每次通信使用不同的TransId值，结合预设的密钥，生成唯一的加密种子，然后对消息体（头部之后的部分）进行XOR加密。

### 3.1 加密流程

1. 生成一个随机的TransId值（范围：1-32767）
2. 将TransId值设置到消息头的u16SubSysCode字段（低15位）
3. 基于TransId和预设密钥生成加密种子
4. **根据消息头格式，使用生成的种子对消息体进行XOR加密：**
   - 对于带SN号的消息，跳过头部32字节，从第32字节（下标32）开始加密
   - 对于不带SN号的消息，跳过头部12字节，从第12字节（下标12）开始加密

### 3.2 解密流程

1. 从消息头的u16SubSysCode字段提取TransId值
2. 基于TransId和预设密钥生成相同的加密种子
3. **根据消息头格式，使用生成的种子对消息体进行XOR解密（与加密操作相同）：**
   - 对于带SN号的消息，从第32字节（下标32）开始解密
   - 对于不带SN号的消息，从第12字节（下标12）开始解密

## 4. Java实现（应用端）

```java
/**
 * 消息加密工具，用于与基站进行安全通信
 */
public class MessageEncryption {
    // 可更改的加密密钥
    private static final byte[] ENCRYPTION_KEY = {0x5A, 0x3C, 0x7E, 0x19, 0x2F, 0x6D, 0x4B, 0x8A};
    
    /**
     * 加密消息
     * @param message 原始消息字节数组
     * @return 加密后的消息字节数组
     */
    public static byte[] encryptMessage(byte[] message) {
        if (message == null || message.length < 12) {
            return message; // 消息太短，无法加密
        }
        
        // 创建消息副本，避免修改原始消息
        byte[] encrypted = message.clone();
        
        // 生成TransId (1-32767)作为加密种子的一部分
        // 仅使用15位 (0x7FFF)，符合TransId字段规范
        int transId = (int)(Math.random() * 0x7FFF) + 1;
        
        // 将TransId设置到SubSysCode字段（字节10-11）
        // 最高位为0（传输完成）
        encrypted[10] = (byte)(transId & 0xFF);
        encrypted[11] = (byte)((transId >> 8) & 0x7F); // 保持最高位为0
        
        // 使用TransId作为加密种子
        byte[] seed = generateSeed(transId);
        
        // 跳过头部（12字节）并加密消息体
        for (int i = 12; i < encrypted.length; i++) {
            // 与种子中的字节进行XOR运算
            encrypted[i] = (byte)(encrypted[i] ^ seed[i % seed.length]);
        }
        
        return encrypted;
    }
    
    /**
     * 解密消息
     * @param encryptedMessage 加密的消息字节数组
     * @return 解密后的消息字节数组
     */
    public static byte[] decryptMessage(byte[] encryptedMessage) {
        // 对于XOR加密，解密操作与加密相同
        // 但需要先从消息中提取TransId
        if (encryptedMessage == null || encryptedMessage.length < 12) {
            return encryptedMessage; // 消息太短，无法解密
        }
        
        byte[] decrypted = encryptedMessage.clone();
        
        // 从SubSysCode字段提取TransId
        int transId = (decrypted[10] & 0xFF) | ((decrypted[11] & 0x7F) << 8);
        
        // 生成与加密时相同的种子
        byte[] seed = generateSeed(transId);
        
        // 跳过头部（12字节）并解密消息体
        for (int i = 12; i < decrypted.length; i++) {
            decrypted[i] = (byte)(decrypted[i] ^ seed[i % seed.length]);
        }
        
        return decrypted;
    }
    
    /**
     * 基于TransId生成加密种子
     * @param transId TransId值 (1-32767)
     * @return 用于加密的字节数组
     */
    private static byte[] generateSeed(int transId) {
        byte[] seed = new byte[ENCRYPTION_KEY.length];
        
        // 将TransId与密钥混合，创建唯一的种子
        for (int i = 0; i < seed.length; i++) {
            // 使用各种操作组合TransId和密钥
            int value = ENCRYPTION_KEY[i] ^ 
                        ((transId >> (i % 4)) & 0xFF) ^ 
                        ((transId << (7 - (i % 8))) & 0xFF);
            seed[i] = (byte)(value & 0xFF);
        }
        
        return seed;
    }
    
    /**
     * 检查消息是否已加密
     * @param message 要检查的消息字节数组
     * @return 如果消息已加密则返回true
     */
    public static boolean isMessageEncrypted(byte[] message) {
        if (message == null || message.length < 12) {
            return false;
        }
        
        // 检查TransId是否已设置（非零）
        int transId = (message[10] & 0xFF) | ((message[11] & 0x7F) << 8);
        return transId != 0;
    }
}
```

## 5. 基站端实现

### 5.1 C++实现

```cpp
#include <stdint.h>
#include <stdlib.h>
#include <string.h>

/**
 * 消息加密/解密工具，用于安全通信
 */
class MessageEncryption {
public:
    /**
     * 解密消息
     * @param encryptedMessage 加密的消息缓冲区
     * @param length 消息长度
     * @return 新分配的解密消息缓冲区（调用者必须释放）
     */
    static uint8_t* decryptMessage(const uint8_t* encryptedMessage, size_t length) {
        if (encryptedMessage == NULL || length < 12) {
            // 消息太短，无法解密
            return NULL;
        }
        
        // 为解密消息分配内存
        uint8_t* decrypted = (uint8_t*)malloc(length);
        if (decrypted == NULL) {
            return NULL; // 内存分配失败
        }
        
        // 复制加密消息
        memcpy(decrypted, encryptedMessage, length);
        
        // 从SubSysCode字段提取TransId
        // 假设最高位为0（传输完成）
        uint16_t transId = decrypted[10] | ((decrypted[11] & 0x7F) << 8);
        
        // 如果TransId为0，则消息未加密
        if (transId == 0) {
            return decrypted;
        }
        
        // 生成与加密时相同的种子
        uint8_t seed[8];
        generateSeed(transId, seed);
        
        // 跳过头部（12字节）并解密消息体
        for (size_t i = 12; i < length; i++) {
            decrypted[i] = decrypted[i] ^ seed[i % sizeof(seed)];
        }
        
        return decrypted;
    }
    
    /**
     * 加密消息
     * @param message 原始消息缓冲区
     * @param length 消息长度
     * @param transId 要使用的TransId (1-32767)
     * @return 新分配的加密消息缓冲区（调用者必须释放）
     */
    static uint8_t* encryptMessage(const uint8_t* message, size_t length, uint16_t transId) {
        if (message == NULL || length < 12 || transId == 0 || transId > 0x7FFF) {
            // 无效参数
            return NULL;
        }
        
        // 为加密消息分配内存
        uint8_t* encrypted = (uint8_t*)malloc(length);
        if (encrypted == NULL) {
            return NULL; // 内存分配失败
        }
        
        // 复制原始消息
        memcpy(encrypted, message, length);
        
        // 设置SubSysCode字段中的TransId
        // 最高位为0（传输完成）
        encrypted[10] = transId & 0xFF;
        encrypted[11] = (transId >> 8) & 0x7F;
        
        // 生成加密种子
        uint8_t seed[8];
        generateSeed(transId, seed);
        
        // 跳过头部（12字节）并加密消息体
        for (size_t i = 12; i < length; i++) {
            encrypted[i] = encrypted[i] ^ seed[i % sizeof(seed)];
        }
        
        return encrypted;
    }
    
    /**
     * 检查消息是否已加密
     * @param message 要检查的消息缓冲区
     * @param length 消息长度
     * @return 如果已加密则返回1，否则返回0
     */
    static int isMessageEncrypted(const uint8_t* message, size_t length) {
        if (message == NULL || length < 12) {
            return 0;
        }
        
        // 检查TransId是否已设置（非零）
        uint16_t transId = message[10] | ((message[11] & 0x7F) << 8);
        return (transId != 0) ? 1 : 0;
    }

private:
    // 加密密钥 - 必须与Java实现匹配
    static const uint8_t ENCRYPTION_KEY[8];
    
    /**
     * 基于TransId生成加密种子
     * @param transId TransId值 (1-32767)
     * @param seed 生成的种子的输出缓冲区（至少8字节）
     */
    static void generateSeed(uint16_t transId, uint8_t* seed) {
        // 将TransId与密钥混合，创建唯一的种子
        for (int i = 0; i < 8; i++) {
            // 使用各种操作组合TransId和密钥
            uint8_t value = ENCRYPTION_KEY[i] ^ 
                           ((transId >> (i % 4)) & 0xFF) ^ 
                           ((transId << (7 - (i % 8))) & 0xFF);
            seed[i] = value;
        }
    }
};

// 定义加密密钥 - 必须与Java实现匹配
const uint8_t MessageEncryption::ENCRYPTION_KEY[8] = {0x5A, 0x3C, 0x7E, 0x19, 0x2F, 0x6D, 0x4B, 0x8A};
```

### 5.2 C语言实现（基站端）

```c
#include <stdint.h>
#include <stdlib.h>
#include <string.h>

/* 加密密钥 - 必须与Java实现匹配 */
static const uint8_t ENCRYPTION_KEY[8] = {0x5A, 0x3C, 0x7E, 0x19, 0x2F, 0x6D, 0x4B, 0x8A};

/**
 * 基于TransId生成加密种子
 * @param transId TransId值 (1-32767)
 * @param seed 生成的种子的输出缓冲区（至少8字节）
 */
static void generateSeed(uint16_t transId, uint8_t* seed) {
    int i;
    /* 将TransId与密钥混合，创建唯一的种子 */
    for (i = 0; i < 8; i++) {
        /* 使用各种操作组合TransId和密钥 */
        uint8_t value = ENCRYPTION_KEY[i] ^ 
                       ((transId >> (i % 4)) & 0xFF) ^ 
                       ((transId << (7 - (i % 8))) & 0xFF);
        seed[i] = value;
    }
}

/**
 * 检查消息是否已加密
 * @param message 要检查的消息缓冲区
 * @param length 消息长度
 * @return 如果已加密则返回1，否则返回0
 */
int isMessageEncrypted(const uint8_t* message, size_t length) {
    if (message == NULL || length < 12) {
        return 0;
    }
    
    /* 检查TransId是否已设置（非零） */
    uint16_t transId = message[10] | ((message[11] & 0x7F) << 8);
    return (transId != 0) ? 1 : 0;
}

/**
 * 解密消息
 * @param encryptedMessage 加密的消息缓冲区
 * @param length 消息长度
 * @return 新分配的解密消息缓冲区（调用者必须释放）
 */
uint8_t* decryptMessage(const uint8_t* encryptedMessage, size_t length) {
    size_t i;
    uint16_t transId;
    uint8_t seed[8];
    uint8_t* decrypted;
    
    if (encryptedMessage == NULL || length < 12) {
        /* 消息太短，无法解密 */
        return NULL;
    }
    
    /* 为解密消息分配内存 */
    decrypted = (uint8_t*)malloc(length);
    if (decrypted == NULL) {
        return NULL; /* 内存分配失败 */
    }
    
    /* 复制加密消息 */
    memcpy(decrypted, encryptedMessage, length);
    
    /* 从SubSysCode字段提取TransId */
    /* 假设最高位为0（传输完成） */
    transId = decrypted[10] | ((decrypted[11] & 0x7F) << 8);
    
    /* 如果TransId为0，则消息未加密 */
    if (transId == 0) {
        return decrypted;
    }
    
    /* 生成与加密时相同的种子 */
    generateSeed(transId, seed);
    
    /* 跳过头部（12字节）并解密消息体 */
    for (i = 12; i < length; i++) {
        decrypted[i] = decrypted[i] ^ seed[i % sizeof(seed)];
    }
    
    return decrypted;
}

/**
 * 加密消息
 * @param message 原始消息缓冲区
 * @param length 消息长度
 * @param transId 要使用的TransId (1-32767)
 * @return 新分配的加密消息缓冲区（调用者必须释放）
 */
uint8_t* encryptMessage(const uint8_t* message, size_t length, uint16_t transId) {
    size_t i;
    uint8_t seed[8];
    uint8_t* encrypted;
    
    if (message == NULL || length < 12 || transId == 0 || transId > 0x7FFF) {
        /* 无效参数 */
        return NULL;
    }
    
    /* 为加密消息分配内存 */
    encrypted = (uint8_t*)malloc(length);
    if (encrypted == NULL) {
        return NULL; /* 内存分配失败 */
    }
    
    /* 复制原始消息 */
    memcpy(encrypted, message, length);
    
    /* 设置SubSysCode字段中的TransId */
    /* 最高位为0（传输完成） */
    encrypted[10] = transId & 0xFF;
    encrypted[11] = (transId >> 8) & 0x7F;
    
    /* 生成加密种子 */
    generateSeed(transId, seed);
    
    /* 跳过头部（12字节）并加密消息体 */
    for (i = 12; i < length; i++) {
        encrypted[i] = encrypted[i] ^ seed[i % sizeof(seed)];
    }
    
    return encrypted;
}

/**
 * 在原地解密消息（不分配新内存）
 * @param message 加密的消息缓冲区
 * @param length 消息长度
 * @return 成功返回1，失败返回0
 */
int decryptMessageInPlace(uint8_t* message, size_t length) {
    size_t i;
    uint16_t transId;
    uint8_t seed[8];
    
    if (message == NULL || length < 12) {
        /* 消息太短，无法解密 */
        return 0;
    }
    
    /* 从SubSysCode字段提取TransId */
    transId = message[10] | ((message[11] & 0x7F) << 8);
    
    /* 如果TransId为0，则消息未加密 */
    if (transId == 0) {
        return 1; /* 成功但无需解密 */
    }
    
    /* 生成与加密时相同的种子 */
    generateSeed(transId, seed);
    
    /* 跳过头部（12字节）并解密消息体 */
    for (i = 12; i < length; i++) {
        message[i] = message[i] ^ seed[i % sizeof(seed)];
    }
    
    return 1; /* 成功 */
}

/**
 * 在原地加密消息（不分配新内存）
 * @param message 原始消息缓冲区
 * @param length 消息长度
 * @param transId 要使用的TransId (1-32767)
 * @return 成功返回1，失败返回0
 */
int encryptMessageInPlace(uint8_t* message, size_t length, uint16_t transId) {
    size_t i;
    uint8_t seed[8];
    
    if (message == NULL || length < 12 || transId == 0 || transId > 0x7FFF) {
        /* 无效参数 */
        return 0;
    }
    
    /* 设置SubSysCode字段中的TransId */
    message[10] = transId & 0xFF;
    message[11] = (transId >> 8) & 0x7F;
    
    /* 生成加密种子 */
    generateSeed(transId, seed);
    
    /* 跳过头部（12字节）并加密消息体 */
    for (i = 12; i < length; i++) {
        message[i] = message[i] ^ seed[i % sizeof(seed)];
    }
    
    return 1; /* 成功 */
}
```

## 6. 使用示例

### 6.1 Java（应用端）使用示例

```java
// 发送消息前加密
byte[] originalMessage = createMessagePacket(); // 您现有的消息创建代码
byte[] encryptedMessage = MessageEncryption.encryptMessage(originalMessage);
sendToBaseStation(encryptedMessage); // 您现有的发送方法

// 接收并解密消息
byte[] receivedMessage = receiveFromBaseStation(); // 您现有的接收方法
if (MessageEncryption.isMessageEncrypted(receivedMessage)) {
    byte[] decryptedMessage = MessageEncryption.decryptMessage(receivedMessage);
    processMessage(decryptedMessage); // 您现有的消息处理
} else {
    processMessage(receivedMessage); // 未加密，按原样处理
}
```

### 6.2 C++（基站端）使用示例

```cpp
// 接收并解密消息
void handleReceivedMessage(const uint8_t* receivedMessage, size_t length) {
    if (MessageEncryption::isMessageEncrypted(receivedMessage)) {
        // 消息已加密，解密它
        uint8_t* decryptedMessage = MessageEncryption::decryptMessage(receivedMessage, length);
        if (decryptedMessage != NULL) {
            processMessage(decryptedMessage, length); // 您现有的消息处理
            free(decryptedMessage); // 释放分配的内存
        }
    } else {
        // 消息未加密，按原样处理
        processMessage(receivedMessage, length);
    }
}

// 发送消息前加密
void sendEncryptedMessage(const uint8_t* message, size_t length) {
    // 生成1到32767之间的随机TransId
    uint16_t transId = (rand() % 32767) + 1;
    
    // 加密消息
    uint8_t* encryptedMessage = MessageEncryption::encryptMessage(message, length, transId);
    if (encryptedMessage != NULL) {
        sendToApp(encryptedMessage, length); // 您现有的发送方法
        free(encryptedMessage); // 释放分配的内存
    }
}
```

### 6.3 C语言（基站端）使用示例

```c
/* 接收并解密消息 */
void handleReceivedMessage(const uint8_t* receivedMessage, size_t length) {
    if (isMessageEncrypted(receivedMessage)) {
        /* 消息已加密，解密它 */
        uint8_t* decryptedMessage = decryptMessage(receivedMessage, length);
        if (decryptedMessage != NULL) {
            processMessage(decryptedMessage, length); /* 您现有的消息处理 */
            free(decryptedMessage); /* 释放分配的内存 */
        }
    } else {
        /* 消息未加密，按原样处理 */
        processMessage(receivedMessage, length);
    }
}

/* 使用原地解密方式处理消息 - 更高效的实现 */
void handleReceivedMessageEfficient(uint8_t* receivedMessage, size_t length) {
    if (isMessageEncrypted(receivedMessage)) {
        /* 消息已加密，原地解密 */
        if (decryptMessageInPlace(receivedMessage, length)) {
            processMessage(receivedMessage, length); /* 您现有的消息处理 */
        }
    } else {
        /* 消息未加密，按原样处理 */
        processMessage(receivedMessage, length);
    }
}

/* 发送消息前加密 */
void sendEncryptedMessage(const uint8_t* message, size_t length) {
    /* 生成1到32767之间的随机TransId */
    uint16_t transId = (rand() % 32767) + 1;
    
    /* 加密消息 */
    uint8_t* encryptedMessage = encryptMessage(message, length, transId);
    if (encryptedMessage != NULL) {
        sendToApp(encryptedMessage, length); /* 您现有的发送方法 */
        free(encryptedMessage); /* 释放分配的内存 */
    }
}

/* 使用原地加密方式处理消息 - 更高效的实现 */
void sendEncryptedMessageEfficient(uint8_t* message, size_t length) {
    /* 生成1到32767之间的随机TransId */
    uint16_t transId = (rand() % 32767) + 1;
    
    /* 原地加密消息 */
    if (encryptMessageInPlace(message, length, transId)) {
        sendToApp(message, length); /* 您现有的发送方法 */
    }
}
```

## 7. 方案特点

1. **简单有效**：使用基于TransId派生的密钥进行XOR加密，在不知道算法的情况下难以解密。

2. **无第三方依赖**：仅使用Java和C/C++的标准库实现。

3. **TransId利用**：使用u16SubSysCode字段的低15位作为加密种子的一部分，使每条消息具有唯一的加密。

4. **兼容现有代码**：不修改现有消息结构，仅加密头部之后的负载部分。

5. **最小开销**：加解密过程快速，不会向消息添加额外字节。

6. **透明集成**：可以以最小的更改添加到现有代码中。

7. **内存优化**：C语言版本提供了原地加解密功能，减少内存分配和复制操作。

## 8. 安全性考虑

1. **密钥管理**：加密密钥（ENCRYPTION_KEY）在Java和C/C++实现中必须保持一致，且应定期更新。

2. **算法强度**：此方案提供基本的混淆，可以防止简单的协议分析，但不应被视为高强度加密。对于需要更高安全性的场景，应考虑使用标准的加密库。

3. **TransId唯一性**：应确保每次通信使用不同的TransId，以增加安全性。

4. **随机性**：在生产环境中，应使用更高质量的随机数生成器来生成TransId。

## 9. 实施建议

1. 在测试环境中先验证加解密功能，确保Java端和C/C++端能够正确互操作。

2. 逐步将加密集成到现有通信中，先从非关键消息开始，再扩展到所有消息。

3. 考虑添加消息完整性验证机制，例如简单的校验和，以检测消息是否被篡改。

4. 定期更新加密密钥，提高系统的长期安全性。

5. 对于资源受限的基站设备，优先使用C语言版本中的原地加解密功能，以减少内存使用。

## 10. 消息描述示例：授权串号查询 (0xF111)

### 10.1 查询消息（LMT->eNB）
- 消息名称：O_FL_LMT_TO_ENB_AUTH_SERIAL_QUERY
- 消息ID：0xF111
- 方向：LMT（前端/管理终端）发送到 eNB（基站）
- 描述：用于查询基站的授权串号。
- 消息体：无（仅消息头）

### 10.2 应答消息（eNB->LMT）
- 消息名称：O_FL_ENB_TO_LMT_AUTH_SERIAL_QUERY_ACK
- 消息ID：0xF112
- 方向：eNB（基站）发送到 LMT（前端/管理终端）
- 描述：返回基站的授权串号。

#### 消息结构：
| 参数             | 数据类型      | 取值范围 | 含义           |
|------------------|--------------|----------|----------------|
| WrmsgHeaderinfo  | wrMsgHeader  |          | 消息头(0xF112)  |
| authSerial[65]   | S8           | 字符串   | 授权串号，长度为64，字符串以'\0'结尾 |
| res[3]           | U8           |          | 保留字节        |

- 说明：
  - `authSerial`为授权串号，最大长度64字节，实际内容不足64字节时以'\0'结尾。
  - `res`为保留字节，填0。
  - 消息体总长度为68字节（authSerial[65] + res[3]）。
  - 消息头格式参考前文消息头定义。

## 11. 消息描述示例：授权密钥配置 (0xF113)

### 11.1 配置消息（LMT->eNB）
- 消息名称：O_FL_LMT_TO_ENB_AUTH_KEY_CFG
- 消息ID：0xF113
- 方向：LMT（前端/管理终端）发送到 eNB（基站）
- 描述：配置基站的授权认证密钥。
- 是否立即生效：是
- 重启是否保留配置：是

#### 消息结构：
| 参数             | 数据类型      | 取值范围 | 含义           |
|------------------|--------------|----------|----------------|
| WrmsgHeaderInfo  | wrMsgHeader  |          | 消息头(0xF113)  |
| authKey[17]      | S8           | 字符串   | 认证密钥，长度为16，字符串以'\0'结尾 |
| res[3]           | U8           |          | 保留字节        |

- 说明：
  - `authKey`为认证密钥，最大长度16字节，实际内容不足16字节时以'\0'结尾。
  - `res`为保留字节，填0。
  - 消息体总长度为20字节（authKey[17] + res[3]）。
  - 配置下发后立即生效，基站重启后配置仍然保留。

### 11.2 应答消息（eNB->LMT）
- 消息名称：O_FL_ENB_TO_LMT_AUTH_KEY_CFG_ACK
- 消息ID：0xF114
- 方向：eNB（基站）发送到 LMT（前端/管理终端）
- 描述：返回授权密钥配置结果。

#### 消息结构：
| 参数             | 数据类型      | 取值范围 | 含义           |
|------------------|--------------|----------|----------------|
| WrmsgHeaderInfo  | wrMsgHeader  |          | 消息头(0xF114)  |
| cfgResult        | U8           | 0,1      | 配置结果：0-成功，1-失败 |
| authResult       | U8           | 0,1      | 授权结果：0-成功，1-失败 |
| res[2]           | U8           |          | 保留字节        |

- 说明：
  - `cfgResult`为配置下发结果，0表示成功，1表示失败。
  - `authResult`为授权校验结果，0表示成功，1表示失败。
  - `res`为保留字节，填0。
  - 消息体总长度为4字节（cfgResult + authResult + res[2]）。
  - 消息头格式参考前文消息头定义。

## 4.4 心跳指示（eNB->LMT）（0xF010）
基站软件启动完成后，基站开始周期性（5秒）向客户端发送此消息，客户端收到此消息后，即可开始与基站的通信操作。心跳指示中携带小区状态、小区配置等参数信息。小区状态为"已激活态"时，后面的频点等小区配置信息才有效。由于配置消息会触发基站执行流程，为避免与当前流程冲突，只有小区状态为"去激活"和"已激活"时，客户端才可以下发配置信息给基站。

- 消息名称：O_FL_ENB_TO_LMT_SYS_INIT_SUCC_IND
- 消息ID：0xF010
- 方向：eNB（基站）发送到 LMT（客户端/管理终端）
- 周期：5秒

| 参数            | 数据类型     | 取值范围/说明 | 含义 |
|-----------------|-------------|--------------|------|
| WrmsgHeaderInfo | wrMsgHeader | /            | 消息头(0xF010) |
| CellState       | U16         | 0：小区IDLE态<br>1：扫频/同步进行中<br>2：小区激活中<br>3：小区激活态<br>4：小区去激活中<br>5：同步成功，REM处于ON状态<br>6：同步中 | 代表小区状态 |
| Band            | U16         | FDD:Band1/3/7<br>TDD:Band38/39/40/41 | 指示当前激活小区的BAND |
| ulEarfcn        | U32         | TDD:255<br>FDD:实际频点 | 上行频点 |
| dlEarfcn        | U32         | 实际频点     | 下行频点 |
| PLMN[7]         | U8          | 字符数组，以结束符结束，如"46000" | PLMN标识 |
| Bandwidth       | U8          | 25（5M, tdd+fdd）<br>50（10M, tdd+fdd）<br>75（15M, only fdd）<br>100（20M, tdd+fdd） | 系统带宽 |
| PCI             | U16         | 0~503        | 物理小区ID（PhysCellId） |
| TAC             | U16         | 0~65535      | 跟踪区码（TrackingAreaCode） |

- 说明：
  - CellState为3（小区激活态）时，Band、ulEarfcn、dlEarfcn、PLMN、Bandwidth、PCI、TAC等配置信息才有效。
  - 只有CellState为4（去激活）和3（已激活）时，客户端才可下发配置信息。

## 4.5 心跳应答（LMT->eNB）（0xF011）
客户端收到基站发出的心跳指示后，回复应答消息给基站，基站收到此消息，代表客户端到基站侧的下行链路正常。

- 消息名称：O_FL_LMT_TO_ENB_SYS_INIT_SUCC_RSP
- 消息ID：0xF011
- 方向：LMT（客户端/管理终端）发送到 eNB（基站）

| 参数            | 数据类型     | 取值范围/说明 | 含义 |
|-----------------|-------------|--------------|------|
| WrmsgHeaderInfo | wrMsgHeader | /            | 消息头(0xF011) |

- 说明：
  - 消息体为空，表示客户端到基站侧的下行链路正常。
  - 消息头格式参考前文消息头定义。

## 4.9.1 基站基本信息查询(0xF02B)

### 4.9.1.1 查询消息（LMT->eNB）
- 消息名称：O_FL_LMT_TO_ENB_BASE_INFO_QUERY
- 消息ID：0xF02B
- 方向：LMT（前端/管理终端）发送到 eNB（基站）
- 描述：用于客户端查询一些基站的基本信息，比如版本号，MAC地址，SN等。

#### 消息结构：
| 参数             | 数据类型      | 取值范围 | 含义           |
|------------------|--------------|----------|----------------|
| WrmsgHeaderInfo  | wrMsgHeader  | /        | 消息头(0xF02B) |
| u32EnbBaseInfoType | U32          | 0: 设备型号<br>1：硬件版本<br>2：软件版本<br>3：SN号<br>4：MAC地址<br>5：uboot版本号<br>6：板卡温度 | 查询信息的类型 |

### 4.9.1.2 应答消息（eNB -> LMT）
- 消息名称：O_FL_ENB_TO_LMT_BASE_INFO_QUERY_ACK
- 消息ID：0xF02C
- 方向：eNB（基站）发送到 LMT（前端/管理终端）
- 描述：返回基站的基本信息。

#### 消息结构：
| 参数             | 数据类型      | 取值范围 | 含义           |
|------------------|--------------|----------|----------------|
| WrmsgHeaderInfo  | wrMsgHeader  | /        | 消息头(0xF02C) |
| u32EnbBaseInfoType | U32          | 0: 设备型号<br>1：硬件版本<br>2：软件版本<br>3：序列号<br>4：MAC地址<br>5：uboot版本号<br>6：板卡温度 | 查询信息的类型 |
| u8EnbbaseInfo[100] | U8           | 字符串，以'0'结束 | 信息上报       |

## 4.7.14 辅PLMN列表配置(0xF060)
是否立即生效：否
重启是否保留配置：是

### 4.7.14.1 配置消息（LMT->eNB）
- 消息名称：O_FL_LMT_TO_ENB_SECONDARY_PLMNS_SET
- 消息ID：0xF060
- 方向：LMT（前端/管理终端）发送到 eNB（基站）
- 描述：此接口用于配置基站广播SIB1 中PLMN LIST字段中的非主PLMN。

#### 消息结构：
| 参数             | 数据类型      | 取值范围 | 含义           |
|------------------|--------------|----------|----------------|
| WrmsgHeaderInfo  | wrMsgHeader  | /        | 消息头(0xF060) |
| u8SecPLMNNum     | U8           | 1~5      | 辅PLMN的数目   |
| u8SecPLMNList[5][7]| U8           | 字符数组，以结束符结束eg:<br>“46000”<br>“46001” | 辅PLMN列表     |

### 4.7.14.2 应答消息（eNB -> LMT）
- 消息名称：O_FL_ENB_TO_LMT_SECONDARY_PLMNS_SET_ACK
- 消息ID：0xF061
- 方向：eNB（基站）发送到 LMT（前端/管理终端）
- 描述：返回辅PLMN列表配置结果。

#### 消息结构：
| 参数             | 数据类型      | 取值范围 | 含义           |
|------------------|--------------|----------|----------------|
| WrmsgHeaderInfo  | wrMsgHeader  | /        | 消息头(0xF061) |
| cfgResult        | U8           | 0,1      | 配置结果：0-成功，1-失败 |
| res[3]           | U8           |          | 保留字节        |

- 说明：
  - `cfgResult`为配置下发结果，0表示成功，1表示失败。
  - `res`为保留字节，填0。
  - 消息体总长度为4字节（cfgResult + res[3]）。
  - 消息头格式参考前文消息头定义。

## 4.7.4 基站重启配置(0xF00B)
是否立即生效：是
重启是否保留配置：否

### 4.7.4.1 配置消息（LMT->eNB）
- 消息名称：O_FL_LMT_TO_ENB_REBOOT_CFG
- 消息ID：0xF00B
- 方向：LMT（前端/管理终端）发送到 eNB（基站）
- 描述：此接口用于客户端指示基站执行reboot操作。基站收到此消息，先回复ACK，再执行reboot。基站处于任何状态都会处理该消息。

#### 消息结构：
| 参数             | 数据类型      | 取值范围 | 含义           |
|------------------|--------------|----------|----------------|
| WrmsgHeaderInfo  | wrMsgHeader  | /        | 消息头0xF00B   |
| SelfActiveCfg    | U32          | 0：reboot后自动激活小区<br>1：reboot后不自激活小区 | 指示基站重启后是否采用现有参数配置自动激活小区，该字段只有在定位版本中生效，围栏版本不需要判断该字节。 |

### 4.7.4.2 应答消息（eNB -> LMT）
- 消息名称：O_FL_ENB_TO_LMT_REBOOT_ACK
- 消息ID：0xF00C
- 方向：eNB（基站）发送到 LMT（前端/管理终端）
- 描述：基站重启应答。

#### 消息结构：
| 参数            | 数据类型     | 取值范围/说明 | 含义 |
|-----------------|-------------|--------------|------|
| WrmsgHeaderInfo | wrMsgHeader | /            | 消息头(0xF00C) |

- 说明：
  - 消息体为空。

## 4.9.2 

// ... existing code ... 
