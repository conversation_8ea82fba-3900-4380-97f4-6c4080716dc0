@echo off
REM MCP Server 统一启动脚本 (Windows版本)
REM 用于一键启动所有必要的服务：主服务、认证服务和MCP服务

setlocal enabledelayedexpansion

REM 颜色定义 (Windows CMD不支持颜色，使用echo代替)
set "INFO=[INFO]"
set "SUCCESS=[SUCCESS]"
set "WARNING=[WARNING]"
set "ERROR=[ERROR]"

echo %INFO% === MCP Server 统一启动脚本 (Windows) ===

REM 检查Node.js是否安装
echo %INFO% 检查 Node.js 安装状态...
node --version >nul 2>&1
if errorlevel 1 (
    echo %ERROR% Node.js 未安装，请先安装 Node.js
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo %INFO% 检测到 Node.js 版本: %NODE_VERSION%

REM 检查npm依赖
echo %INFO% 检查项目依赖...
if not exist "node_modules" (
    echo %WARNING% 未找到 node_modules，正在安装依赖...
    call npm install
    if errorlevel 1 (
        echo %ERROR% 主项目依赖安装失败
        pause
        exit /b 1
    )
)

REM 检查auth_server依赖
if not exist "auth_server\node_modules" (
    echo %WARNING% 未找到 auth_server 依赖，正在安装...
    cd auth_server
    call npm install
    if errorlevel 1 (
        echo %ERROR% auth_server 依赖安装失败
        cd ..
        pause
        exit /b 1
    )
    cd ..
)

echo %SUCCESS% 依赖检查完成

REM 检查必要文件
echo %INFO% 检查必要文件...
set "files_missing=0"

if not exist "index.js" (
    echo %ERROR% 缺少必要文件: index.js
    set "files_missing=1"
)

if not exist "udp_server.js" (
    echo %ERROR% 缺少必要文件: udp_server.js
    set "files_missing=1"
)

if not exist "auth_server\app.js" (
    echo %ERROR% 缺少必要文件: auth_server\app.js
    set "files_missing=1"
)

if not exist "mcp_server\app.js" (
    echo %ERROR% 缺少必要文件: mcp_server\app.js
    set "files_missing=1"
)

if "%files_missing%"=="1" (
    echo %ERROR% 存在缺失文件，无法启动服务
    pause
    exit /b 1
)

echo %SUCCESS% 文件检查完成

REM 检查是否安装了concurrently
echo %INFO% 检查 concurrently 工具...
call npx concurrently --version >nul 2>&1
if errorlevel 1 (
    echo %WARNING% 未找到 concurrently，正在安装...
    call npm install -g concurrently
    if errorlevel 1 (
        echo %WARNING% concurrently 安装失败，将使用传统方式启动
        goto :traditional_start
    )
)

REM 使用concurrently启动服务
echo %INFO% 使用 concurrently 启动所有服务...
echo %INFO% 服务说明:
echo %INFO%   - 主服务 (index.js): UDP通信和Web界面 - 端口 3000
echo %INFO%   - 认证服务 (auth_server/app.js): 设备认证管理 - 端口 3001
echo %INFO%   - MCP服务 (mcp_server/app.js): LLM工具调用处理
echo %INFO% 按 Ctrl+C 停止所有服务
echo.

call npx concurrently ^
    --prefix "[{name}]" ^
    --names "MAIN,AUTH,MCP" ^
    --prefix-colors "cyan,magenta,yellow" ^
    "node index.js" ^
    "node auth_server/app.js" ^
    "node mcp_server/app.js"

goto :end

:traditional_start
echo %WARNING% 使用传统方式启动服务...
echo %INFO% 正在启动主服务 (index.js)...
start "Main Service" cmd /c "node index.js"

echo %INFO% 正在启动认证服务 (auth_server/app.js)...
start "Auth Service" cmd /c "node auth_server/app.js"

echo %INFO% 正在启动MCP服务 (mcp_server/app.js)...
start "MCP Service" cmd /c "node mcp_server/app.js"

echo %SUCCESS% 所有服务已在独立窗口中启动
echo %INFO% 请查看各个服务窗口的运行状态
echo %INFO% 关闭对应窗口即可停止相应服务

:end
echo.
echo %INFO% 脚本执行完成
pause
