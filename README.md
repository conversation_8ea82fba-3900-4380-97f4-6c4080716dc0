# MCP Server - 基站智能管理系统

基于LLM的基站设备智能管理系统，支持自然语言交互、实时状态监控和多基站管理。

## 🚀 快速开始

### 一键启动 (推荐)

```bash
# Linux/macOS
./start_all.sh

# Windows
start_all.bat

# 或使用npm
npm run start:all
```

### 访问服务
- 主服务Web界面: http://localhost:3000
- 认证服务管理界面: http://localhost:3001

## 📋 系统架构

本系统包含三个核心服务：

| 服务 | 端口 | 功能 |
|------|------|------|
| **主服务** | 3000 | UDP通信、Web界面、基站控制 |
| **认证服务** | 3001 | 设备认证管理、SN码管理 |
| **MCP服务** | - | LLM工具调用处理、自然语言交互 |

## ✨ 核心功能

### 🤖 自然语言交互
- **基站状态查询**: "现在系统接入了几个基站板？"
- **功率配置**: "设置IP为*************的基站功率为50dB"
- **批量操作**: "将所有基站的功率设置为30dB"

### 📊 实时状态监控
- 基站IP、端口、SN码
- 小区状态、频点、PCI
- 心跳状态、连接时间
- 干扰检测、信号质量

### 🔧 多基站管理
- 支持多基站并发连接
- IP路由和消息关联
- 智能配置和批量操作
- 状态缓存和实时更新

## 🛠️ 安装和配置

### 环境要求
- Node.js 18+
- npm 或 yarn

### 安装依赖
```bash
# 主项目依赖
npm install

# 认证服务依赖
cd auth_server && npm install
```

### 启动方式

#### 方式1: 统一启动脚本
```bash
# 检查环境
./start_all.sh --check

# 启动所有服务
./start_all.sh

# 查看帮助
./start_all.sh --help
```

#### 方式2: npm脚本
```bash
npm run start:all      # 启动所有服务
npm run start:main     # 仅启动主服务
npm run start:auth     # 仅启动认证服务
npm run start:mcp      # 仅启动MCP服务
```

#### 方式3: 手动启动
```bash
# 终端1
node index.js

# 终端2  
node auth_server/app.js

# 终端3
node mcp_server/app.js
```

## 🧪 测试

```bash
# 运行所有测试
npm run test:comprehensive

# 基站状态测试
npm run test:status

# 自然语言交互测试
npm run test:natural

# 端到端测试
npm run test:e2e
```

## 📁 项目结构

```
pwsjiami/
├── docs/                    # 📚 文档目录
│   ├── startup_guide.md     # 启动指南
│   ├── mcp_server_design.md # 设计文档
│   └── ...
├── test/                    # 🧪 测试脚本
│   ├── test_comprehensive.js
│   ├── test_base_station_status.js
│   └── ...
├── mcp_server/              # 🤖 MCP服务
│   ├── app.js
│   └── protocol/
├── auth_server/             # 🔐 认证服务
│   └── app.js
├── start_all.sh            # 🚀 Linux/macOS启动脚本
├── start_all.bat           # 🚀 Windows启动脚本
├── index.js                # 🏠 主服务入口
└── udp_server.js           # 📡 UDP通信服务
```

## 🔧 配置说明

### 端口配置
- 主服务: 3000 (可通过环境变量PORT修改)
- 认证服务: 3001
- UDP服务: 8080
- NATS: 4222

### 环境变量
```bash
PORT=3000                    # 主服务端口
AUTH_PORT=3001              # 认证服务端口
UDP_PORT=8080               # UDP服务端口
NATS_URL=nats://localhost:4222  # NATS连接地址
```

## 📖 使用指南

### 基站连接
1. 确保基站设备网络连通
2. 在认证服务中添加基站SN码
3. 基站会自动连接并发送心跳

### 自然语言操作
1. 访问主服务Web界面
2. 在聊天框中输入自然语言指令
3. 系统会自动解析并执行相应操作

### 状态监控
- 实时查看基站连接状态
- 监控基站参数变化
- 查看历史操作记录

## 🚨 故障排除

### 常见问题
1. **端口被占用**: 使用`lsof -i :3000`查找占用进程
2. **依赖安装失败**: 删除node_modules重新安装
3. **NATS连接失败**: 检查4222端口是否被占用
4. **基站连接失败**: 检查网络连通性和SN码配置

### 日志查看
- 服务日志会输出到控制台
- 使用启动脚本时有颜色区分
- 可设置DEBUG环境变量查看详细日志

## 📚 文档

- [启动指南](docs/startup_guide.md) - 详细的启动和配置说明
- [设计文档](docs/mcp_server_design.md) - 系统架构和设计原理
- [开发计划](docs/mcp_server_development_plan.md) - 开发进度和规划
- [基站状态监控](docs/README_BASE_STATION_STATUS.md) - 状态监控功能说明
- [LLM使用指南](docs/LLM_USAGE_GUIDE.md) - 自然语言交互指南

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📄 许可证

ISC License

---

**快速开始**: `./start_all.sh` 或 `npm run start:all`
