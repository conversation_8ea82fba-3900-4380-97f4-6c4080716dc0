# 多基站板UDP通信系统优化方案

## 1. 目标

- 前端网页可动态输入多个基站板的 IP 和端口号，批量提交给后端。
- 后端 UDP 服务可同时与多个基站板通信，独立管理每个设备的状态、消息收发、认证授权等。
- NATS、API 交互均支持多设备并行，所有消息都带设备唯一标识。

---

## 2. 总体设计

### 2.1 前端

- 提供表单/列表，允许用户输入多个基站板的 IP 和端口号。
- 启动 UDP 服务时，将基站板列表作为数组 POST 给后端。

### 2.2 后端

- `UdpServer` 类维护一个 `devices` Map，key 为基站板的 IP（不能用端口），value 为设备状态对象及前端设置的目标端口号。
- 所有 UDP 消息收发、认证、授权、NATS 交互都以设备为单位。
- API 支持批量启动、动态增删设备。

### 2.3 NATS 及 API 交互

- NATS 消息体带设备唯一标识。
- 下发指令 API 支持指定目标设备。

---

## 3. 详细修改步骤与测试方法

### 步骤一：前端页面改造

#### 1.1 设计多设备输入表单

- 支持添加、删除、编辑多组 IP/端口。
- 校验输入格式。

**测试方法：**
- 手动输入多组 IP/端口，检查能否正确添加、删除、编辑。
- 检查输入校验（如 IP 格式、端口范围）。

**任务清单：**
- [x] 前端页面可正常添加、删除、编辑多组 IP/端口。（已完成，2024-06-09，功能和校验通过测试）
- [x] 输入校验功能生效。（已完成，2024-06-09，功能和校验通过测试）
- [x] 相关代码已合并到主分支。（已完成，2024-06-09）

#### 1.2 启动时批量提交

- 将所有基站板信息组成数组，通过 HTTP POST 提交给后端。

**测试方法：**
- 打开浏览器开发者工具，确认请求体格式正确（如 `boards: [{ip, port}, ...]`）。
- 后端能收到完整的基站板列表。

**任务清单：**
- [x] 前端页面点击启动后，能正确发送包含多个基站的 POST 请求。（已完成，2024-06-09，初步测试通过）
- [x] 后端日志能打印收到的基站板列表。（已完成，2024-06-09）
- [x] 相关代码已合并到主分支。（已完成，2024-06-09）

---

### 步骤二：后端 UDP 服务改造

> **重要注意：**
> - 基站板发UDP报文给后端时，源端口是随机的，不能用 rinfo.port 作为设备唯一标识。
> - 后端收到UDP报文时，只能用 rinfo.address（IP）来识别设备，端口号应忽略。
> - 发送UDP报文给基站板时，必须使用前端设置的目标IP和端口号，不能用收到报文的源端口。

#### 2.1 修改 UdpServer 启动参数

- `start(listenIp, listenPort, boards)`，`boards` 为基站板数组。
- 设备状态 Map 的 key 只用 IP，value 里保存前端设置的目标端口号。

**测试方法：**
- 用 Postman 或 curl 发送带多个基站的启动请求，后端能正确解析参数。
- 检查 Map 结构，key 只为 IP，端口号为 value 属性。

**任务清单：**
- [x] UdpServer 支持接收并解析多个基站参数，设备Map以IP为key。（已完成，2024-06-09，初步测试通过）
- [x] 日志输出所有基站参数。（已完成，2024-06-09）
- [x] 相关代码已合并到主分支。（已完成，2024-06-09）

#### 2.2 设备状态管理

- 新增 `this.devices = new Map();`，key 为基站板 IP。
- 每个 value 为 `{ ip, port, sn, hasSN, heartbeatCount, ... }`，port为前端设置的目标端口。

**测试方法：**
- 启动后，检查 `this.devices` 是否包含所有基站板（key为IP，value含端口）。
- 日志输出所有设备的初始状态。

**任务清单：**
- [x] 设备状态 Map 正确初始化并维护所有基站状态（key为IP，含端口）。（已完成，2024-06-09，初步测试通过）
- [x] 日志能输出所有设备的状态。（已完成，2024-06-09）
- [x] 相关代码已合并到主分支。（已完成，2024-06-09）

#### 2.3 消息收发逻辑调整

- 收到 UDP 消息时，只用 rinfo.address（IP）查找/创建设备状态，忽略 rinfo.port。
- 发送消息时，始终使用前端设置的目标IP和端口号。

**测试方法：**
- 用多个基站板模拟器/真实设备同时发送心跳，后端能分别识别、处理（仅用IP识别）。
- 后端能向指定设备下发消息，始终用前端设置的端口，设备能收到。
- 验证收到UDP报文的源端口与下发端口不一致时，通信仍正常。

**任务清单：**
- [x] 多设备消息收发逻辑生效，能区分不同设备（仅用IP识别）。
- [x] 发送消息始终用前端设置的端口。
- [x] 日志能区分不同设备的消息。
- [x] 相关代码已合并到主分支。

#### 2.4 认证、授权、心跳等流程独立化（详细规划）

##### 目标

- 每个基站板的认证、授权、心跳计数等状态完全独立，互不影响。
- 支持多个基站板同时上线、认证、授权、心跳检测，流程互不干扰。

##### 设计与实现细节

###### 1. 设备状态结构设计

每个设备（基站板）在 `devices` Map 中的 value 建议结构如下：

```js
{
  ip: '*************',
  port: 50001,             // 前端设置的目标端口
  sn: null,                // 设备SN，初始为null，收到心跳或查询后赋值
  hasSN: false,            // 是否带SN
  heartbeatCount: 0,       // 心跳计数
  lastHeartbeat: null,     // 最后一次心跳时间
  authenticated: false,    // 是否已授权
  lastAuthDate: null,      // 上次授权日期
  authSerial: null,        // 设备序列号
  authCode: null,          // 授权码
  // ...其他需要的状态
}
```

**任务清单：**
- [x] 设备状态结构已实现并在代码中应用（key为IP，含端口）。
- [x] 日志能输出每个设备的详细状态。
- [x] 相关代码已合并到主分支。

###### 2. 心跳流程独立化

- 每收到一次心跳包，根据 rinfo.address（IP）找到对应设备，单独递增其 `heartbeatCount`。
- 判断心跳包长度，独立设置该设备的 `hasSN`。
- SN 变化、心跳异常等都只影响当前设备。

**测试方法：**
- 用两个或以上基站板同时发送心跳，分别观察每个设备的 `heartbeatCount` 是否独立递增。
- 模拟某一设备掉线，其他设备心跳不受影响。

**任务清单：**
- [x] 多设备心跳计数、SN、hasSN 独立维护。（已完成，2024-06-09，初步测试通过）
- [x] 日志能区分并输出每个设备的心跳状态。（已完成，2024-06-09）
- [x] 相关代码已合并到主分支。（已完成，2024-06-09）

###### 3. 认证流程独立化

- 每个设备独立维护 `authenticated`、`lastAuthDate` 等认证状态。
- 认证流程（如收到3次心跳后发起认证查询）只针对当前设备。
- 认证成功后，仅更新当前设备的状态。

**测试方法：**
- 多个设备同时上线，分别走认证流程，互不干扰。
- 某一设备认证失败，不影响其他设备。

**任务清单：**
- [x] 多设备认证流程独立，互不影响。（已完成，2024-06-09，初步测试通过）
- [x] 日志能区分认证流程。（已完成，2024-06-09）
- [x] 相关代码已合并到主分支。（已完成，2024-06-09）

###### 4. 授权流程独立化

- 每个设备独立维护 `authSerial`、`authCode` 等授权信息。
- 授权流程（如收到授权应答、配置PLMN等）只针对当前设备。
- 授权成功后，仅更新当前设备的状态。

**测试方法：**
- 多个设备同时进行授权，分别观察每个设备的授权状态。
- 某一设备授权失败，不影响其他设备。

**任务清单：**
- [x] 多设备授权流程独立，互不影响。(已完成, 2025-07-01, 后端发送逻辑改造完毕)
- [x] 日志能区分授权流程。(已完成, 2025-07-01, 发送日志已包含目标IP)
- [x] 相关代码已合并到主分支。(已完成, 2025-07-01)

###### 5. 消息收发独立化

- 所有下发消息（如认证查询、授权配置、PLMN配置等）都需指定目标设备的 IP，端口用前端设置的目标端口。
- 发送时从 `devices` Map 查找目标设备，独立组包、加密、发送。
- 绝不能用收到UDP报文的源端口作为下发端口。

**测试方法：**
- 前端下发指令到不同设备，后端能正确路由到目标设备（用IP查找，端口用前端设置）。
- 日志能区分不同设备的消息收发。
- 验证下发端口始终为前端设置的端口。

**任务清单：**
- [x] 多设备下发消息独立，能正确路由（用IP查找，端口用前端设置）。(已完成, 2025-07-01, sendMessageToBoard 及 send... 系列函数改造完毕)
- [x] 日志能区分下发消息。(已完成, 2025-07-01, 发送日志已包含目标IP)
- [x] 相关代码已合并到主分支。(已完成, 2025-07-01)

###### 日志与监控建议

- 日志输出需带设备唯一标识（如 IP），便于排查问题。
- 可定期输出所有设备的状态快照，便于监控。

**任务清单：**
- [x] 日志输出格式统一，能区分每个设备。(已完成, 2025-07-01, 所有收发日志均已包含设备IP)
- [x] 监控脚本/页面能展示所有设备状态。(已完成, 2025-07-01, 已添加 GET /api/udp/devices 接口)

###### 测试用例建议

1. **多设备同时上线，心跳计数独立递增。**
2. **多设备分别走认证、授权流程，互不影响。**
3. **某一设备掉线/异常，其他设备不受影响。**
4. **前端下发指令到不同设备，后端能正确路由。**
5. **日志能区分不同设备的所有操作。**
6. **收到UDP报文的源端口与下发端口不一致时，通信仍正常。**

**任务清单：**
- [x] 所有测试用例通过。(已完成, 2025-07-01, 详见 test_report.md)
- [x] 测试报告归档。(已完成, 2025-07-01, 详见 test_report.md)

---

### 2.5 其他相关章节细化

#### 2.5.1 设备动态增删

- 支持前端动态添加、删除设备，后端实时更新 `devices` Map。
- 删除设备时，清理其状态，关闭相关连接。

**测试方法：**
- 前端动态添加、删除设备，后端能正确响应，状态同步。

**任务清单：**
- [x] 动态增删设备功能上线。(已完成, 2025-07-01, 已添加 addDevice/removeDevice 方法和对应API)
- [x] 日志能反映设备增删。(已完成, 2025-07-01, 方法内已添加日志)
- [x] 相关代码已合并到主分支。(已完成, 2025-07-01)

#### 2.5.2 状态持久化与恢复（可选）

- 可将设备状态持久化到数据库，支持服务重启后恢复。
- 适用于大规模设备管理场景。

**测试方法：**
- 重启服务后，设备状态能正确恢复。

**任务清单：**
- [ ] 状态持久化功能上线。
- [ ] 重启后状态恢复正常。
- [ ] 相关代码已合并到主分支。

---

## 3. 交付与验收标准（细化）

- 多设备心跳、认证、授权流程完全独立，互不影响。
- 支持动态增删设备，状态实时同步。
- 日志、监控能区分和追踪每个设备的全流程。
- 所有测试用例通过，系统稳定可靠。

---

如需更详细的代码实现建议、接口定义或测试脚本示例，请随时告知！ 