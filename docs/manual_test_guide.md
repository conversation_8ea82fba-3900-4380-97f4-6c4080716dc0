# MCP Server 手动测试指南

## 1. 概述

本指南提供了手动测试 MCP Server 与实际基站板集成的方法。您需要分别启动项目的各个核心组件，然后运行测试客户端来验证整个数据流。

**重要提示：**

*   请确保您的 **NATS 服务器** (`nats://************:4222`) 正在运行且可访问。
*   请确保您的 **基站板** 已经通电，并且其IP地址和端口与 `index.js` 中配置的自动启动参数 (`*************:3345`) 相符。
*   您的计算机IP (`************`) 必须是 `index.js` 启动UDP服务器时监听的IP。

### 1.1 当前支持的查询功能

根据 `mcp_server/protocol/definitions.js` 文件中的当前定义，MCP Server 目前支持以下查询功能：

*   **`query_base_info` (查询基站基本信息)**
    *   **消息ID**: `0xF02B`
    *   **描述**: 查询基站的基本信息，例如设备型号、软硬件版本、SN号或板卡温度。
    *   **参数**: `u32EnbBaseInfoType` (U32 类型，表示查询信息的类型，如 0:设备型号, 1:硬件版本, 2:软件版本, 3:SN号, 4:MAC地址, 5:uboot版本号, 6:板卡温度)。

**如何测试：**

`test_mcp_client.js` 脚本已预配置为调用 `query_base_info` 功能，并传入 `u32EnbBaseInfoType: 3`，即查询基站的 **SN号**。当您运行 `test_mcp_client.js` 时，它将自动执行此查询。

如果您希望测试 `22.md` 中定义的其他查询功能（例如 GPS 经纬高度查询、同步信息查询等），您需要先完成 **任务 4.1：全面填充协议定义**，将这些查询接口的定义添加到 `mcp_server/protocol/definitions.js` 中。之后，您可以修改 `test_mcp_client.js` 来调用这些新的查询功能。

## 2. 测试步骤和命令

您需要打开**至少四个独立的终端窗口**来分别启动各个服务。

### **终端 1：启动主服务 (index.js)**

这个服务会启动 Express 服务器，并根据 `AUTO_TEST_MODE` 环境变量自动启动 UDP 服务器。

```bash
# 进入项目根目录
cd /home/<USER>/prj/pwsjiami/

# 启动主服务，并启用自动化测试模式，这将自动启动UDP服务器
AUTO_TEST_MODE=true node index.js
```
**预期输出：** 您会看到类似以下日志：
```
Server is running on port 3000
[Auto Test Mode] Enabled. Starting UDP server automatically.
[Auto Test Mode] UDP Server started. Listening on ************:3345, targeting board *************:3345.
```

### **终端 2：启动认证服务 (auth_server/app.js)**

这是一个独立的认证服务，虽然本次测试不直接使用其功能，但作为完整系统的一部分，建议启动。

```bash
# 进入认证服务目录
cd /home/<USER>/prj/pwsjiami/auth_server/

# 确保安装了依赖 (如果尚未安装)
npm install

# 启动认证服务
node app.js
```
**预期输出：** 您会看到类似以下日志：
```
Connected to the SQLite database.
Authorizations table created or already exists.
Connected to NATS at nats://************:4222
Subscribed to 'au_query' NATS topic.
Auth server listening at http://localhost:3006
```

### **终端 3：启动 MCP Server (mcp_server/app.js)**

这是我们新开发的 MCP Server，它将处理 LLM 的指令并与 `udp_server` 交互。

```bash
# 进入 MCP Server 目录
cd /home/<USER>/prj/pwsjiami/mcp_server/

# 确保安装了依赖 (如果尚未安装)
npm install

# 启动 MCP Server
node app.js
```
**预期输出：** 您会看到类似以下日志：
```
[MCP Server] Starting...
[MCP Server] Initializing...
[NATS Client] Connected to nats://************:4222
[NATS Client] Subscribed to 'llm.invoke_tool'
[NATS Client] Subscribed to 'base_station.raw_response'
[NATS Client] Subscribed to 'mcp.get_llm_tools'
[MCP Server] Running and waiting for messages.
```

### **终端 4：运行测试客户端 (test_mcp_client.js)**

在所有服务都启动并稳定运行后，运行这个客户端来触发测试流程。

```bash
# 进入项目根目录
cd /home/<USER>/prj/pwsjiami/

# 运行测试客户端
node test_mcp_client.js
```
**预期输出：**
*   您会看到 `[Test Client] Connecting to NATS...` 和 `[Test Client] Connected.`。
*   然后是 `--- Step 1: Requesting LLM Tools ---` 和打印出的工具定义（JSON 格式）。
*   接着是 `--- Step 2: Invoking a Tool (query_base_info) ---` 和 `[Test Client] Published tool call...`。
*   **最关键的是**，您会看到 `[Test Client] Received matching tool result...` 以及从基站返回的SN号信息（如果基站响应了）。

## 3. 观察和验证

在测试过程中，请密切关注所有终端的日志输出，以验证整个数据流是否正确：

1.  `test_mcp_client` 发出工具调用请求。
2.  `mcp_server` 收到请求，构建数据包，并发布到 `mcp.send_packet`。
3.  `udp_server` 收到数据包，通过UDP发送给基站。
4.  `udp_server` 收到基站的UDP响应，解密后发布到 `base_station.raw_response`。
5.  `mcp_server` 收到原始响应，解析后，将最终结果发布到 `llm.tool_results`。
6.  `test_mcp_client` 收到并打印出最终的JSON结果。

如果遇到任何问题或错误，请将完整的错误信息复制给我，我将帮助您诊断。