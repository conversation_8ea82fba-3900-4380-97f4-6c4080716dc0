# 优化版 MCP Server 设计文档

## 1. 引言与目标

本文档旨在阐述一个优化的、全自动化的 MCP (Machine-to-Client Protocol) Server 设计方案。该 MCP Server 的核心目标是作为大型语言模型 (LLM) 与基站板之间的高级抽象层，使 LLM 能够通过自然语言指令与基站板进行通信，而无需了解底层复杂的 UDP 协议细节。

此优化方案的核心是**基于一份标准化的、人工维护的协议定义文档 (`22.md`)**。它将这些预定义的接口动态转化为 LLM 可调用的工具（Function Calling），从而在保证接口精确性的前提下，实现高度的自动化和便捷的维护。

## 2. 总体架构

MCP Server 将作为一个独立的 Node.js 服务运行，并通过 NATS 与 `udp_server.js` 模块进行集成。整个系统分为以下几个核心部分：

```mermaid
graph TD;
    A[LLM 客户端应用] -->|用户自然语言指令 + LLM Tools Schema| B(大型语言模型 LLM);
    B -->|Function Call (tool_name, tool_args, correlation_id)| C(MCP Server);
    C -->|调用 'udp_server.js' 方法 (转换参数)| D(UDP Server 'udp_server.js');
    D -->|加密 UDP 消息| E[基站板];
    E -->|加密 UDP 响应| D;
    D -->|解密 UDP 响应, 结构化解析| C;
    C -->|Tool Output (structured_data, correlation_id)| B;
    B -->|用户友好回复| A;

    subgraph MCP Server 内部
        C_Sub1[协议接口注册表 (基于 22.md)]
        C_Sub2[LLM Tool 生成器]
        C_Sub3[NATS 命令订阅/响应发布]
        C_Sub4[动态消息构建/解析器]
        C_Sub1 --> C_Sub2;
        C_Sub1 --> C_Sub4;
        C_Sub3 <--> C_Sub4;
    end

    C --> C_Sub1;
    C --> C_Sub2;
    C --> C_Sub3;
    C --> C_Sub4;
```

## 3. 关键组件与职责

### 3.1. 协议接口注册表 (核心定义)

*   **职责：** 这是 MCP Server 的核心知识库。它是一个在代码中维护的、结构化的数据集合（例如一个 JavaScript 对象或 Map），精确地定义了所有可用的基站命令。
*   **数据来源：** **手动根据 `22.md` 文档进行定义和更新**。当 `22.md` 中有接口变更或新增时，开发者需要同步更新这个注册表。
*   **实现方法：**
    1.  创建一个或多个专门的定义文件（例如 `protocol_definitions.js`）。
    2.  在文件中，为 `22.md` 里描述的每一个**请求消息**（LMT -> eNB）创建一个条目。
    3.  每个条目应包含：
        *   **消息名称 (Message Name)**: 例如 `O_FL_LMT_TO_ENB_SYS_MODE_CFG`。
        *   **消息ID (Message ID)**: 例如 `0xF001`。
        *   **功能描述 (Description)**: 从 `22.md` 中提炼的、简洁明了的功能说明，这将作为 LLM 工具的描述。
        *   **参数定义 (Parameters Schema)**: 一个结构化的对象，详细描述消息体中的每一个参数，包括：
            *   `name`: 参数名 (如 `sysMode`)。
            *   `type`: 数据类型 (如 `U32`, `S8`, `String`, `Array`)。
            *   `description`: 参数的含义和取值范围说明。
            *   `size`: 对于固定长度的类型（如数组），需要标明其长度。
        *   **关联的响应消息ID (Response ID)**: 例如 `0xF002`。
    4.  同样，为所有**响应/上报消息**（eNB -> LMT）也创建类似的定义，用于后续的响应解析。

*   **产物：** 一个静态的、在程序启动时加载到内存的**"协议接口注册表"**。

### 3.2. LLM 工具定义的自动化生成

*   **职责：** 根据"协议接口注册表"中定义的信息，动态生成 LLM API 所需的 `tools` Schema 定义。
*   **实现方法：**
    1.  **遍历注册表：** 在服务启动时，遍历注册表中所有定义好的**请求消息**。
    2.  **构建 LLM 函数定义：** 为每个请求消息生成一个 JSON 格式的 LLM `Function Calling` 定义：
        *   `name`：将消息名称转换为 LLM 友好的 `snake_case` 格式（例如 "基站重启配置" -> `configure_base_station_reboot`）。
        *   `description`：直接使用注册表中为该消息提供的功能描述。
        *   `parameters`：将注册表中的参数定义（Parameters Schema）映射为 JSON Schema。
            *   **类型映射：** 将协议中的自定义类型（如 `U32`, `U8[16]`）映射为 LLM 可处理的 `integer`, `string`, `array` 等标准 JSON Schema 类型，并在 `description` 中提供详细的格式说明。
            *   自动识别并标记必填参数。
*   **产物：** 一个动态生成的 LLM `tools` Schema 数组。MCP Server 可以通过一个内部 API 端点或 NATS 主题将此 Schema 提供给 LLM 客户端。

### 3.3. MCP Server 核心逻辑 (NATS 集成与动态路由)

*   **职责：** 作为 LLM 与基站 UDP 通信之间的中介，接收 LLM 的工具调用指令，动态构建并发送 UDP 消息，解析基站响应，并将结果反馈给 LLM。
*   **实现方法：**
    1.  **NATS 命令订阅：** MCP Server 订阅一个预定义 NATS 主题（例如 `llm.invoke_tool`）。LLM 客户端将其决定调用的工具信息作为 JSON 消息发布到此主题，包含 `tool_name`、`tool_args` 和一个唯一的 `correlation_id`。
    2.  **动态消息构建：**
        *   收到 NATS 命令后，MCP Server 根据 `tool_name` 从"协议接口注册表"中查找对应的基站消息定义（包括其 `id` 和参数 `schema`）。
        *   校验 `tool_args` 中的参数是否符合注册表中的定义。
        *   将 `tool_args` 中的参数根据协议 `schema` 进行类型转换和打包，生成一个符合协议规范的二进制 Buffer。
        *   调用 `udp_server.js` 实例的通用发送方法（例如 `udpServerInstance.sendMessageToBoard(builtMessageBuffer)`），将构建好的消息发送到基站。
    3.  **响应处理与 NATS 发布：**
        *   **改造 `udp_server.js`：** `udp_server.js` 的 `server.on('message', ...)` 回调在接收到基站响应并解密后，不再自己解析具体业务。它将：
            *   从消息头中解析出 `msgType` (消息ID)。
            *   将 `msgType` 和解密后的消息体 `payload` 一起发布到一个 NATS 主题（例如 `mcp.raw_response`）。
        *   **MCP Server 订阅响应：** MCP Server 订阅 `mcp.raw_response` 主题。
            *   收到消息后，根据 `msgType` 在**协议接口注册表**中查找对应的响应消息定义。
            *   根据定义好的 `schema`，动态地从 `payload` 中解析出各个字段，将其转换为结构化的 JSON 对象。
            *   将解析后的 JSON 响应数据，连同原始请求的 `correlation_id`（通过内部请求-响应映射表关联），发布到另一个 NATS 响应主题（例如 `llm.tool_results`）。
    4.  **请求-响应关联：**
        *   由于 UDP 是无连接的，MCP Server 需要维护一个临时的内部映射表（例如，使用 `correlation_id` 作为键），关联发出的请求和预期的响应消息类型，以便在收到响应时进行匹配和路由。同时需要实现超时和错误处理机制。

### 3.4. LLM 客户端层 (您的应用程序)

*   **职责和实现方法与原设计相同**：负责与用户交互，获取并注入`tools`，处理`function_call`，通过NATS与MCP Server通信，并将最终结果交给LLM生成回复。

## 4. 数据流示例

数据流与原设计完全一致，区别仅在于 MCP Server 获取协议定义的方式从“动态解析Markdown”变为了“查询内部写好的注册表”。

## 5. 关键技术

*   **Node.js, NATS, LLM Function Calling API, `udp_server.js`** 保持不变。
*   **不再需要 Markdown 解析库**。

## 6. 对 `22.md` 的要求

*   `22.md` 作为项目**最权威的协议说明文档**，是开发和维护的“单一事实来源”。
*   当需要修改或添加基站接口时，**首先更新 `22.md` 文档**，然后**同步修改 MCP Server 代码中的“协议接口注册表”**。

## 7. 挑战与考量

*   **手动维护成本**：当协议频繁变更时，需要人工同步更新 `22.md` 文档和代码中的注册表，需要有良好的开发流程来保证一致性。
*   **类型转换的精确性**：将 JSON 类型参数精确地打包成二进制 `Buffer`，以及反向解析，是核心的开发工作，需要细致和充分的测试。
*   **请求-响应匹配**：在 UDP 这种无连接协议上，确保请求和响应的精确匹配仍然是一个需要仔细设计的关键点。
*   **错误处理**：需要设计健壮的错误处理和日志记录机制，以便在通信、协议解析或参数校验失败时，能够快速定位问题。

## 8. 总结

这个优化后的设计方案，通过用**人工维护的、精确的协议注册表**替换**自动化但可能脆弱的Markdown解析**，在保证核心功能（LLM通过自然语言控制基站）不变的前提下，极大地提升了系统的**稳定性、可靠性和可维护性**。它清晰地划分了“协议定义”（文档和注册表）和“协议执行”（MCP Server）的界限，是一个非常务实且强大的工程实践。