# 优化版 MCP Server 设计文档

## 1. 引言与目标

本文档旨在阐述一个优化的、全自动化的 MCP (Machine-to-Client Protocol) Server 设计方案。该 MCP Server 的核心目标是作为大型语言模型 (LLM) 与基站板之间的高级抽象层，使 LLM 能够通过自然语言指令与基站板进行通信，而无需了解底层复杂的 UDP 协议细节。

此优化方案的核心是**基于一份标准化的、人工维护的协议定义文档 (`22.md`)**。它将这些预定义的接口动态转化为 LLM 可调用的工具（Function Calling），从而在保证接口精确性的前提下，实现高度的自动化和便捷的维护。

## 2. 总体架构

MCP Server 将作为一个独立的 Node.js 服务运行，并通过 NATS 与 `udp_server.js` 模块进行集成。整个系统分为以下几个核心部分：

```mermaid
graph TD;
    A[LLM 客户端应用] -->|用户自然语言指令 + LLM Tools Schema| B(大型语言模型 LLM);
    B -->|Function Call (tool_name, tool_args, correlation_id, target_ip)| C(MCP Server);
    C -->|NATS消息 (targetIp, packet)| D(UDP Server 'udp_server.js');
    D -->|加密 UDP 消息| E[基站板];
    E -->|加密 UDP 响应| D;
    D -->|解密 UDP 响应, 结构化解析| C;
    C -->|Tool Output (structured_data, correlation_id, source_ip)| B;
    B -->|用户友好回复| A;

    subgraph MCP Server 内部
        C_Sub1[协议接口注册表 (基于 22.md)]
        C_Sub2[LLM Tool 生成器]
        C_Sub3[NATS 命令订阅/响应发布]
        C_Sub4[动态消息构建/解析器]
        C_Sub5[基站状态缓存 (实时监控)]
        C_Sub6[虚拟工具处理器]
        C_Sub1 --> C_Sub2;
        C_Sub1 --> C_Sub4;
        C_Sub3 <--> C_Sub4;
        C_Sub5 --> C_Sub6;
        C_Sub3 <--> C_Sub5;
    end

    C --> C_Sub1;
    C --> C_Sub2;
    C --> C_Sub3;
    C --> C_Sub4;
    C --> C_Sub5;
    C --> C_Sub6;

    subgraph UDP Server 增强
        D_Sub1[心跳报文解析]
        D_Sub2[基站状态提取]
        D_Sub3[多设备管理 (IP路由)]
        D_Sub4[NATS状态发布]
        D_Sub1 --> D_Sub2;
        D_Sub2 --> D_Sub4;
        D_Sub3 <--> D_Sub4;
    end

    D --> D_Sub1;
    D --> D_Sub2;
    D --> D_Sub3;
    D --> D_Sub4;
    D_Sub4 -->|base_station.status_update| C_Sub5;
```

## 3. 关键组件与职责

### 3.1. 协议接口注册表 (核心定义)

*   **职责：** 这是 MCP Server 的核心知识库。它是一个在代码中维护的、结构化的数据集合（例如一个 JavaScript 对象或 Map），精确地定义了所有可用的基站命令。
*   **数据来源：** **手动根据 `22.md` 文档进行定义和更新**。当 `22.md` 中有接口变更或新增时，开发者需要同步更新这个注册表。
*   **实现方法：**
    1.  创建一个或多个专门的定义文件（例如 `protocol_definitions.js`）。
    2.  在文件中，为 `22.md` 里描述的每一个**请求消息**（LMT -> eNB）创建一个条目。
    3.  每个条目应包含：
        *   **消息名称 (Message Name)**: 例如 `O_FL_LMT_TO_ENB_SYS_MODE_CFG`。
        *   **消息ID (Message ID)**: 例如 `0xF001`。
        *   **功能描述 (Description)**: 从 `22.md` 中提炼的、简洁明了的功能说明，这将作为 LLM 工具的描述。
        *   **参数定义 (Parameters Schema)**: 一个结构化的对象，详细描述消息体中的每一个参数，包括：
            *   `name`: 参数名 (如 `sysMode`)。
            *   `type`: 数据类型 (如 `U32`, `S8`, `String`, `Array`)。
            *   `description`: 参数的含义和取值范围说明。
            *   `size`: 对于固定长度的类型（如数组），需要标明其长度。
        *   **关联的响应消息ID (Response ID)**: 例如 `0xF002`。
    4.  同样，为所有**响应/上报消息**（eNB -> LMT）也创建类似的定义，用于后续的响应解析。

*   **产物：** 一个静态的、在程序启动时加载到内存的**"协议接口注册表"**。
*   **新增虚拟工具支持：** 注册表现在支持虚拟工具定义，这些工具不发送UDP消息，而是直接从MCP Server内部缓存返回数据（如基站状态查询）。

### 3.2. LLM 工具定义的自动化生成

*   **职责：** 根据"协议接口注册表"中定义的信息，动态生成 LLM API 所需的 `tools` Schema 定义。
*   **实现方法：**
    1.  **遍历注册表：** 在服务启动时，遍历注册表中所有定义好的**请求消息**。
    2.  **构建 LLM 函数定义：** 为每个请求消息生成一个 JSON 格式的 LLM `Function Calling` 定义：
        *   `name`：将消息名称转换为 LLM 友好的 `snake_case` 格式（例如 "基站重启配置" -> `configure_base_station_reboot`）。
        *   `description`：直接使用注册表中为该消息提供的功能描述。
        *   `parameters`：将注册表中的参数定义（Parameters Schema）映射为 JSON Schema。
            *   **类型映射：** 将协议中的自定义类型（如 `U32`, `U8[16]`）映射为 LLM 可处理的 `integer`, `string`, `array` 等标准 JSON Schema 类型，并在 `description` 中提供详细的格式说明。
            *   自动识别并标记必填参数。
*   **产物：** 一个动态生成的 LLM `tools` Schema 数组。MCP Server 可以通过一个内部 API 端点或 NATS 主题将此 Schema 提供给 LLM 客户端。

### 3.3. 基站状态监控系统 (新增核心功能)

*   **职责：** 实时监控基站状态，从UDP Server的心跳报文中解析基站信息，为LLM提供基站状态查询能力。
*   **实现方法：**
    1.  **UDP Server心跳增强：**
        *   在UDP Server的心跳处理逻辑中，解析心跳报文提取基站信息：IP、端口、SN、小区状态、频点、PCI、带宽等。
        *   将解析的状态信息缓存到设备对象中，并通过NATS发布到`base_station.status_update`主题。
    2.  **MCP Server状态订阅：**
        *   订阅`base_station.status_update`主题，接收基站状态更新。
        *   维护一个基站状态缓存Map，以IP地址为键存储最新的基站状态信息。
    3.  **虚拟工具实现：**
        *   实现`query_base_station_status`虚拟工具，直接从缓存返回基站状态，无需发送UDP消息。
        *   支持查询所有基站或指定IP的基站状态。
*   **支持的基站信息：**
    *   基站信息：IP地址、端口、SN号、心跳次数、最后心跳时间
    *   小区状态：状态码、状态描述、PLMN、物理小区ID(PCI)、跟踪区码(TAC)
    *   频率信息：频段、上行频点、下行频点、系统带宽、带宽描述
    *   技术参数：更新时间戳、数据新鲜度

### 3.4. 多基站支持与IP路由 (架构增强)

*   **职责：** 支持多基站环境，确保消息能够正确路由到指定基站并匹配响应。
*   **实现方法：**
    1.  **强制target_ip参数：** 所有工具调用必须包含`target_ip`参数，指定目标基站IP地址。
    2.  **请求-响应关联增强：**
        *   在请求映射表中同时存储`correlation_id`和`target_ip`。
        *   响应匹配时同时验证响应ID和源IP地址，防止多基站环境下的响应串扰。
    3.  **UDP Server路由：** UDP Server根据`targetIp`将消息发送到对应的基站设备。
*   **产物：** 完全支持多基站并发操作的可靠消息路由机制。

### 3.5. MCP Server 核心逻辑 (NATS 集成与动态路由)

*   **职责：** 作为 LLM 与基站 UDP 通信之间的中介，接收 LLM 的工具调用指令，动态构建并发送 UDP 消息，解析基站响应，并将结果反馈给 LLM。
*   **实现方法：**
    1.  **NATS 命令订阅：** MCP Server 订阅一个预定义 NATS 主题（例如 `llm.invoke_tool`）。LLM 客户端将其决定调用的工具信息作为 JSON 消息发布到此主题，包含 `tool_name`、`tool_args`、`correlation_id` 和 `target_ip`（目标基站IP）。
    2.  **虚拟工具处理：**
        *   检查工具定义中的`isVirtual`标志，如果是虚拟工具则直接处理并返回结果。
        *   虚拟工具从内部缓存获取数据，无需发送UDP消息。
    3.  **动态消息构建：**
        *   收到 NATS 命令后，MCP Server 根据 `tool_name` 从"协议接口注册表"中查找对应的基站消息定义（包括其 `id` 和参数 `schema`）。
        *   校验 `tool_args` 中的参数是否符合注册表中的定义。
        *   将 `tool_args` 中的参数根据协议 `schema` 进行类型转换和打包，生成一个符合协议规范的二进制 Buffer。
        *   通过NATS发布到`mcp.send_packet`主题，消息包含`targetIp`和`packet`，由UDP Server根据IP路由到对应基站。
    4.  **响应处理与 NATS 发布：**
        *   **UDP Server响应发布：** `udp_server.js` 在接收到基站响应并解密后：
            *   从消息头中解析出 `msgType` (消息ID)。
            *   将 `msgType`、解密后的消息体 `payload` 和源IP地址一起发布到 `mcp.raw_response` 主题。
        *   **MCP Server 订阅响应：** MCP Server 订阅 `mcp.raw_response` 主题。
            *   收到消息后，根据 `msgType` 在**协议接口注册表**中查找对应的响应消息定义。
            *   根据定义好的 `schema`，动态地从 `payload` 中解析出各个字段，将其转换为结构化的 JSON 对象。
            *   通过双重匹配（响应ID + 源IP）找到对应的请求，将解析后的 JSON 响应数据连同 `correlation_id` 和 `source_ip` 发布到 `llm.tool_results` 主题。
    5.  **请求-响应关联增强：**
        *   MCP Server 维护一个内部映射表，存储 `correlation_id`、`target_ip`、预期响应类型和超时时间。
        *   响应匹配时同时验证响应ID和源IP地址，确保多基站环境下的响应正确性。
        *   实现超时清理机制，防止映射表无限增长。

### 3.6. LLM 客户端层 (您的应用程序)

*   **职责和实现方法与原设计相同**：负责与用户交互，获取并注入`tools`，处理`function_call`，通过NATS与MCP Server通信，并将最终结果交给LLM生成回复。

## 4. 数据流示例

数据流与原设计完全一致，区别仅在于 MCP Server 获取协议定义的方式从“动态解析Markdown”变为了“查询内部写好的注册表”。
### 4.1. 基站状态查询流程 (虚拟工具)

1. **用户自然语言输入**：客户询问"现在系统接入了几个基站板？"
2. **LLM工具调用**：LLM识别意图，调用`query_base_station_status`工具
3. **MCP Server处理**：识别为虚拟工具，直接从基站状态缓存返回数据
4. **结果返回**：返回所有基站的详细状态信息
5. **LLM自然语言回答**：将结构化数据转换为用户友好的自然语言回答

### 4.2. 功率配置流程 (标准工具)

1. **用户自然语言输入**：客户说"设置IP为*************的基站功率为50dB"
2. **LLM参数解析**：LLM提取IP地址和功率值，计算衰减参数
3. **MCP Server消息构建**：根据协议定义构建UDP消息包
4. **UDP路由发送**：通过NATS发送到UDP Server，路由到指定IP基站
5. **基站响应处理**：接收基站响应，解析结果并返回给LLM
6. **配置确认**：LLM确认配置成功并提供详细信息

### 4.3. 多基站批量操作流程

1. **用户批量请求**：客户说"所有基站的功率都设置为45dB"
2. **LLM智能处理**：先查询基站列表，然后逐个配置
3. **并发操作管理**：MCP Server管理多个并发请求的correlation_id
4. **结果汇总**：收集所有基站的配置结果，统计成功/失败数量
5. **批量结果报告**：LLM提供详细的批量操作报告

## 5. 关键技术

*   **Node.js, NATS, LLM Function Calling API, `udp_server.js`** 保持不变。
*   **不再需要 Markdown 解析库**。
*   **新增基站状态监控**：实时解析心跳报文，提取基站状态信息。
*   **虚拟工具系统**：支持不发送UDP消息的工具，直接从缓存返回数据。
*   **多基站IP路由**：基于IP地址的消息路由和响应匹配机制。

## 6. 对 `22.md` 的要求

*   `22.md` 作为项目**最权威的协议说明文档**，是开发和维护的“单一事实来源”。
*   当需要修改或添加基站接口时，**首先更新 `22.md` 文档**，然后**同步修改 MCP Server 代码中的“协议接口注册表”**。

## 7. 挑战与考量

*   **手动维护成本**：当协议频繁变更时，需要人工同步更新 `22.md` 文档和代码中的注册表，需要有良好的开发流程来保证一致性。
*   **类型转换的精确性**：将 JSON 类型参数精确地打包成二进制 `Buffer`，以及反向解析，是核心的开发工作，需要细致和充分的测试。
*   **多基站请求-响应匹配**：在多基站UDP环境中，确保请求和响应的精确匹配，需要同时验证响应ID和源IP地址。
*   **状态数据一致性**：基站状态缓存需要与实际基站状态保持同步，需要处理心跳丢失、网络中断等异常情况。
*   **并发操作管理**：多基站批量操作时，需要管理多个并发请求的生命周期和超时处理。
*   **错误处理**：需要设计健壮的错误处理和日志记录机制，以便在通信、协议解析或参数校验失败时，能够快速定位问题。

## 8. 总结

这个优化后的设计方案，通过用**人工维护的、精确的协议注册表**替换**自动化但可能脆弱的Markdown解析**，在保证核心功能（LLM通过自然语言控制基站）不变的前提下，极大地提升了系统的**稳定性、可靠性和可维护性**。它清晰地划分了“协议定义”（文档和注册表）和“协议执行”（MCP Server）的界限，是一个非常务实且强大的工程实践。
### 8.1. 核心创新点

*   **基站状态监控**：实现了从UDP心跳报文实时解析基站状态信息的能力，为LLM提供了完整的基站可见性。
*   **虚拟工具系统**：创新性地引入了虚拟工具概念，扩展了MCP功能而无需UDP通信开销。
*   **多基站架构**：完全支持多基站环境，通过IP路由和双重匹配机制确保操作的准确性。
*   **自然语言交互**：实现了完整的自然语言到基站操作的转换，客户可以用日常语言管理基站设备。

### 8.2. 系统优势

*   **实时监控**：基于心跳报文的实时基站状态更新，提供最新的设备信息。
*   **智能配置**：LLM可以基于基站状态进行智能决策和配置优化。
*   **高可靠性**：多重验证机制确保多基站环境下的操作准确性。
*   **易于扩展**：虚拟工具模式为未来功能扩展提供了灵活的架构基础。
它清晰地划分了"协议定义"（文档和注册表）、"状态监控"（实时数据）和"协议执行"（MCP Server）的界限，是一个非常务实且强大的工程实践。

### 8.3. 实现成果

*   **完整的自然语言交互**：客户可以用自然语言查询基站状态和配置设备参数。
*   **实时状态监控**：系统能够实时解析和缓存基站状态信息。
*   **多基站支持**：完全支持多基站环境，确保操作的准确性和可靠性。
*   **智能配置能力**：基于基站状态进行智能决策和配置优化。