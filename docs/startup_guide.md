# MCP Server 启动指南

本文档介绍如何启动和管理MCP Server系统的各个服务组件。

## 系统架构

MCP Server系统包含三个主要服务：

1. **主服务 (index.js)** - UDP通信和Web界面，默认端口3000
2. **认证服务 (auth_server/app.js)** - 设备认证管理，默认端口3001  
3. **MCP服务 (mcp_server/app.js)** - LLM工具调用处理，通过NATS通信

## 启动方式

### 方式1：使用统一启动脚本 (推荐)

#### Linux/macOS
```bash
# 给脚本添加执行权限
chmod +x start_all.sh

# 启动所有服务
./start_all.sh

# 仅检查环境
./start_all.sh --check

# 显示帮助信息
./start_all.sh --help
```

#### Windows
```cmd
# 双击运行或在命令行中执行
start_all.bat
```

### 方式2：使用npm脚本

```bash
# 启动所有服务 (推荐)
npm run start:all

# 单独启动各个服务
npm run start:main    # 主服务
npm run start:auth    # 认证服务  
npm run start:mcp     # MCP服务

# 运行测试
npm run test:status        # 基站状态测试
npm run test:comprehensive # 综合功能测试
npm run test:natural       # 自然语言交互测试
```

### 方式3：手动启动

```bash
# 终端1 - 启动主服务
node index.js

# 终端2 - 启动认证服务
node auth_server/app.js

# 终端3 - 启动MCP服务
node mcp_server/app.js
```

## 环境要求

### 必需依赖
- Node.js (推荐v18+)
- npm 或 yarn

### 自动安装的依赖
启动脚本会自动检查并安装以下依赖：
- 主项目依赖 (`npm install`)
- 认证服务依赖 (`cd auth_server && npm install`)

### 可选工具
- `concurrently` - 用于并发启动多个服务
- `lsof` (Linux/macOS) - 用于端口占用检查

## 端口配置

| 服务 | 默认端口 | 说明 |
|------|----------|------|
| 主服务 | 3000 | Web界面和HTTP API |
| 认证服务 | 3001 | 设备认证管理界面 |
| NATS | 4222 | 消息队列服务 |
| UDP服务 | 8080 | 基站通信端口 |

## 服务状态检查

### 检查服务是否运行
```bash
# 检查端口占用
lsof -i :3000  # 主服务
lsof -i :3001  # 认证服务
lsof -i :4222  # NATS

# 或使用netstat
netstat -tlnp | grep :3000
```

### 访问服务
- 主服务Web界面: http://localhost:3000
- 认证服务管理界面: http://localhost:3001

## 日志和调试

### 查看服务日志
各服务的日志会输出到控制台，使用统一启动脚本时会有颜色区分：
- 蓝色 `[MAIN]` - 主服务日志
- 紫色 `[AUTH]` - 认证服务日志  
- 黄色 `[MCP]` - MCP服务日志

### 调试模式
```bash
# 设置调试环境变量
DEBUG=* npm run start:all

# 或单独设置
DEBUG=mcp:* node mcp_server/app.js
```

## 停止服务

### 使用统一启动脚本
按 `Ctrl+C` 停止所有服务

### 手动停止
```bash
# 查找进程ID
ps aux | grep node

# 停止特定进程
kill <PID>

# 强制停止所有node进程 (谨慎使用)
pkill -f node
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查找占用端口的进程
   lsof -i :3000
   # 停止占用进程
   kill -9 <PID>
   ```

2. **依赖安装失败**
   ```bash
   # 清理缓存重新安装
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **NATS连接失败**
   - 确保没有其他NATS服务运行在4222端口
   - 检查防火墙设置

4. **权限问题 (Linux/macOS)**
   ```bash
   # 给脚本添加执行权限
   chmod +x start_all.sh
   ```

### 检查系统状态
```bash
# 使用启动脚本的检查功能
./start_all.sh --check

# 或手动检查
node --version
npm --version
```

## 开发模式

### 热重载开发
```bash
# 安装nodemon (可选)
npm install -g nodemon

# 使用nodemon启动服务
nodemon index.js
nodemon auth_server/app.js  
nodemon mcp_server/app.js
```

### 测试环境
```bash
# 运行端到端测试
npm run test:e2e

# 运行特定测试
npm run test:status
npm run test:comprehensive
npm run test:natural
```

## 生产部署

### 使用PM2 (推荐)
```bash
# 安装PM2
npm install -g pm2

# 启动服务
pm2 start index.js --name "main-service"
pm2 start auth_server/app.js --name "auth-service"
pm2 start mcp_server/app.js --name "mcp-service"

# 查看状态
pm2 status

# 查看日志
pm2 logs

# 停止服务
pm2 stop all
```

### 使用Docker (可选)
```bash
# 构建镜像
docker build -t mcp-server .

# 运行容器
docker run -p 3000:3000 -p 3001:3001 mcp-server
```

## 更多信息

- [MCP Server设计文档](mcp_server_design.md)
- [开发计划](mcp_server_development_plan.md)
- [基站状态监控说明](README_BASE_STATION_STATUS.md)
- [LLM使用指南](LLM_USAGE_GUIDE.md)
