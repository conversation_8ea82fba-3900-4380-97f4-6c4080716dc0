# LLM自然语言交互使用指南

## 🎯 概述

现在系统完全支持通过自然语言与LLM交互来管理基站设备。LLM可以理解客户的自然语言请求，自动调用相应的MCP工具，并用自然语言回答结果。

## 🗣️ 支持的自然语言交互

### 1. 基站状态查询

#### 客户可以这样问：
- "现在系统接入了几个基站板？"
- "显示所有基站的详细信息"
- "查看基站*************的状态"
- "有哪些基站在线？"
- "基站的频点和PCI是多少？"

#### LLM处理逻辑：
```javascript
// LLM识别为基站查询请求，调用工具
{
  tool_name: 'query_base_station_status',
  tool_args: {
    target_ip: '' // 空字符串查询所有，或指定IP
  },
  correlation_id: 'uuid',
  target_ip: 'virtual'
}
```

#### LLM回答示例：
```
根据系统监控，当前接入了 3 个基站板：

🏢 基站 1 (*************)
   - 状态: 小区激活态
   - 频点: 上行18000, 下行1800
   - PCI: 123, 带宽: 20M
   - 数据更新: 5秒前

🏢 基站 2 (*************)
   - 状态: 扫频/同步进行中
   - 频点: 上行18100, 下行1810
   - PCI: 124, 带宽: 20M
   - 数据更新: 3秒前

🏢 基站 3 (*************)
   - 状态: 小区IDLE态
   - 频点: 上行18200, 下行1820
   - PCI: 125, 带宽: 10M
   - 数据更新: 7秒前

📊 总计: 3 个基站板在线运行
```

### 2. 功率配置

#### 客户可以这样说：
- "设置IP为*************的基站功率为50dB"
- "把所有基站的发射功率调到45dB"
- "将基站*************的功率降低到30dB"
- "所有基站功率统一设置为40dB"

#### LLM处理逻辑：
```javascript
// 1. 解析功率值：50dB
// 2. 计算衰减参数：50 / 0.25 = 200
// 3. 调用功率配置工具
{
  tool_name: 'configure_tx_power_attenuation',
  tool_args: {
    Pwr1Derease: 200, // 衰减参数
    IsSave: 1,        // 保存配置
    Res: [0, 0, 0]
  },
  correlation_id: 'uuid',
  target_ip: '*************' // 目标基站IP
}
```

#### LLM回答示例：
```
✅ 基站 ************* 的发射功率已成功配置为 50dB

📋 配置详情:
   - 衰减参数: 200
   - 实际衰减: 50.0dB
   - 配置已保存: 是
   - 重启后保留: 是
```

### 3. 接收增益配置

#### 客户可以这样说：
- "设置基站*************的接收增益为60dB"
- "调整所有基站的RX增益到55dB"
- "基站*************的接收增益改为70dB"

#### LLM处理逻辑：
```javascript
{
  tool_name: 'configure_rx_gain',
  tool_args: {
    Rxgain: 60,         // 接收增益值
    RxGainSaveFlag: 1,  // 保存配置
    RxOrSnfFlag: 0,     // 使用RX口
    Res: [0, 0]
  },
  correlation_id: 'uuid',
  target_ip: '*************'
}
```

### 4. 批量操作

#### 客户可以这样说：
- "把所有基站的功率都设置为45dB"
- "给每个基站配置不同的功率：*************设为50dB，*************设为40dB"
- "所有在线基站的接收增益统一调到65dB"

#### LLM处理逻辑：
```javascript
// 1. 先查询所有基站
// 2. 遍历每个基站进行配置
// 3. 汇总配置结果
```

## 🔧 LLM工具映射表

| 自然语言意图 | MCP工具 | 参数说明 |
|-------------|---------|----------|
| 查询基站状态 | `query_base_station_status` | `target_ip`: 空=所有，IP=特定基站 |
| 设置发射功率 | `configure_tx_power_attenuation` | `Pwr1Derease`: 功率dB/0.25 |
| 设置接收增益 | `configure_rx_gain` | `Rxgain`: 增益dB值 |
| 查询基站信息 | `query_base_info` | 获取基站详细配置信息 |
| 重启基站 | `configure_reboot` | 重启指定基站 |

## 🧠 LLM智能处理能力

### 1. 参数自动转换
- **功率单位转换**: "50dB" → 衰减参数200 (50/0.25)
- **IP地址识别**: 自动提取IP地址作为target_ip
- **批量操作**: 自动查询基站列表并逐个配置

### 2. 错误处理和建议
```javascript
// 当配置失败时，LLM会给出建议
if (configResult.error) {
  return `❌ 配置失败: ${configResult.error}
💡 建议: 
  - 检查基站连接状态
  - 确认IP地址正确
  - 验证网络通信正常`;
}
```

### 3. 状态分析
```javascript
// LLM可以分析基站状态并给出建议
switch (cellState) {
  case 0: return "基站处于IDLE态，建议检查配置";
  case 1: return "基站正在同步，请稍等片刻";
  case 3: return "基站运行正常，可以进行配置";
}
```

## 🎭 交互示例

### 完整对话流程：

**客户**: "请问现在有几个基站在线？"

**LLM**: "我来为您查询当前系统中的基站信息..."
*[调用 query_base_station_status 工具]*

**LLM**: "根据系统监控，当前接入了 3 个基站板：
- 基站1 (*************): 小区激活态
- 基站2 (*************): 扫频/同步进行中  
- 基站3 (*************): 小区IDLE态"

**客户**: "把*************的功率设置为50dB"

**LLM**: "我来为您配置基站*************的发射功率..."
*[调用 configure_tx_power_attenuation 工具]*

**LLM**: "✅ 基站*************的发射功率已成功配置为50dB，配置已保存。"

**客户**: "所有基站的功率都调到45dB"

**LLM**: "我来为您配置所有基站的发射功率..."
*[先查询基站列表，然后批量配置]*

**LLM**: "📊 批量功率配置完成！
✅ 成功配置: 3 个基站
🎯 目标功率: 45dB
💾 配置已保存到基站"

## 🚀 测试方法

运行自然语言交互演示：
```bash
node test_natural_language_demo.js
```

这个脚本会模拟完整的客户-LLM交互流程，展示：
1. 基站状态查询的自然语言交互
2. 单个基站功率配置的自然语言交互  
3. 批量基站功率配置的自然语言交互

## ✅ 系统能力确认

**是的！** 现在系统完全支持：

1. ✅ **自然语言查询**: 客户用自然语言询问基站数量和状态
2. ✅ **智能回答**: LLM用自然语言回答所有基站的详细信息
3. ✅ **自然语言配置**: 客户用自然语言设置功率参数
4. ✅ **自动工具调用**: LLM自动调用MCP工具实现配置
5. ✅ **批量操作**: 支持"所有基站"的批量配置
6. ✅ **智能参数转换**: 自动将dB值转换为系统参数
7. ✅ **多基站支持**: 完全支持多基站环境
8. ✅ **实时状态**: 基于心跳报文的实时基站状态

客户现在可以完全通过自然语言与系统交互，无需了解技术细节！
