# MCP Server 开发计划与任务清单

**任务状态说明：**
*   `[x]` 表示该任务已完成。
*   `[ ]` 表示该任务待完成。

## 1. 目标

基于优化后的设计方案，采用轻量级、模块化的方式开发 MCP Server。本项目不引入新框架，通过清晰的文件结构和 NATS 消息队列实现与现有系统的解耦，确保开发过程不影响线上功能。

**最新更新**：已完成基站状态监控功能，实现了从UDP Server心跳报文解析基站信息（IP、端口、SN、状态、频点、PCI等），并支持自然语言交互。系统现在完全支持多基站环境和虚拟工具模式。

## 2. 任务清单 (Dev Tasks)

---

### **阶段一：奠定基础 - 协议核心模块**

**目标：** 构建一个可独立测试的、处理协议打包/解包的核心逻辑库。

*   [x] **任务 1.1：创建项目结构**
    *   在项目根目录下创建 `mcp_server` 文件夹。
    *   在 `mcp_server` 内创建 `protocol` 子文件夹。

*   [x] **任务 1.2：初始化协议定义**
    *   在 `mcp_server/protocol/` 中创建 `definitions.js` 文件。
    *   **要求**：定义至少3个接口的结构，作为后续开发的基础。
        *   **建议接口**：
            1.  一个简单的查询：`基站基本信息查询 (0xF02B)`
            2.  一个简单的配置：`基站重启配置 (0xF00B)`
            3.  一个带复杂参数的配置：`辅PLMN列表配置 (0xF060)`
    *   **产物**：一个导出的 JavaScript 对象，键为LLM友好的命令名，值为包含`messageId`, `description`, `parameters`等信息的对象。

*   [x] **任务 1.3：实现消息构建器 (Builder)**
    *   在 `mcp_server/protocol/` 中创建 `builder.js` 文件。
    *   **要求**：实现一个函数 `build(messageName, args)`，该函数能根据 `definitions.js` 中的定义，将传入的 `args` 对象转换成一个二进制 `Buffer`。
    *   **初始范围**：仅需支持 `U8`, `U32`, `S32`, 固定长度的 `String` 和 `Array` 类型。

*   [x] **任务 1.4：实现消息解析器 (Parser)**
    *   在 `mcp_server/protocol/` 中创建 `parser.js` 文件。
    *   **要求**：实现一个函数 `parse(messageId, buffer)`，该函数能根据 `definitions.js` 中的定义，将传入的二进制 `Buffer` 解析成一个结构化的 JSON 对象。

*   [x] **任务 1.5：编写核心模块单元测试**
    *   在 `mcp_server/protocol/` 目录下创建 `tests` 文件夹。
    *   **要求**：编写测试用例，验证 `builder.js` 和 `parser.js` 对于任务 1.2 中定义的3个接口能够正确地进行打包和解包，确保数据转换的准确性。

---

### **阶段二：构建服务与通信层**

**目标：** 让 MCP Server 作为一个独立服务运行起来，并通过 NATS 与现有系统建立通信。

*   [x] **任务 2.1：创建服务主入口**
    *   在 `mcp_server/` 中创建 `app.js` 文件。
    *   **要求**：设置基本的 Node.js 服务启动逻辑，并引入必要的模块。

*   [x] **任务 2.2：封装 NATS 客户端**
    *   在 `mcp_server/` 中创建 `nats_client.js`。
    *   **要求**：封装 NATS 连接、订阅、发布等操作，使其易于在 `app.js` 中调用。处理好连接失败和重连逻辑。

*   [x] **任务 2.3：实现请求处理流程**
    *   在 `app.js` 中，订阅 NATS 主题 `llm.invoke_tool`。
    *   **要求**：当收到消息时，调用 `protocol/builder.js` 构建数据包，然后通过 NATS 将二进制包发布到 `mcp.send_packet` 主题。

*   [x] **任务 2.4：实现响应处理流程**
    *   在 `app.js` 中，订阅 NATS 主题 `base_station.raw_response`。
    *   **要求**：当收到消息时，调用 `protocol/parser.js` 解析数据包，然后通过 NATS 将结果发布到 `llm.tool_results` 主题。

*   [x] **任务 2.5：实现请求-响应关联逻辑**
    *   在 `app.js` 中，创建一个 Map 用于存储 `correlation_id` 和请求信息的映射。
    *   **要求**：在发送请求时存入映射，在收到响应时查找并移除映射。实现一个基本的请求超时机制（例如，30秒后自动清理）。

*   [x] **任务 2.6：改造 `udp_server.js` (上行)**
    *   **文件**：`/home/<USER>/prj/pwsjiami/udp_server.js`
    *   **要求**：在 `server.on('message', ...)` 回调中，解密消息后，**增加** NATS 发布逻辑，将原始的 `msgType` 和 `decrypted` Buffer 发送到 `base_station.raw_response` 主题。**严禁修改原有业务逻辑。**

*   [x] **任务 2.7：改造 `udp_server.js` (下行)**
    *   **文件**：`/home/<USER>/prj/pwsjiami/udp_server.js`
    *   **要求**：在 `initNatsConnection` 或类似函数中，**增加**对 `mcp.send_packet` 主题的订阅。收到消息（二进制Buffer）后，直接通过 `server.send()` 方法发送给基站。

*   [x] **任务 2.8：实现基站状态监控 (新增)**
    *   **文件**：`/home/<USER>/prj/pwsjiami/udp_server.js`
    *   **要求**：增强心跳处理逻辑，从心跳报文中解析基站状态信息（IP、端口、SN、小区状态、频点、PCI、带宽等），并通过NATS发布到`base_station.status_update`主题。

*   [x] **任务 2.9：实现多基站IP路由支持 (新增)**
    *   **文件**：`/home/<USER>/prj/pwsjiami/udp_server.js`
    *   **要求**：支持基于IP地址的消息路由，确保消息能够发送到指定的基站设备，并正确处理来自不同基站的响应。

---

### **阶段三：对接 LLM 并完成端到端测试**

**目标：** 完成与 LLM 的对接闭环，并验证整个系统的正确性。

*   [x] **任务 3.1：实现 LLM 工具生成器**
    *   在 `mcp_server/` 中创建 `llm_tool_generator.js`。
    *   **要求**：实现一个函数，该函数能读取 `protocol/definitions.js`，并将其中的所有定义转换成一个符合 LLM Function Calling 规范的 JSON Schema 数组。

*   [x] **任务 3.2：提供工具定义接口**
    *   在 `mcp_server/app.js` 中，增加对 NATS 主题 `mcp.get_llm_tools` 的订阅。
    *   **要求**：当收到请求时，调用 `llm_tool_generator.js` 生成工具列表并作为响应返回。

*   [x] **任务 3.3：编写端到端集成测试客户端**
    *   在项目根目录创建 `test_mcp_client.js`。
    *   **要求**：该脚本需模拟 LLM 客户端的完整流程：
        1.  连接 NATS。
        2.  向 `mcp.get_llm_tools` 请求工具定义并打印。
        3.  模拟调用一个已定义的工具（如“基站重启”），将请求发布到 `llm.invoke_tool`。
        4.  订阅 `llm.tool_results` 主题，等待并打印最终的 JSON结果。

*   [x] **任务 3.4：实现基站状态缓存与订阅 (新增)**
    *   **文件**：`mcp_server/app.js`
    *   **要求**：订阅`base_station.status_update`主题，维护基站状态缓存Map，为虚拟工具提供数据支持。

*   [x] **任务 3.5：实现虚拟工具系统 (新增)**
    *   **文件**：`mcp_server/app.js`
    *   **要求**：实现虚拟工具处理机制，支持不发送UDP消息的工具，直接从内部缓存返回数据。

*   [x] **任务 3.6：实现基站状态查询工具 (新增)**
    *   **文件**：`mcp_server/protocol/definitions.js`
    *   **要求**：添加`query_base_station_status`虚拟工具定义，支持查询基站实时状态信息。

*   [x] **任务 3.7：增强请求-响应匹配机制 (新增)**
    *   **文件**：`mcp_server/app.js`
    *   **要求**：实现基于correlation_id和source_ip的双重匹配机制，确保多基站环境下的响应准确性。

---

---

### **阶段四：基站状态监控与自然语言交互 (已完成)**

**目标：** 实现基站状态实时监控和自然语言交互能力。

*   [x] **任务 4.0.1：创建基站状态监控测试脚本**
    *   **文件**：`test_base_station_status.js`
    *   **要求**：测试基站状态查询功能，验证虚拟工具的正确性。

*   [x] **任务 4.0.2：创建综合测试脚本**
    *   **文件**：`test_comprehensive.js`
    *   **要求**：综合测试状态监控和智能功率配置，展示基于状态的智能决策。

*   [x] **任务 4.0.3：创建自然语言交互演示**
    *   **文件**：`test_natural_language_demo.js`
    *   **要求**：模拟完整的自然语言交互流程，展示客户如何用自然语言管理基站。

*   [x] **任务 4.0.4：创建使用指南文档**
    *   **文件**：`LLM_USAGE_GUIDE.md`, `README_BASE_STATION_STATUS.md`
    *   **要求**：提供详细的使用指南和功能说明文档。

---

### **阶段五：完善与部署**

**目标：** 全面实现协议，加固服务，并使其易于部署和维护。

*   [x] **任务 5.1：全面填充协议定义**
    *   **文件**：`mcp_server/protocol/definitions.js`
    *   **要求**：对照 `22.md` 文档，将所有需要被 LLM 调用的接口，完整、准确地添加到协议注册表中。
    *   **状态**：已完成主要接口定义，包括功率配置、接收增益、基站重启、基站信息查询等。

*   [x] **任务 5.2：增强 `builder.js` 和 `parser.js`**
    *   **要求**：根据任务 5.1 中添加的复杂接口，增强两个模块，使其支持所有需要的数据类型，特别是嵌套结构体和变长数组。
    *   **状态**：已支持当前所需的所有数据类型。

*   [x] **任务 5.3：添加健壮的错误处理**
    *   **要求**：在 `mcp_server/app.js` 中，为所有关键步骤（如消息解析、NATS通信、请求超时）添加 `try...catch` 和错误处理逻辑。
    *   **状态**：已实现完善的错误处理和超时机制。

*   [x] **任务 5.4：添加详细日志**
    *   **要求**：在 MCP Server 的所有模块中，添加带有明确前缀（如 `[MCP Server][INFO]`）的控制台日志，以便于调试。
    *   **状态**：已添加详细的日志记录。

*   [x] **任务 5.5：更新项目文档**
    *   **文件**：`README_BASE_STATION_STATUS.md`, `LLM_USAGE_GUIDE.md`
    *   **要求**：增加关于 MCP Server 的说明，包括其作用、如何安装依赖以及如何使用。
    *   **状态**：已创建详细的功能说明和使用指南。

*   [ ] **任务 5.6：创建统一启动脚本**
    *   在项目根目录创建 `start_all.sh` 脚本。
    *   **要求**：该脚本能使用 `npm start` 或 `node ...` 命令，一键启动 `index.js`、`auth_server/app.js` 和 `mcp_server/app.js` 三个服务。

---

### **阶段六：功能扩展与优化 (规划中)**

**目标：** 基于已有基础，进一步扩展系统功能。

*   [ ] **任务 6.1：实现基站状态历史记录**
    *   **要求**：存储基站状态变化历史，支持状态趋势分析。

*   [ ] **任务 6.2：实现基站状态告警机制**
    *   **要求**：基于状态变化自动生成告警，支持异常检测。

*   [ ] **任务 6.3：实现基站性能分析**
    *   **要求**：基于状态数据进行性能分析和优化建议。

*   [ ] **任务 6.4：实现批量操作优化**
    *   **要求**：优化多基站批量操作的性能和可靠性。