// test_multi_device.js - 测试多基站环境下的功率配置功能
const { connect, StringCodec } = require('nats');
const { v4: uuidv4 } = require('uuid');

const sc = StringCodec();

// 模拟多个基站的配置
const BASE_STATIONS = [
  { ip: '*************', name: '基站A' },
  { ip: '*************', name: '基站B' },
  { ip: '*************', name: '基站C' }
];

async function testMultiDeviceConfiguration() {
  let natsConnection;
  try {
    console.log('[Multi-Device Test] 连接到NATS服务器...');
    natsConnection = await connect({ servers: "nats://************:4222" });
    console.log('[Multi-Device Test] NATS连接成功。');

    // 1. 获取可用的工具列表
    console.log('\n=== 步骤1: 获取MCP工具列表 ===');
    const toolsMsg = await natsConnection.request('mcp.get_llm_tools', '', { timeout: 5000 });
    const tools = JSON.parse(sc.decode(toolsMsg.data));
    
    console.log('[Multi-Device Test] 可用的工具:');
    tools.forEach(tool => {
      console.log(`  - ${tool.name}: ${tool.description}`);
    });

    // 2. 并行测试多个基站的功率配置
    console.log('\n=== 步骤2: 并行测试多基站功率配置 ===');
    await testParallelPowerConfiguration(natsConnection);

    // 3. 顺序测试每个基站的不同配置
    console.log('\n=== 步骤3: 顺序测试各基站不同配置 ===');
    await testSequentialConfiguration(natsConnection);

    console.log('\n[Multi-Device Test] 所有测试完成。');

  } catch (err) {
    console.error('[Multi-Device Test] 测试失败:', err);
  } finally {
    if (natsConnection) {
      await natsConnection.close();
      console.log('\n[Multi-Device Test] NATS连接已关闭。');
    }
  }
}

/**
 * 并行测试多个基站的功率配置
 */
async function testParallelPowerConfiguration(natsConnection) {
  console.log('[Multi-Device Test] 开始并行测试...');
  
  // 为每个基站创建不同的配置任务
  const tasks = BASE_STATIONS.map((station, index) => {
    return testSingleDevicePowerConfig(natsConnection, station, {
      Pwr1Derease: 20 + (index * 10), // 不同的功率衰减值
      IsSave: 1,
      Res: [0, 0, 0]
    });
  });

  // 并行执行所有任务
  const results = await Promise.allSettled(tasks);
  
  // 分析结果
  results.forEach((result, index) => {
    const station = BASE_STATIONS[index];
    if (result.status === 'fulfilled') {
      console.log(`✅ ${station.name} (${station.ip}) 配置成功`);
    } else {
      console.log(`❌ ${station.name} (${station.ip}) 配置失败: ${result.reason}`);
    }
  });
}

/**
 * 顺序测试每个基站的不同配置
 */
async function testSequentialConfiguration(natsConnection) {
  console.log('[Multi-Device Test] 开始顺序测试...');
  
  for (let i = 0; i < BASE_STATIONS.length; i++) {
    const station = BASE_STATIONS[i];
    
    console.log(`\n--- 测试 ${station.name} (${station.ip}) ---`);
    
    // 测试发射功率配置
    try {
      await testSingleDevicePowerConfig(natsConnection, station, {
        Pwr1Derease: 30 + (i * 5),
        IsSave: 1,
        Res: [0, 0, 0]
      });
      console.log(`✅ ${station.name} 发射功率配置成功`);
    } catch (error) {
      console.log(`❌ ${station.name} 发射功率配置失败: ${error.message}`);
    }
    
    // 等待一段时间再测试下一个设备
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 测试接收增益配置
    try {
      await testSingleDeviceRxGain(natsConnection, station, {
        Rxgain: 50 + (i * 10),
        RxGainSaveFlag: 1,
        RxOrSnfFlag: 0,
        Res: [0, 0]
      });
      console.log(`✅ ${station.name} 接收增益配置成功`);
    } catch (error) {
      console.log(`❌ ${station.name} 接收增益配置失败: ${error.message}`);
    }
    
    // 等待一段时间再测试下一个设备
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

/**
 * 测试单个设备的发射功率配置
 */
async function testSingleDevicePowerConfig(natsConnection, station, config) {
  const correlationId = uuidv4();
  
  const toolCallRequest = {
    tool_name: 'configure_tx_power_attenuation',
    tool_args: config,
    correlation_id: correlationId,
    target_ip: station.ip  // [关键] 指定目标基站IP
  };

  console.log(`[Multi-Device Test] 向 ${station.name} (${station.ip}) 发送发射功率配置:`);
  console.log(`  功率衰减值: ${config.Pwr1Derease} (${config.Pwr1Derease * 0.25}dB)`);
  console.log(`  关联ID: ${correlationId}`);

  // 订阅响应
  const sub = natsConnection.subscribe('llm.tool_results');
  
  // 发送请求
  natsConnection.publish('llm.invoke_tool', sc.encode(JSON.stringify(toolCallRequest)));
  
  // 等待响应
  const result = await waitForResponse(sub, correlationId, 10000);
  
  if (!result.success) {
    throw new Error(result.error);
  }
  
  // 验证响应来源
  if (result.response.source_ip !== station.ip) {
    throw new Error(`响应IP不匹配: 期望 ${station.ip}, 实际 ${result.response.source_ip}`);
  }
  
  return result.response;
}

/**
 * 测试单个设备的接收增益配置
 */
async function testSingleDeviceRxGain(natsConnection, station, config) {
  const correlationId = uuidv4();
  
  const toolCallRequest = {
    tool_name: 'configure_rx_gain',
    tool_args: config,
    correlation_id: correlationId,
    target_ip: station.ip  // [关键] 指定目标基站IP
  };

  console.log(`[Multi-Device Test] 向 ${station.name} (${station.ip}) 发送接收增益配置:`);
  console.log(`  接收增益: ${config.Rxgain}dB`);
  console.log(`  关联ID: ${correlationId}`);

  // 订阅响应
  const sub = natsConnection.subscribe('llm.tool_results');
  
  // 发送请求
  natsConnection.publish('llm.invoke_tool', sc.encode(JSON.stringify(toolCallRequest)));
  
  // 等待响应
  const result = await waitForResponse(sub, correlationId, 10000);
  
  if (!result.success) {
    throw new Error(result.error);
  }
  
  // 验证响应来源
  if (result.response.source_ip !== station.ip) {
    throw new Error(`响应IP不匹配: 期望 ${station.ip}, 实际 ${result.response.source_ip}`);
  }
  
  return result.response;
}

/**
 * 等待指定correlation_id的响应
 */
async function waitForResponse(subscription, correlationId, timeoutMs) {
  return new Promise((resolve) => {
    const timeout = setTimeout(() => {
      resolve({ success: false, error: '响应超时' });
    }, timeoutMs);

    (async () => {
      for await (const m of subscription) {
        try {
          const response = JSON.parse(sc.decode(m.data));
          if (response.correlation_id === correlationId) {
            clearTimeout(timeout);
            resolve({ success: true, response });
            break;
          }
        } catch (parseError) {
          console.error('[Multi-Device Test] 解析响应失败:', parseError);
        }
      }
    })();
  });
}

// 运行测试
if (require.main === module) {
  testMultiDeviceConfiguration();
}

module.exports = { testMultiDeviceConfiguration };
