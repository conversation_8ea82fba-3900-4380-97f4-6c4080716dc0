const { NodeSSH } = require('node-ssh');

class SshClient {
    constructor() {
        this.ssh = new NodeSSH();
        this.isConnected = false;
        // Default SSH credentials
        this.defaultConfig = {
            username: 'root',
            password: 'BlTf128'
        };
    }

    /**
     * Connects to the OpenWrt board via SSH.
     * @param {object} config - SSH connection configuration (host, username, password/privateKey).
     */
    async connect(config) {
        // Merge provided config with defaults
        const finalConfig = { ...this.defaultConfig, ...config };
        try {
            await this.ssh.connect(finalConfig);
            this.isConnected = true;
            console.log(' connected to board.');
            return true;
        } catch (error) {
            console.error('connection error:', error);
            this.isConnected = false;
            return false;
        }
    }

    /**
     * Executes a command on the OpenWrt board.
     * @param {string} command - The command to execute.
     * @returns {Promise<object>} - Object with stdout, stderr, and exitCode.
     */
    async execCommand(command) {
        if (!this.isConnected) {
            console.error(' not connected. Please connect first.');
            return { stdout: '', stderr: 'not connected.', exitCode: -1 };
        }
        try {
            const result = await this.ssh.execCommand(command);
            // console.log(`Command '${command}' executed.`);
            // console.log('STDOUT:', result.stdout);
            console.log('STDERR:', result.stderr);
            return result;
        } catch (error) {
            console.error(`Error executing command '${command}':`, error);
            return { stdout: '', stderr: error.message, exitCode: -1 };
        }
    }

    /**
     * Uploads a file to the OpenWrt board.
     * @param {string} localPath - Path to the local file.
     * @param {string} remotePath - Path where the file should be uploaded on the board.
     * @returns {Promise<boolean>} - True if successful, false otherwise.
     */
    async uploadFile(localPath, remotePath) {
        if (!this.isConnected) {
            console.error(' not connected. Please connect first.');
            return false;
        }
        try {
            await this.ssh.putFile(localPath, remotePath);
            console.log(`File '${localPath}' uploaded to '${remotePath}' on board.`);
            return true;
        } catch (error) {
            console.error(`Error uploading file '${localPath}':`, error);
            return false;
        }
    }

    /**
     * Changes file permissions on the OpenWrt board.
     * @param {string} remotePath - Path to the file on the board.
     * @param {string} permissions - Octal permissions (e.g., '0755').
     * @returns {Promise<object>} - Result from execCommand.
     */
    async changeFilePermissions(remotePath, permissions) {
        if (!this.isConnected) {
            console.error(' not connected. Please connect first.');
            return { stdout: '', stderr: ' not connected.', exitCode: -1 };
        }
        const command = `chmod ${permissions} ${remotePath}`;
        return this.execCommand(command);
    }

    /**
     * Disconnects the SSH session.
     */
    disconnect() {
        if (this.isConnected) {
            this.ssh.dispose();
            this.isConnected = false;
            console.log(' disconnected.');
        }
    }
}

module.exports = { SshClient }; 