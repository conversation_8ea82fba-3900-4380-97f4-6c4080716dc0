// test_natural_language_demo.js - 模拟自然语言交互演示
const { connect, StringCodec } = require('nats');
const { v4: uuidv4 } = require('uuid');

const sc = StringCodec();

async function simulateNaturalLanguageInteraction() {
  let natsConnection;
  try {
    console.log('🤖 [LLM模拟器] 连接到MCP系统...');
    natsConnection = await connect({ servers: "nats://113.45.77.38:4222" });
    console.log('✅ [LLM模拟器] 连接成功\n');

    // 模拟客户的自然语言查询
    await simulateUserQuery1(natsConnection);
    
    console.log('\n' + '='.repeat(80) + '\n');
    
    // 模拟客户的功率配置请求
    await simulateUserQuery2(natsConnection);
    
    console.log('\n' + '='.repeat(80) + '\n');
    
    // 模拟批量功率配置
    await simulateUserQuery3(natsConnection);

  } catch (err) {
    console.error('❌ [LLM模拟器] 测试失败:', err);
  } finally {
    if (natsConnection) {
      await natsConnection.close();
      console.log('\n🔌 [LLM模拟器] 连接已关闭');
    }
  }
}

/**
 * 模拟用户查询：现在系统接入了几个基站板？
 */
async function simulateUserQuery1(natsConnection) {
  console.log('👤 [客户]: "请问现在系统接入了几个基站板？显示所有基站的详细信息"');
  console.log('🤖 [LLM]: 我来为您查询当前系统中的基站信息...\n');

  // LLM调用基站状态查询工具
  const correlationId = uuidv4();
  const toolCallRequest = {
    tool_name: 'query_base_station_status',
    tool_args: {
      target_ip: '' // 查询所有基站
    },
    correlation_id: correlationId,
    target_ip: 'virtual'
  };

  console.log('🔧 [LLM内部]: 调用 query_base_station_status 工具...');
  
  const sub = natsConnection.subscribe('llm.tool_results');
  natsConnection.publish('llm.invoke_tool', sc.encode(JSON.stringify(toolCallRequest)));
  
  const result = await waitForResponse(sub, correlationId, 5000);
  
  if (result.success && result.response.tool_output.success) {
    const stations = result.response.tool_output.data;
    
    // 模拟LLM的自然语言回答
    console.log('🤖 [LLM回答]:');
    console.log(`根据系统监控，当前接入了 ${stations.length} 个基站板：\n`);
    
    stations.forEach((station, index) => {
      console.log(`🏢 基站 ${index + 1} (${station.基站信息.IP地址})`);
      console.log(`   - 端口: ${station.基站信息.端口}`);
      console.log(`   - SN号: ${station.基站信息.SN号}`);
      console.log(`   - 状态: ${station.小区状态.状态描述}`);
      console.log(`   - 频点: 上行${station.频率信息.上行频点}, 下行${station.频率信息.下行频点}`);
      console.log(`   - PCI: ${station.小区状态.物理小区ID}, 带宽: ${station.频率信息.带宽描述}`);
      console.log(`   - PLMN: ${station.小区状态.PLMN}`);
      console.log(`   - 数据更新: ${station.技术参数.数据新鲜度}\n`);
    });
    
    console.log(`📊 总计: ${stations.length} 个基站板在线运行`);
    
  } else {
    console.log('🤖 [LLM回答]: 抱歉，当前没有检测到任何基站板连接。请确保基站设备已正确连接并发送心跳报文。');
  }
}

/**
 * 模拟用户请求：设置某个IP的基站功率
 */
async function simulateUserQuery2(natsConnection) {
  console.log('👤 [客户]: "设置IP为*************的基站发射功率为50dB"');
  console.log('🤖 [LLM]: 我来为您配置基站*************的发射功率...\n');

  // LLM解析功率参数：50dB对应衰减参数200 (50dB / 0.25dB = 200)
  const targetPower = 50; // dB
  const attenuationParam = Math.round(targetPower / 0.25); // 转换为衰减参数
  
  console.log(`🧠 [LLM分析]: 目标功率${targetPower}dB，计算衰减参数为${attenuationParam}`);

  const correlationId = uuidv4();
  const toolCallRequest = {
    tool_name: 'configure_tx_power_attenuation',
    tool_args: {
      Pwr1Derease: attenuationParam,
      IsSave: 1, // 保存配置
      Res: [0, 0, 0]
    },
    correlation_id: correlationId,
    target_ip: '*************'
  };

  console.log('🔧 [LLM内部]: 调用 configure_tx_power_attenuation 工具...');
  
  const sub = natsConnection.subscribe('llm.tool_results');
  natsConnection.publish('llm.invoke_tool', sc.encode(JSON.stringify(toolCallRequest)));
  
  const result = await waitForResponse(sub, correlationId, 8000);
  
  if (result.success) {
    console.log('🤖 [LLM回答]:');
    console.log(`✅ 基站 ************* 的发射功率已成功配置为 ${targetPower}dB`);
    console.log(`📋 配置详情:`);
    console.log(`   - 衰减参数: ${attenuationParam}`);
    console.log(`   - 实际衰减: ${attenuationParam * 0.25}dB`);
    console.log(`   - 配置已保存: 是`);
    console.log(`   - 重启后保留: 是`);
  } else {
    console.log('🤖 [LLM回答]:');
    console.log(`❌ 配置基站 ************* 功率失败: ${result.error}`);
    console.log(`💡 建议: 请检查基站连接状态和网络通信`);
  }
}

/**
 * 模拟用户请求：设置所有基站功率
 */
async function simulateUserQuery3(natsConnection) {
  console.log('👤 [客户]: "设置所有基站的发射功率为45dB"');
  console.log('🤖 [LLM]: 我来为您配置所有基站的发射功率...\n');

  // 1. 先查询所有基站
  console.log('🔍 [LLM步骤1]: 查询当前所有基站...');
  const queryId = uuidv4();
  const queryRequest = {
    tool_name: 'query_base_station_status',
    tool_args: { target_ip: '' },
    correlation_id: queryId,
    target_ip: 'virtual'
  };

  let sub = natsConnection.subscribe('llm.tool_results');
  natsConnection.publish('llm.invoke_tool', sc.encode(JSON.stringify(queryRequest)));
  
  const queryResult = await waitForResponse(sub, queryId, 5000);
  
  if (!queryResult.success || !queryResult.response.tool_output.success) {
    console.log('🤖 [LLM回答]: ❌ 无法获取基站列表，配置失败');
    return;
  }

  const stations = queryResult.response.tool_output.data;
  console.log(`📡 [LLM步骤1]: 发现 ${stations.length} 个基站需要配置\n`);

  // 2. 批量配置每个基站
  const targetPower = 45; // dB
  const attenuationParam = Math.round(targetPower / 0.25);
  
  console.log(`🧠 [LLM分析]: 目标功率${targetPower}dB，衰减参数${attenuationParam}`);
  console.log('🔧 [LLM步骤2]: 开始批量配置...\n');

  let successCount = 0;
  let failCount = 0;

  for (let i = 0; i < stations.length; i++) {
    const station = stations[i];
    const ip = station.基站信息.IP地址;
    
    console.log(`⚙️  配置基站 ${i + 1}/${stations.length}: ${ip}`);

    const configId = uuidv4();
    const configRequest = {
      tool_name: 'configure_tx_power_attenuation',
      tool_args: {
        Pwr1Derease: attenuationParam,
        IsSave: 1,
        Res: [0, 0, 0]
      },
      correlation_id: configId,
      target_ip: ip
    };

    sub = natsConnection.subscribe('llm.tool_results');
    natsConnection.publish('llm.invoke_tool', sc.encode(JSON.stringify(configRequest)));
    
    const configResult = await waitForResponse(sub, configId, 8000);
    
    if (configResult.success) {
      console.log(`   ✅ ${ip} 配置成功`);
      successCount++;
    } else {
      console.log(`   ❌ ${ip} 配置失败: ${configResult.error}`);
      failCount++;
    }

    // 基站间配置间隔
    if (i < stations.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  // 3. 总结配置结果
  console.log('\n🤖 [LLM回答]:');
  console.log(`📊 批量功率配置完成！`);
  console.log(`✅ 成功配置: ${successCount} 个基站`);
  console.log(`❌ 配置失败: ${failCount} 个基站`);
  console.log(`🎯 目标功率: ${targetPower}dB`);
  console.log(`💾 配置已保存到基站，重启后保留`);
  
  if (failCount > 0) {
    console.log(`💡 建议: 请检查失败基站的连接状态`);
  }
}

/**
 * 等待响应
 */
async function waitForResponse(subscription, correlationId, timeoutMs) {
  return new Promise((resolve) => {
    const timeout = setTimeout(() => {
      resolve({ success: false, error: '响应超时' });
    }, timeoutMs);

    (async () => {
      for await (const m of subscription) {
        try {
          const response = JSON.parse(sc.decode(m.data));
          if (response.correlation_id === correlationId) {
            clearTimeout(timeout);
            resolve({ success: true, response });
            break;
          }
        } catch (parseError) {
          console.error('[LLM模拟器] 解析响应失败:', parseError);
        }
      }
    })();
  });
}

// 运行演示
if (require.main === module) {
  console.log('🎭 自然语言交互演示');
  console.log('=' .repeat(80));
  simulateNaturalLanguageInteraction();
}

module.exports = { simulateNaturalLanguageInteraction };
