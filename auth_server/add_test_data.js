const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 连接数据库
const db = new sqlite3.Database(path.join(__dirname, 'auth.db'), (err) => {
    if (err) {
        console.error('Error opening database:', err.message);
        return;
    }
    console.log('Connected to the SQLite database.');
});

// 添加测试数据
function addTestData() {
    const testData = [
        // 未授权记录
        {
            sn: 'TEST_SN_001',
            authSerial: 'auth_serial_001',
            auth_code: null,
            auth_count: 0,
            last_au_time: new Date().toISOString(),
            status: 'pending',
            first_auth_time: new Date().toISOString(),
            daily_request_count: 1,
            last_daily_reset_date: new Date().toISOString().split('T')[0]
        },
        {
            sn: 'TEST_SN_002',
            authSerial: 'auth_serial_002',
            auth_code: null,
            auth_count: 0,
            last_au_time: new Date().toISOString(),
            status: 'pending',
            first_auth_time: new Date().toISOString(),
            daily_request_count: 2,
            last_daily_reset_date: new Date().toISOString().split('T')[0]
        },
        {
            sn: 'TEST_SN_003',
            authSerial: 'auth_serial_003',
            auth_code: null,
            auth_count: 0,
            last_au_time: new Date().toISOString(),
            status: 'pending',
            first_auth_time: new Date().toISOString(),
            daily_request_count: 1,
            last_daily_reset_date: new Date().toISOString().split('T')[0]
        },
        // 已授权记录
        {
            sn: 'TEST_SN_004',
            authSerial: 'auth_serial_004',
            auth_code: 'AUTH_CODE_004',
            auth_count: 5,
            last_au_time: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1天前
            status: 'authorized',
            first_auth_time: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7天前
            daily_request_count: 3,
            last_daily_reset_date: new Date().toISOString().split('T')[0]
        },
        {
            sn: 'TEST_SN_005',
            authSerial: 'auth_serial_005',
            auth_code: 'AUTH_CODE_005',
            auth_count: 10,
            last_au_time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2小时前
            status: 'authorized',
            first_auth_time: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3天前
            daily_request_count: 5,
            last_daily_reset_date: new Date().toISOString().split('T')[0]
        },
        {
            sn: 'TEST_SN_006',
            authSerial: 'auth_serial_006',
            auth_code: 'AUTH_CODE_006',
            auth_count: 15,
            last_au_time: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30分钟前
            status: 'authorized',
            first_auth_time: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5天前
            daily_request_count: 8,
            last_daily_reset_date: new Date().toISOString().split('T')[0]
        }
    ];

    const insertSql = `INSERT OR REPLACE INTO authorizations 
        (sn, authSerial, auth_code, auth_count, last_au_time, status, first_auth_time, daily_request_count, last_daily_reset_date) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`;

    let completed = 0;
    testData.forEach((data, index) => {
        db.run(insertSql, [
            data.sn,
            data.authSerial,
            data.auth_code,
            data.auth_count,
            data.last_au_time,
            data.status,
            data.first_auth_time,
            data.daily_request_count,
            data.last_daily_reset_date
        ], function(err) {
            if (err) {
                console.error(`Error inserting test data ${index + 1}:`, err.message);
            } else {
                console.log(`Test data ${index + 1} inserted successfully`);
            }
            
            completed++;
            if (completed === testData.length) {
                console.log('All test data inserted successfully');
                
                // 查询总记录数
                db.get(`SELECT COUNT(*) as total FROM authorizations`, (err, row) => {
                    if (err) {
                        console.error('Error counting records:', err.message);
                    } else {
                        console.log(`Total records in database: ${row.total}`);
                    }
                    
                    // 关闭数据库连接
                    db.close((err) => {
                        if (err) {
                            console.error('Error closing database:', err.message);
                        } else {
                            console.log('Database connection closed.');
                        }
                    });
                });
            }
        });
    });
}

// 执行添加测试数据
addTestData();
