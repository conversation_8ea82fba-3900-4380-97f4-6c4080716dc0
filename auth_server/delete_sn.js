const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'auth.db');
const snToDelete = '31333032303030343331323343424d30323936'; // 您提供的没有00结尾的SN

let db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('Error opening database:', err.message);
    } else {
        console.log('Connected to the SQLite database.');
        // 删除sn匹配且auth_code为NULL或status为'pending'的记录
        db.run(`DELETE FROM authorizations WHERE sn = ? AND (auth_code IS NULL OR status = 'pending')`, [snToDelete], function(err) {
            if (err) {
                console.error('Error deleting record:', err.message);
            } else {
                if (this.changes > 0) {
                    console.log(`Successfully deleted ${this.changes} record(s) for SN: ${snToDelete}`);
                } else {
                    console.log(`No matching unauthorized record found for SN: ${snToDelete}`);
                }
            }
            db.close((closeErr) => {
                if (closeErr) {
                    console.error('Error closing database:', closeErr.message);
                } else {
                    console.log('Database connection closed.');
                }
            });
        });
    }
});