const express = require('express');
const { connect, StringCodec, consumerOpts, createInbox } = require('nats');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const bodyParser = require('body-parser');
const multer = require('multer');
const fs = require('fs');

const app = express();
const port = 3006;

// 设置 EJS 作为模板引擎
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// 解析 application/json 和 application/x-www-form-urlencoded
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public'))); // 假设有静态文件

// 配置 multer 用于文件上传
const upload = multer({ dest: 'uploads/' });

// NATS 连接
let nc;
const sc = StringCodec();

// SQLite 数据库
let db;

/**
 * 初始化数据库连接和表
 */
function initDb() {
    db = new sqlite3.Database(path.join(__dirname, 'auth.db'), (err) => {
        if (err) {
            console.error('Error opening database:', err.message);
        } else {
            console.log('Connected to the SQLite database.');
            db.run(`CREATE TABLE IF NOT EXISTS authorizations (
                sn TEXT NOT NULL,
                authSerial TEXT NOT NULL,
                auth_code TEXT,
                auth_count INTEGER DEFAULT 0,
                last_au_time TEXT,
                status TEXT DEFAULT 'pending', -- 'pending', 'authorized'
                PRIMARY KEY (sn, authSerial)
            )`, (createErr) => {
                if (createErr) {
                    console.error('Error creating table:', createErr.message);
                } else {
                    console.log('Authorizations table created or already exists.');
                    // 添加新列，如果它们不存在
                    db.run(`ALTER TABLE authorizations ADD COLUMN first_auth_time TEXT`, (err) => {
                        if (err) { /* 忽略错误，因为列可能已存在 */ }
                        else { console.log('Added first_auth_time column.'); }
                    });
                    db.run(`ALTER TABLE authorizations ADD COLUMN daily_request_count INTEGER DEFAULT 0`, (err) => {
                        if (err) { /* 忽略错误，因为列可能已存在 */ }
                        else { console.log('Added daily_request_count column.'); }
                    });
                    db.run(`ALTER TABLE authorizations ADD COLUMN last_daily_reset_date TEXT`, (err) => {
                        if (err) { /* 忽略错误，因为列可能已存在 */ }
                        else { console.log('Added last_daily_reset_date column.'); }
                    });
                }
            });
        }
    });
}

/**
 * 初始化 NATS 连接并订阅 au_query
 */
async function initNats() {
    try {
        nc = await connect({ servers: "nats://113.45.77.38:4222" });
        console.log(`Connected to NATS at ${nc.getServer()}`);

        const sub = nc.subscribe("au_query");
        console.log("Subscribed to 'au_query' NATS topic.");

        (async () => {
            for await (const m of sub) {
                const msg = sc.decode(m.data);
                console.log(`[NATS] Received au_query: ${msg}`);
                try {
                    const { sn, authSerial } = JSON.parse(msg);
                    console.log(`[NATS] Parsed au_query - SN: ${sn}, AuthSerial: ${authSerial}`);

                    // 检查数据库中是否存在该 SN 和 AuthSerial 的记录
                    db.get(`SELECT * FROM authorizations WHERE sn = ? AND authSerial = ?`, [sn, authSerial], (err, row) => {
                        if (err) {
                            console.error('Error querying database:', err.message);
                            return;
                        }

                        const now = new Date();
                        const today = now.toISOString().split('T')[0]; // YYYY-MM-DD
                        let currentDailyRequestCount = row ? row.daily_request_count : 0;
                        let firstAuthTime = row ? row.first_auth_time : now.toISOString();
                        let lastDailyResetDate = row ? row.last_daily_reset_date : today;

                        // 如果不是今天第一次请求，则重置 daily_request_count
                        if (lastDailyResetDate !== today) {
                            currentDailyRequestCount = 0;
                            lastDailyResetDate = today;
                        }

                        currentDailyRequestCount++;

                        if (row && row.auth_code) {
                            // 已存在 auth_code，直接发布 au_response
                            console.log(`[NATS] SN ${sn} already authorized. Sending au_response.`);
                            const responseMessage = JSON.stringify({ sn, authCode: row.auth_code });
                            nc.publish("au_response", sc.encode(responseMessage));

                            // 更新统计信息，包括 daily_request_count 和 last_daily_reset_date
                            db.run(`UPDATE authorizations SET auth_count = auth_count + 1, last_au_time = ?, daily_request_count = ?, last_daily_reset_date = ? WHERE sn = ? AND authSerial = ?`,
                                [now.toISOString(), currentDailyRequestCount, lastDailyResetDate, sn, authSerial],
                                (updateErr) => {
                                    if (updateErr) console.error('Error updating auth count and time:', updateErr.message);
                                });
                        } else {
                            // 不存在 auth_code 或 status 不是 authorized，标记为 pending
                            const status = row && row.auth_code ? 'authorized' : 'pending'; // 如果已有 auth_code 但状态不是 authorized，则更新为 authorized
                            const upsertSql = `INSERT OR REPLACE INTO authorizations (sn, authSerial, auth_code, auth_count, last_au_time, status, first_auth_time, daily_request_count, last_daily_reset_date) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`;
                            db.run(upsertSql,
                                [sn, authSerial, row ? row.auth_code : null, row ? row.auth_count : 0, now.toISOString(), status, firstAuthTime, currentDailyRequestCount, lastDailyResetDate],
                                (upsertErr) => {
                                    if (upsertErr) {
                                        console.error('Error upserting authorization:', upsertErr.message);
                                    } else {
                                        console.log(`[DB] SN: ${sn}, AuthSerial: ${authSerial} added/updated as ${status}.`);
                                    }
                                });
                        }
                    });

                } catch (parseError) {
                    console.error(`[NATS] Error parsing au_query message: ${parseError.message}`);
                }
            }
        })();

    } catch (err) {
        console.error(`Error connecting to NATS: ${err.message}`);
    }
}

// 路由
app.get('/', (req, res) => {
    // 获取总记录数
    db.get(`SELECT COUNT(*) as total FROM authorizations`, (countErr, countResult) => {
        if (countErr) {
            res.status(500).send('Error retrieving authorization count');
            return;
        }

        // 获取第一页数据（默认每页10条）
        const sql = `SELECT * FROM authorizations
                     ORDER BY
                       CASE WHEN status = 'pending' THEN 0 ELSE 1 END,
                       CASE WHEN status = 'authorized' THEN last_au_time END DESC,
                       CASE WHEN status = 'pending' THEN last_au_time END DESC
                     LIMIT 10 OFFSET 0`;

        db.all(sql, (err, rows) => {
            if (err) {
                res.status(500).send('Error retrieving authorizations');
                return;
            }
            res.render('index', {
                authorizations: rows,
                totalRecords: countResult.total,
                currentPage: 1,
                pageSize: 10,
                totalPages: Math.ceil(countResult.total / 10)
            });
        });
    });
});

// API 获取授权数据（支持分页）
app.get('/api/authorizations', (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || 10;
    const offset = (page - 1) * pageSize;

    // 获取总记录数
    db.get(`SELECT COUNT(*) as total FROM authorizations`, (countErr, countResult) => {
        if (countErr) {
            res.status(500).json({ error: countErr.message });
            return;
        }

        // 获取分页数据，排序规则：未授权记录优先，已授权记录按最后授权时间倒序
        const sql = `SELECT * FROM authorizations
                     ORDER BY
                       CASE WHEN status = 'pending' THEN 0 ELSE 1 END,
                       CASE WHEN status = 'authorized' THEN last_au_time END DESC,
                       CASE WHEN status = 'pending' THEN last_au_time END DESC
                     LIMIT ? OFFSET ?`;

        db.all(sql, [pageSize, offset], (err, rows) => {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }

            res.json({
                data: rows,
                pagination: {
                    currentPage: page,
                    pageSize: pageSize,
                    totalRecords: countResult.total,
                    totalPages: Math.ceil(countResult.total / pageSize)
                }
            });
        });
    });
});

// API 数据库备份功能
app.get('/api/backup', (req, res) => {
    const dbPath = path.join(__dirname, 'auth.db');
    res.download(dbPath, 'auth_backup.db', (err) => {
        if (err) {
            console.error('Error during database backup:', err.message);
            res.status(500).send('Error backing up database.');
        }
    });
});

// API 提交授权码
app.post('/api/authorize', (req, res) => {
    const { sn, authSerial, authCode } = req.body;

    if (!sn || !authSerial || !authCode) {
        return res.status(400).json({ error: 'Missing sn, authSerial, or authCode' });
    }

    db.run(`UPDATE authorizations SET auth_code = ?, auth_count = auth_count + 1, last_au_time = ?, status = 'authorized' WHERE sn = ? AND authSerial = ?`,
        [authCode, new Date().toISOString(), sn, authSerial],
        function (err) {
            if (err) {
                console.error('Error updating authorization:', err.message);
                return res.status(500).json({ error: err.message });
            }
            if (this.changes === 0) {
                return res.status(404).json({ error: 'Authorization record not found.' });
            }

            console.log(`[DB] Updated SN: ${sn} with AuthCode: ${authCode}`);

            // 发布 au_response 消息到 NATS
            if (nc) {
                const responseMessage = JSON.stringify({ sn, authCode });
                nc.publish("au_response", sc.encode(responseMessage));
                console.log(`[NATS] Published au_response for SN ${sn}: ${responseMessage}`);
                res.json({ success: true, message: 'Authorization updated and au_response sent.' });
            } else {
                console.warn('[NATS] NATS connection not established, cannot publish au_response.');
                res.status(500).json({ error: 'NATS connection not established.' });
            }
        });
});

// API 数据库恢复功能
app.post('/api/restore', upload.single('backupFile'), (req, res) => {
    if (!req.file) {
        return res.status(400).send('No backup file uploaded.');
    }

    const uploadedFilePath = req.file.path;
    const dbPath = path.join(__dirname, 'auth.db');

    // 关闭当前数据库连接
    if (db) {
        db.close((err) => {
            if (err) {
                console.error('Error closing database before restore:', err.message);
                return res.status(500).send('Failed to close database.');
            }
            console.log('Database closed for restore.');
            performRestore();
        });
    } else {
        performRestore();
    }

    function performRestore() {
        // 删除现有数据库文件
        fs.unlink(dbPath, (err) => {
            if (err && err.code !== 'ENOENT') { // 忽略文件不存在的错误
                console.error('Error deleting existing database:', err.message);
                return res.status(500).send('Failed to delete existing database.');
            }
            console.log('Existing database deleted (if it existed).');

            // 将上传的文件移动到数据库路径
            fs.rename(uploadedFilePath, dbPath, (renameErr) => {
                if (renameErr) {
                    console.error('Error restoring database:', renameErr.message);
                    return res.status(500).send('Failed to restore database.');
                }
                console.log('Database restored from backup.');

                // 重新初始化数据库连接
                initDb();
                res.send('Database restored successfully.');
            });
        });
    }
});

// 启动服务器
const server = app.listen(port, () => {
    console.log(`Auth server listening at http://localhost:${port}`);
    initDb();
    initNats();
});

// 优雅关闭 NATS 连接
process.on('SIGTERM', () => {
    console.log('SIGTERM signal received: closing NATS connection, Express server, and SQLite DB.');
    if (nc) {
        nc.close();
    }
    if (server) {
        server.close(() => {
            console.log('Express server closed.');
            if (db) {
                db.close((err) => {
                    if (err) {
                        console.error('Error closing database:', err.message);
                    } else {
                        console.log('SQLite database closed.');
                    }
                    process.exit(0);
                });
            } else {
                process.exit(0);
            }
        });
    } else if (db) {
        db.close((err) => {
            if (err) {
                console.error('Error closing database:', err.message);
            } else {
                console.log('SQLite database closed.');
            }
            process.exit(0);
        });
    } else {
        process.exit(0);
    }
});

process.on('SIGINT', () => {
    console.log('SIGINT signal received: closing NATS connection, Express server, and SQLite DB.');
    if (nc) {
        nc.close();
    }
    if (server) {
        server.close(() => {
            console.log('Express server closed.');
            if (db) {
                db.close((err) => {
                    if (err) {
                        console.error('Error closing database:', err.message);
                    } else {
                        console.log('SQLite database closed.');
                    }
                    process.exit(0);
                });
            } else {
                process.exit(0);
            }
        });
    } else if (db) {
        db.close((err) => {
            if (err) {
                console.error('Error closing database:', err.message);
            } else {
                console.log('SQLite database closed.');
            }
            process.exit(0);
        });
    } else {
        process.exit(0);
    }
}); 