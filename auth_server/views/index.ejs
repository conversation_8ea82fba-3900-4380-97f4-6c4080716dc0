<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>授权管理系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f4f4f4; color: #333; }
        h1 { color: #0056b3; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; background-color: #fff; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        th, td { padding: 12px 15px; border: 1px solid #ddd; text-align: left; }
        th { background-color: #007bff; color: white; }
        tr:nth-child(even) { background-color: #f2f2f2; }
        tr:hover { background-color: #e9e9e9; }
        .status-pending { color: orange; font-weight: bold; }
        .status-authorized { color: green; }
        .action-form { display: flex; gap: 5px; align-items: center; }
        .action-form input[type="text"] { padding: 8px; border: 1px solid #ccc; border-radius: 4px; flex-grow: 1; }
        .action-form button { padding: 8px 12px; background-color: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .action-form button:hover { background-color: #218838; }
        .refresh-button { padding: 10px 15px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin-bottom: 20px; }
        .refresh-button:hover { background-color: #0056b3; }
    </style>
</head>
<body>
    <h1>授权管理系统</h1>
    <button class="refresh-button" onclick="fetchAuthorizations()">刷新数据</button>

    <div style="margin-top: 20px;">
        <button class="refresh-button" onclick="backupDatabase()" style="background-color: #ffc107;">数据库备份</button>
        <input type="file" id="backupFileInput" accept=".db" style="margin-left: 10px;">
        <button class="refresh-button" onclick="restoreDatabase()" style="background-color: #dc3545;">数据库恢复</button>
    </div>

    <table id="authorizationTable">
        <thead>
            <tr>
                <th>SN</th>
                <th>授权串号 (AuthSerial)</th>
                <th>授权码 (AuthCode)</th>
                <th>授权次数 (AuthCount)</th>
                <th>最后授权时间 (LastAuthTime)</th>
                <th>第一次授权时间 (FirstAuthTime)</th>
                <th>当天请求次数 (DailyReqCount)</th>
                <th>状态</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            <% authorizations.forEach(function(auth) { %>
                <tr>
                    <td><%= auth.sn %></td>
                    <td><%= auth.authSerial %></td>
                    <td><%= auth.auth_code || '未设置' %></td>
                    <td><%= auth.auth_count %></td>
                    <td><%= auth.last_au_time ? new Date(auth.last_au_time).toLocaleString() : 'N/A' %></td>
                    <td><%= auth.first_auth_time ? new Date(auth.first_auth_time).toLocaleString() : 'N/A' %></td>
                    <td><%= auth.daily_request_count %></td>
                    <td class="status-<%= auth.status === 'pending' ? 'pending' : 'authorized' %>"><%= auth.status === 'pending' ? '待处理' : '已授权' %></td>
                    <td>
                        <% if (auth.status === 'pending') { %>
                            <div class="action-form">
                                <input type="text" placeholder="输入授权码" id="authCode_<%= auth.sn %>_<%= auth.authSerial %>">
                                <button onclick="authorize('<%= auth.sn %>', '<%= auth.authSerial %>')">授权</button>
                            </div>
                        <% } else { %>
                            <span>已完成</span>
                        <% } %>
                    </td>
                </tr>
            <% }); %>
        </tbody>
    </table>

    <script>
        async function fetchAuthorizations() {
            try {
                const response = await fetch('/api/authorizations');
                const data = await response.json();
                const tableBody = document.querySelector('#authorizationTable tbody');
                tableBody.innerHTML = ''; // 清空现有内容

                data.forEach(auth => {
                    const row = `
                        <tr>
                            <td>${auth.sn}</td>
                            <td>${auth.authSerial}</td>
                            <td>${auth.auth_code || '未设置'}</td>
                            <td>${auth.auth_count}</td>
                            <td>${auth.last_au_time ? new Date(auth.last_au_time).toLocaleString() : 'N/A'}</td>
                            <td>${auth.first_auth_time ? new Date(auth.first_auth_time).toLocaleString() : 'N/A'}</td>
                            <td>${auth.daily_request_count}</td>
                            <td class="status-${auth.status === 'pending' ? 'pending' : 'authorized'}">${auth.status === 'pending' ? '待处理' : '已授权'}</td>
                            <td>
                                ${auth.status === 'pending' ? `
                                    <div class="action-form">
                                        <input type="text" placeholder="输入授权码" id="authCode_${auth.sn}_${auth.authSerial}">
                                        <button onclick="authorize('${auth.sn}', '${auth.authSerial}')">授权</button>
                                    </div>
                                ` : `
                                    <span>已完成</span>
                                `}
                            </td>
                        </tr>
                    `;
                    tableBody.innerHTML += row;
                });
            } catch (error) {
                console.error('Error fetching authorizations:', error);
                alert('获取授权数据失败！');
            }
        }

        async function authorize(sn, authSerial) {
            const authCodeInput = document.querySelector(`#authCode_${sn}_${authSerial}`);
            const authCode = authCodeInput.value.trim();

            if (!authCode) {
                alert('请输入授权码！');
                return;
            }

            try {
                const response = await fetch('/api/authorize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ sn, authSerial, authCode }),
                });

                const result = await response.json();

                if (response.ok) {
                    alert(result.message);
                    fetchAuthorizations(); // 刷新表格数据
                } else {
                    alert(`授权失败: ${result.error || response.statusText}`);
                }
            } catch (error) {
                console.error('Error authorizing:', error);
                alert('授权过程中发生错误！');
            }
        }

        // 页面加载完成后立即获取数据
        document.addEventListener('DOMContentLoaded', fetchAuthorizations);

        async function backupDatabase() {
            try {
                const response = await fetch('/api/backup');
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'auth_backup.db';
                    document.body.appendChild(a);
                    a.click();
                    a.remove();
                    window.URL.revokeObjectURL(url);
                    alert('数据库备份成功！');
                } else {
                    const errorText = await response.text();
                    alert(`数据库备份失败: ${errorText}`);
                }
            } catch (error) {
                console.error('Error during backup:', error);
                alert('备份过程中发生错误！');
            }
        }

        async function restoreDatabase() {
            const fileInput = document.getElementById('backupFileInput');
            const file = fileInput.files[0];

            if (!file) {
                alert('请选择一个备份文件！');
                return;
            }

            const formData = new FormData();
            formData.append('backupFile', file);

            try {
                const response = await fetch('/api/restore', {
                    method: 'POST',
                    body: formData,
                });

                const resultText = await response.text();

                if (response.ok) {
                    alert(`数据库恢复成功: ${resultText}`);
                    fetchAuthorizations(); // 恢复后刷新数据
                } else {
                    alert(`数据库恢复失败: ${resultText || response.statusText}`);
                }
            } catch (error) {
                console.error('Error during restore:', error);
                alert('恢复过程中发生错误！');
            }
        }
    </script>
</body>
</html> 