<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>授权管理系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f4f4f4; color: #333; }
        h1 { color: #0056b3; }
        .info-bar { background-color: #e9ecef; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .info-bar h3 { margin: 0; color: #495057; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; background-color: #fff; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        th, td { padding: 12px 15px; border: 1px solid #ddd; text-align: left; }
        th { background-color: #007bff; color: white; }
        tr:nth-child(even) { background-color: #f2f2f2; }
        tr:hover { background-color: #e9e9e9; }
        .status-pending { color: orange; font-weight: bold; }
        .status-authorized { color: green; }
        .action-form { display: flex; gap: 5px; align-items: center; }
        .action-form input[type="text"] { padding: 8px; border: 1px solid #ccc; border-radius: 4px; flex-grow: 1; }
        .action-form button { padding: 8px 12px; background-color: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .action-form button:hover { background-color: #218838; }
        .refresh-button { padding: 10px 15px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin-bottom: 20px; }
        .refresh-button:hover { background-color: #0056b3; }
        .pagination { display: flex; justify-content: center; align-items: center; margin-top: 20px; gap: 10px; }
        .pagination button { padding: 8px 12px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .pagination button:hover { background-color: #0056b3; }
        .pagination button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .pagination .current-page { font-weight: bold; color: #007bff; }
        .page-size-selector { margin-left: 20px; }
        .page-size-selector select { padding: 5px; border: 1px solid #ccc; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>授权管理系统</h1>

    <div class="info-bar">
        <h3 id="recordInfo">总记录数: <span id="totalRecords"><%= totalRecords || 0 %></span> 条</h3>
    </div>

    <button class="refresh-button" onclick="fetchAuthorizations()">刷新数据</button>

    <div style="margin-top: 20px;">
        <button class="refresh-button" onclick="backupDatabase()" style="background-color: #ffc107;">数据库备份</button>
        <input type="file" id="backupFileInput" accept=".db" style="margin-left: 10px;">
        <button class="refresh-button" onclick="restoreDatabase()" style="background-color: #dc3545;">数据库恢复</button>

        <div class="page-size-selector">
            <label for="pageSizeSelect">每页显示:</label>
            <select id="pageSizeSelect" onchange="changePageSize()">
                <option value="10">10条</option>
                <option value="20">20条</option>
                <option value="50">50条</option>
                <option value="100">100条</option>
            </select>
        </div>
    </div>

    <table id="authorizationTable">
        <thead>
            <tr>
                <th>序号</th>
                <th>SN</th>
                <th>授权串号 (AuthSerial)</th>
                <th>授权码 (AuthCode)</th>
                <th>授权次数 (AuthCount)</th>
                <th>最后授权时间 (LastAuthTime)</th>
                <th>第一次授权时间 (FirstAuthTime)</th>
                <th>当天请求次数 (DailyReqCount)</th>
                <th>状态</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            <% authorizations.forEach(function(auth, index) { %>
                <tr>
                    <td><%= (currentPage - 1) * pageSize + index + 1 %></td>
                    <td><%= auth.sn %></td>
                    <td><%= auth.authSerial %></td>
                    <td><%= auth.auth_code || '未设置' %></td>
                    <td><%= auth.auth_count %></td>
                    <td><%= auth.last_au_time ? new Date(auth.last_au_time).toLocaleString() : 'N/A' %></td>
                    <td><%= auth.first_auth_time ? new Date(auth.first_auth_time).toLocaleString() : 'N/A' %></td>
                    <td><%= auth.daily_request_count %></td>
                    <td class="status-<%= auth.status === 'pending' ? 'pending' : 'authorized' %>"><%= auth.status === 'pending' ? '待处理' : '已授权' %></td>
                    <td>
                        <% if (auth.status === 'pending') { %>
                            <div class="action-form">
                                <input type="text" placeholder="输入授权码" id="authCode_<%= auth.sn %>_<%= auth.authSerial %>">
                                <button onclick="authorize('<%= auth.sn %>', '<%= auth.authSerial %>')">授权</button>
                            </div>
                        <% } else { %>
                            <span>已完成</span>
                        <% } %>
                    </td>
                </tr>
            <% }); %>
        </tbody>
    </table>

    <div class="pagination">
        <button onclick="goToPage(1)" <% if (currentPage <= 1) { %>disabled<% } %>>首页</button>
        <button onclick="goToPage(<%= currentPage - 1 %>)" <% if (currentPage <= 1) { %>disabled<% } %>>上一页</button>
        <span class="current-page">第 <span id="currentPageSpan"><%= currentPage %></span> 页 / 共 <span id="totalPagesSpan"><%= totalPages %></span> 页</span>
        <button onclick="goToPage(<%= currentPage + 1 %>)" <% if (currentPage >= totalPages) { %>disabled<% } %>>下一页</button>
        <button onclick="goToPage(<%= totalPages %>)" <% if (currentPage >= totalPages) { %>disabled<% } %>>末页</button>
    </div>

    <script>
        let currentPage = <%= currentPage || 1 %>;
        let pageSize = <%= pageSize || 10 %>;
        let totalPages = <%= totalPages || 1 %>;
        let totalRecords = <%= totalRecords || 0 %>;

        async function fetchAuthorizations(page = currentPage, size = pageSize) {
            try {
                const response = await fetch(`/api/authorizations?page=${page}&pageSize=${size}`);
                const result = await response.json();

                if (result.data) {
                    const tableBody = document.querySelector('#authorizationTable tbody');
                    tableBody.innerHTML = ''; // 清空现有内容

                    result.data.forEach((auth, index) => {
                        const serialNumber = (result.pagination.currentPage - 1) * result.pagination.pageSize + index + 1;
                        const row = `
                            <tr>
                                <td>${serialNumber}</td>
                                <td>${auth.sn}</td>
                                <td>${auth.authSerial}</td>
                                <td>${auth.auth_code || '未设置'}</td>
                                <td>${auth.auth_count}</td>
                                <td>${auth.last_au_time ? new Date(auth.last_au_time).toLocaleString() : 'N/A'}</td>
                                <td>${auth.first_auth_time ? new Date(auth.first_auth_time).toLocaleString() : 'N/A'}</td>
                                <td>${auth.daily_request_count}</td>
                                <td class="status-${auth.status === 'pending' ? 'pending' : 'authorized'}">${auth.status === 'pending' ? '待处理' : '已授权'}</td>
                                <td>
                                    ${auth.status === 'pending' ? `
                                        <div class="action-form">
                                            <input type="text" placeholder="输入授权码" id="authCode_${auth.sn}_${auth.authSerial}">
                                            <button onclick="authorize('${auth.sn}', '${auth.authSerial}')">授权</button>
                                        </div>
                                    ` : `
                                        <span>已完成</span>
                                    `}
                                </td>
                            </tr>
                        `;
                        tableBody.innerHTML += row;
                    });

                    // 更新分页信息
                    currentPage = result.pagination.currentPage;
                    pageSize = result.pagination.pageSize;
                    totalPages = result.pagination.totalPages;
                    totalRecords = result.pagination.totalRecords;

                    updatePaginationUI();
                    updateRecordInfo();
                } else {
                    // 兼容旧格式
                    const tableBody = document.querySelector('#authorizationTable tbody');
                    tableBody.innerHTML = '';
                    result.forEach((auth, index) => {
                        const serialNumber = (currentPage - 1) * pageSize + index + 1;
                        const row = `
                            <tr>
                                <td>${serialNumber}</td>
                                <td>${auth.sn}</td>
                                <td>${auth.authSerial}</td>
                                <td>${auth.auth_code || '未设置'}</td>
                                <td>${auth.auth_count}</td>
                                <td>${auth.last_au_time ? new Date(auth.last_au_time).toLocaleString() : 'N/A'}</td>
                                <td>${auth.first_auth_time ? new Date(auth.first_auth_time).toLocaleString() : 'N/A'}</td>
                                <td>${auth.daily_request_count}</td>
                                <td class="status-${auth.status === 'pending' ? 'pending' : 'authorized'}">${auth.status === 'pending' ? '待处理' : '已授权'}</td>
                                <td>
                                    ${auth.status === 'pending' ? `
                                        <div class="action-form">
                                            <input type="text" placeholder="输入授权码" id="authCode_${auth.sn}_${auth.authSerial}">
                                            <button onclick="authorize('${auth.sn}', '${auth.authSerial}')">授权</button>
                                        </div>
                                    ` : `
                                        <span>已完成</span>
                                    `}
                                </td>
                            </tr>
                        `;
                        tableBody.innerHTML += row;
                    });
                }
            } catch (error) {
                console.error('Error fetching authorizations:', error);
                alert('获取授权数据失败！');
            }
        }

        async function authorize(sn, authSerial) {
            const authCodeInput = document.querySelector(`#authCode_${sn}_${authSerial}`);
            const authCode = authCodeInput.value.trim();

            if (!authCode) {
                alert('请输入授权码！');
                return;
            }

            try {
                const response = await fetch('/api/authorize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ sn, authSerial, authCode }),
                });

                const result = await response.json();

                if (response.ok) {
                    alert(result.message);
                    fetchAuthorizations(); // 刷新表格数据
                } else {
                    alert(`授权失败: ${result.error || response.statusText}`);
                }
            } catch (error) {
                console.error('Error authorizing:', error);
                alert('授权过程中发生错误！');
            }
        }

        // 分页控制函数
        function goToPage(page) {
            if (page < 1 || page > totalPages) return;
            fetchAuthorizations(page, pageSize);
        }

        function changePageSize() {
            const newPageSize = parseInt(document.getElementById('pageSizeSelect').value);
            pageSize = newPageSize;
            currentPage = 1; // 重置到第一页
            fetchAuthorizations(currentPage, pageSize);
        }

        function updatePaginationUI() {
            document.getElementById('currentPageSpan').textContent = currentPage;
            document.getElementById('totalPagesSpan').textContent = totalPages;

            // 更新分页按钮状态
            const pagination = document.querySelector('.pagination');
            pagination.innerHTML = `
                <button onclick="goToPage(1)" ${currentPage <= 1 ? 'disabled' : ''}>首页</button>
                <button onclick="goToPage(${currentPage - 1})" ${currentPage <= 1 ? 'disabled' : ''}>上一页</button>
                <span class="current-page">第 <span id="currentPageSpan">${currentPage}</span> 页 / 共 <span id="totalPagesSpan">${totalPages}</span> 页</span>
                <button onclick="goToPage(${currentPage + 1})" ${currentPage >= totalPages ? 'disabled' : ''}>下一页</button>
                <button onclick="goToPage(${totalPages})" ${currentPage >= totalPages ? 'disabled' : ''}>末页</button>
            `;
        }

        function updateRecordInfo() {
            document.getElementById('totalRecords').textContent = totalRecords;
        }

        // 页面加载完成后立即获取数据
        document.addEventListener('DOMContentLoaded', () => {
            // 设置页面大小选择器的默认值
            document.getElementById('pageSizeSelect').value = pageSize;
            fetchAuthorizations();
        });

        async function backupDatabase() {
            try {
                const response = await fetch('/api/backup');
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'auth_backup.db';
                    document.body.appendChild(a);
                    a.click();
                    a.remove();
                    window.URL.revokeObjectURL(url);
                    alert('数据库备份成功！');
                } else {
                    const errorText = await response.text();
                    alert(`数据库备份失败: ${errorText}`);
                }
            } catch (error) {
                console.error('Error during backup:', error);
                alert('备份过程中发生错误！');
            }
        }

        async function restoreDatabase() {
            const fileInput = document.getElementById('backupFileInput');
            const file = fileInput.files[0];

            if (!file) {
                alert('请选择一个备份文件！');
                return;
            }

            const formData = new FormData();
            formData.append('backupFile', file);

            try {
                const response = await fetch('/api/restore', {
                    method: 'POST',
                    body: formData,
                });

                const resultText = await response.text();

                if (response.ok) {
                    alert(`数据库恢复成功: ${resultText}`);
                    fetchAuthorizations(); // 恢复后刷新数据
                } else {
                    alert(`数据库恢复失败: ${resultText || response.statusText}`);
                }
            } catch (error) {
                console.error('Error during restore:', error);
                alert('恢复过程中发生错误！');
            }
        }
    </script>
</body>
</html> 