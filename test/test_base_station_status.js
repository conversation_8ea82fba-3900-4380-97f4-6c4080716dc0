// test_base_station_status.js - 测试基站状态查询功能
const { connect, StringCodec } = require('nats');
const { v4: uuidv4 } = require('uuid');

const sc = StringCodec();

async function testBaseStationStatusQuery() {
  let natsConnection;
  try {
    console.log('[Base Station Status Test] 连接到NATS服务器...');
    natsConnection = await connect({ servers: "nats://************:4222" });
    console.log('[Base Station Status Test] NATS连接成功。');

    // 1. 获取可用的工具列表
    console.log('\n=== 步骤1: 获取MCP工具列表 ===');
    const toolsMsg = await natsConnection.request('mcp.get_llm_tools', '', { timeout: 5000 });
    const tools = JSON.parse(sc.decode(toolsMsg.data));
    
    console.log('[Base Station Status Test] 可用的工具:');
    tools.forEach(tool => {
      console.log(`  - ${tool.name}: ${tool.description}`);
    });

    // 2. 查询所有基站状态
    console.log('\n=== 步骤2: 查询所有基站状态 ===');
    await queryAllBaseStationStatus(natsConnection);

    // 3. 查询特定基站状态
    console.log('\n=== 步骤3: 查询特定基站状态 ===');
    await querySpecificBaseStationStatus(natsConnection, '*************');

    // 4. 查询不存在的基站状态
    console.log('\n=== 步骤4: 查询不存在的基站状态 ===');
    await querySpecificBaseStationStatus(natsConnection, '192.168.1.999');

    console.log('\n[Base Station Status Test] 所有测试完成。');

  } catch (err) {
    console.error('[Base Station Status Test] 测试失败:', err);
  } finally {
    if (natsConnection) {
      await natsConnection.close();
      console.log('\n[Base Station Status Test] NATS连接已关闭。');
    }
  }
}

/**
 * 查询所有基站状态
 */
async function queryAllBaseStationStatus(natsConnection) {
  const correlationId = uuidv4();
  
  const toolCallRequest = {
    tool_name: 'query_base_station_status',
    tool_args: {
      target_ip: '' // 空字符串表示查询所有基站
    },
    correlation_id: correlationId,
    target_ip: 'virtual' // 虚拟工具不需要真实IP
  };

  console.log(`[Base Station Status Test] 查询所有基站状态:`);
  console.log(`  关联ID: ${correlationId}`);

  // 订阅响应
  const sub = natsConnection.subscribe('llm.tool_results');
  
  // 发送请求
  natsConnection.publish('llm.invoke_tool', sc.encode(JSON.stringify(toolCallRequest)));
  console.log(`[Base Station Status Test] 请求已发送，等待响应...`);
  
  // 等待响应
  const result = await waitForResponse(sub, correlationId, 5000);
  
  if (result.success) {
    console.log(`[Base Station Status Test] ✅ 查询所有基站状态成功`);
    const data = result.response.tool_output;
    if (data.success && data.data && data.data.length > 0) {
      console.log(`  找到 ${data.total} 个基站:`);
      data.data.forEach((station, index) => {
        console.log(`\n  --- 基站 ${index + 1} ---`);
        console.log(`  IP地址: ${station.基站信息.IP地址}`);
        console.log(`  端口: ${station.基站信息.端口}`);
        console.log(`  SN号: ${station.基站信息.SN号}`);
        console.log(`  小区状态: ${station.小区状态.状态描述}`);
        console.log(`  频点: UL=${station.频率信息.上行频点}, DL=${station.频率信息.下行频点}`);
        console.log(`  PCI: ${station.小区状态.物理小区ID}, 带宽: ${station.频率信息.带宽描述}`);
        console.log(`  PLMN: ${station.小区状态.PLMN}`);
        console.log(`  数据新鲜度: ${station.技术参数.数据新鲜度}`);
      });
    } else {
      console.log(`  ⚠️ 未找到任何基站状态信息`);
      console.log(`  提示: 请确保基站已连接并发送心跳报文`);
    }
  } else {
    console.log(`[Base Station Status Test] ❌ 查询所有基站状态失败: ${result.error}`);
  }
}

/**
 * 查询特定基站状态
 */
async function querySpecificBaseStationStatus(natsConnection, targetIp) {
  const correlationId = uuidv4();
  
  const toolCallRequest = {
    tool_name: 'query_base_station_status',
    tool_args: {
      target_ip: targetIp
    },
    correlation_id: correlationId,
    target_ip: 'virtual' // 虚拟工具不需要真实IP
  };

  console.log(`[Base Station Status Test] 查询基站 ${targetIp} 状态:`);
  console.log(`  关联ID: ${correlationId}`);

  // 订阅响应
  const sub = natsConnection.subscribe('llm.tool_results');
  
  // 发送请求
  natsConnection.publish('llm.invoke_tool', sc.encode(JSON.stringify(toolCallRequest)));
  console.log(`[Base Station Status Test] 请求已发送，等待响应...`);
  
  // 等待响应
  const result = await waitForResponse(sub, correlationId, 5000);
  
  if (result.success) {
    const data = result.response.tool_output;
    if (data.success && data.data && data.data.length > 0) {
      console.log(`[Base Station Status Test] ✅ 查询基站 ${targetIp} 状态成功`);
      const station = data.data[0];
      console.log(`\n  --- 基站详细信息 ---`);
      console.log(`  基站信息:`);
      console.log(`    IP地址: ${station.基站信息.IP地址}`);
      console.log(`    端口: ${station.基站信息.端口}`);
      console.log(`    SN号: ${station.基站信息.SN号}`);
      console.log(`    是否携带SN: ${station.基站信息.是否携带SN}`);
      console.log(`    心跳次数: ${station.基站信息.心跳次数}`);
      console.log(`    最后心跳时间: ${station.基站信息.最后心跳时间}`);
      
      console.log(`  小区状态:`);
      console.log(`    状态码: ${station.小区状态.状态码}`);
      console.log(`    状态描述: ${station.小区状态.状态描述}`);
      console.log(`    PLMN: ${station.小区状态.PLMN}`);
      console.log(`    物理小区ID: ${station.小区状态.物理小区ID}`);
      console.log(`    跟踪区码: ${station.小区状态.跟踪区码}`);
      
      console.log(`  频率信息:`);
      console.log(`    频段: ${station.频率信息.频段}`);
      console.log(`    上行频点: ${station.频率信息.上行频点}`);
      console.log(`    下行频点: ${station.频率信息.下行频点}`);
      console.log(`    系统带宽: ${station.频率信息.系统带宽}`);
      console.log(`    带宽描述: ${station.频率信息.带宽描述}`);
      
      console.log(`  技术参数:`);
      console.log(`    更新时间戳: ${station.技术参数.更新时间戳}`);
      console.log(`    数据新鲜度: ${station.技术参数.数据新鲜度}`);
      
    } else {
      console.log(`[Base Station Status Test] ⚠️ 未找到基站 ${targetIp} 的状态信息`);
      if (data.error) {
        console.log(`  错误: ${data.error}`);
      }
      if (data.suggestion) {
        console.log(`  建议: ${data.suggestion}`);
      }
    }
  } else {
    console.log(`[Base Station Status Test] ❌ 查询基站 ${targetIp} 状态失败: ${result.error}`);
  }
}

/**
 * 等待指定correlation_id的响应
 */
async function waitForResponse(subscription, correlationId, timeoutMs) {
  return new Promise((resolve) => {
    const timeout = setTimeout(() => {
      resolve({ success: false, error: '响应超时' });
    }, timeoutMs);

    (async () => {
      for await (const m of subscription) {
        try {
          const response = JSON.parse(sc.decode(m.data));
          if (response.correlation_id === correlationId) {
            clearTimeout(timeout);
            resolve({ success: true, response });
            break;
          }
        } catch (parseError) {
          console.error('[Base Station Status Test] 解析响应失败:', parseError);
        }
      }
    })();
  });
}

// 运行测试
if (require.main === module) {
  testBaseStationStatusQuery();
}

module.exports = { testBaseStationStatusQuery };
