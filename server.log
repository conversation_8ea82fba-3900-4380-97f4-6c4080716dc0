Server is running on port 3000
[UDP] 启动参数 boards: [
  { ip: '*************', port: 50001 },
  { ip: '*************', port: 50002 }
]
[UDP] 设备列表: [
  {
    ip: '*************',
    port: 50001,
    sn: null,
    hasSN: false,
    heartbeatCount: 0,
    lastHeartbeat: null,
    authenticated: false,
    lastAuthDate: null,
    authSerial: null,
    authCode: null
  },
  {
    ip: '*************',
    port: 50002,
    sn: null,
    hasSN: false,
    heartbeatCount: 0,
    lastHeartbeat: null,
    authenticated: false,
    lastAuthDate: null,
    authSerial: null,
    authCode: null
  }
]
[UDP Server] Listening 0.0.0.0:5555
[UDP] addDevice: Device with IP ************* added.

--- [UDP Server] Sending UDP Packet to Board ---
  Target: *************:50002
  hasSN: false
  Original (Hex): 35353535616161616631313730303230303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030
  Final (Hex): 35353535616161616631313730303230303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030
  Encrypted (Hex): 353535356161616166316543527b6ab38ffe3715527b68b38ffe3715527b68b38ffe3715527b68b38ffe3715527b68b38ffe3715527b68b38ffe3715527b68b3
  Length: 64 bytes
--- [UDP Server] UDP Packet Sent to *************:50002 ---
[UDP] removeDevice: Device with IP ************* removed.
[UDP] sendMessageToBoard: Device with IP ************* not found.
UDP server stopped.
