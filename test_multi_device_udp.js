const http = require('http');

const API_HOSTNAME = 'localhost';
const API_PORT = 3000;

// Helper function to make HTTP requests
function makeRequest(method, path, data) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: API_HOSTNAME,
            port: API_PORT,
            path: path,
            method: method,
            headers: {
                'Content-Type': 'application/json',
            },
        };

        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => (body += chunk));
            res.on('end', () => {
                console.log(`Response from ${method} ${path}: ${res.statusCode}`);
                console.log(` > Body: ${body}`);
                if (res.statusCode >= 200 && res.statusCode < 300) {
                    resolve(JSON.parse(body || '{}'));
                } else {
                    reject(new Error(`Request failed with status ${res.statusCode}: ${body}`));
                }
            });
        });

        req.on('error', (e) => {
            console.error(`Request error for ${method} ${path}:`, e);
            reject(e);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }
        req.end();
    });
}

async function runTest() {
    console.log('--- Starting Multi-Device UDP Test Suite ---');

    // --- Test Data ---
    const initialBoards = [
        { ip: '*************', port: 50001 },
        { ip: '*************', port: 50002 },
    ];
    const newDevice = { ip: '*************', port: 50003 };
    const deviceToRemoveIp = '*************';
    const messageToSend = {
        targetIp: '*************',
        // A dummy hex representation of a message packet
        message: '5555aaaaf1170020000000000000000000000000000000000000000000000000',
    };

    try {
        // 1. Start UDP server with initial devices
        console.log('\nStep 1: Starting UDP server with initial devices...');
        await makeRequest('POST', '/api/udp/start', {
            listenIp: '0.0.0.0',
            listenPort: 5555,
            boards: initialBoards,
        });
        console.log(' > Check server logs to confirm startup with 2 devices.');
        await new Promise(resolve => setTimeout(resolve, 500)); // Wait for server to process

        // 2. Dynamically add a new device
        console.log(`\nStep 2: Dynamically adding device ${newDevice.ip}...`);
        await makeRequest('POST', '/api/udp/device', newDevice);
        console.log(' > Check server logs to confirm device was added.');
        await new Promise(resolve => setTimeout(resolve, 500));

        // 3. Send a message to a specific device
        console.log(`\nStep 3: Sending a message to a specific device ${messageToSend.targetIp}...`);
        await makeRequest('POST', '/api/udp/send', messageToSend);
        console.log(` > Check server logs to confirm message was sent to ${messageToSend.targetIp} on its specific port.`);
        await new Promise(resolve => setTimeout(resolve, 500));

        // 4. Dynamically remove a device
        console.log(`\nStep 4: Dynamically removing device ${deviceToRemoveIp}...`);
        await makeRequest('DELETE', `/api/udp/device/${deviceToRemoveIp}`);
        console.log(' > Check server logs to confirm device was removed.');
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 5. Attempt to send a message to the removed device (should fail)
        console.log(`\nStep 5: Attempting to send a message to the removed device ${deviceToRemoveIp} (should fail)...`);
        const messageToRemoved = { targetIp: deviceToRemoveIp, message: '00' };
        await makeRequest('POST', '/api/udp/send', messageToRemoved);
        console.log(` > Check server logs for an error message indicating the device was not found.`);

    } catch (error) {
        console.error('\n--- TEST FAILED ---');
        console.error(error.message);
        process.exit(1);
    } finally {
        // 6. Stop the UDP server to clean up for the next run
        console.log('\nStep 6: Stopping UDP server...');
        await makeRequest('POST', '/api/udp/stop', {});
    }

    console.log('\n--- Multi-Device UDP Test Suite Completed Successfully ---');
    console.log('Please review the server logs to verify all steps behaved as expected.');
}

// Run the test
runTest();