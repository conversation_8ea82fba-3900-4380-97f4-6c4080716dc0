#!/bin/bash

# MCP Server 统一启动脚本
# 用于一键启动所有必要的服务：主服务、认证服务和MCP服务

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Node.js是否安装
check_nodejs() {
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    local node_version=$(node --version)
    log_info "检测到 Node.js 版本: $node_version"
}

# 检查npm依赖
check_dependencies() {
    log_info "检查项目依赖..."
    
    if [ ! -d "node_modules" ]; then
        log_warning "未找到 node_modules，正在安装依赖..."
        npm install
    fi
    
    # 检查auth_server依赖
    if [ ! -d "auth_server/node_modules" ]; then
        log_warning "未找到 auth_server 依赖，正在安装..."
        cd auth_server && npm install && cd ..
    fi
    
    log_success "依赖检查完成"
}

# 检查必要文件
check_files() {
    log_info "检查必要文件..."
    
    local required_files=(
        "index.js"
        "udp_server.js"
        "auth_server/app.js"
        "mcp_server/app.js"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "缺少必要文件: $file"
            exit 1
        fi
    done
    
    log_success "文件检查完成"
}

# 检查端口占用
check_ports() {
    log_info "检查端口占用情况..."
    
    local ports=(3000 3001 4222)  # 主服务、认证服务、NATS端口
    
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            log_warning "端口 $port 已被占用"
        fi
    done
}

# 启动服务函数
start_services() {
    log_info "正在启动所有服务..."
    
    # 使用concurrently并发启动所有服务
    if command -v npx &> /dev/null; then
        log_info "使用 concurrently 启动服务..."
        npx concurrently \
            --prefix "[{name}]" \
            --names "MAIN,AUTH,MCP" \
            --prefix-colors "cyan,magenta,yellow" \
            "node index.js" \
            "node auth_server/app.js" \
            "node mcp_server/app.js"
    else
        log_warning "未找到 npx，使用传统方式启动..."
        
        # 后台启动各个服务
        log_info "启动主服务 (index.js)..."
        node index.js &
        MAIN_PID=$!
        
        log_info "启动认证服务 (auth_server/app.js)..."
        node auth_server/app.js &
        AUTH_PID=$!
        
        log_info "启动MCP服务 (mcp_server/app.js)..."
        node mcp_server/app.js &
        MCP_PID=$!
        
        log_success "所有服务已启动"
        log_info "主服务 PID: $MAIN_PID"
        log_info "认证服务 PID: $AUTH_PID"
        log_info "MCP服务 PID: $MCP_PID"
        
        # 等待用户中断
        log_info "按 Ctrl+C 停止所有服务"
        
        # 设置信号处理
        trap 'log_info "正在停止所有服务..."; kill $MAIN_PID $AUTH_PID $MCP_PID 2>/dev/null; exit 0' INT TERM
        
        # 等待所有后台进程
        wait
    fi
}

# 显示帮助信息
show_help() {
    echo "MCP Server 统一启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -c, --check    仅检查环境，不启动服务"
    echo "  -v, --verbose  显示详细信息"
    echo ""
    echo "服务说明:"
    echo "  - 主服务 (index.js): UDP通信和Web界面"
    echo "  - 认证服务 (auth_server/app.js): 设备认证管理"
    echo "  - MCP服务 (mcp_server/app.js): LLM工具调用处理"
    echo ""
    echo "默认端口:"
    echo "  - 主服务: 3000"
    echo "  - 认证服务: 3001"
    echo "  - NATS: 4222"
}

# 主函数
main() {
    local check_only=false
    local verbose=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--check)
                check_only=true
                shift
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    log_info "=== MCP Server 统一启动脚本 ==="
    
    # 环境检查
    check_nodejs
    check_dependencies
    check_files
    check_ports
    
    if [ "$check_only" = true ]; then
        log_success "环境检查完成，所有检查项通过"
        exit 0
    fi
    
    # 启动服务
    start_services
}

# 脚本入口
main "$@"
