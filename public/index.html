<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenWrt Board Controller</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <style>
        /* iOS-like UI Styling */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            background-color: #f2f2f7; /* iOS system background color */
            padding-top: 70px; /* 为固定顶部导航栏留出空间 */
        }
        .container {
            margin-top: 10px; /* 进一步减少顶部间距 */
            margin-bottom: 20px; /* 减小容器底部间距 */
        }
        .navbar-brand {
            font-weight: 600; /* Medium bold */
            font-size: 1.25rem;
            /* 移除 mx-auto 以实现左对齐 */
        }
        .navbar.bg-primary {
            background-color: #007aff !important; /* iOS system blue */
        }
        .card {
            margin-bottom: 4px; /* 极限减小卡片之间的垂直间距 */
            border-radius: 12px; /* 更大的圆角 */
            box-shadow: 0 0 1px rgba(0, 0, 0, 0.01); /* 几乎不可见的极致柔和阴影 */
            border: none; /* 移除边框 */
        }
        .card-body {
            padding: 0.75rem 1rem; /* 调整卡片体的内边距，更紧凑 */
        }
        .card-body .mb-3 {
            margin-bottom: 0.75rem !important; /* 确保卡片体内表单元素的垂直间距紧凑 */
        }
        .card-header {
            background-color: #ffffff; /* 白色背景 */
            color: #333; /* 深色文字 */
            font-weight: 600;
            border-bottom: 1px solid #f0f0f0; /* 细分隔线 */
            border-radius: 12px 12px 0 0; /* 配合卡片圆角 */
            padding: 0.5rem 1rem; /* 调整卡片头部内边距，使其更薄 */
        }
        .form-label {
            font-weight: 500; /* 调整标签字体权重 */
            color: #4a4a4f; /* 更柔和的标签颜色 */
        }
        .form-control {
            border-radius: 8px; /* 输入框圆角 */
            border: 1px solid #e0e0e5; /* 更浅的边框颜色，使其更融入背景 */
            padding: 0.6rem 0.8rem;
            transition: all 0.2s ease-in-out; /* 添加过渡动画 */
        }
        .form-control:focus {
            border-color: transparent; /* 焦点时边框透明 */
            box-shadow: 0 0 0 0.15rem rgba(0, 122, 255, 0.15); /* 更柔和的焦点光晕 */
        }
        .form-text.text-muted {
            font-size: 0.85em; /* 调整提示文本字体大小 */
            margin-top: 0.25rem !important; /* 调整提示文本上间距 */
            color: #8e8e93 !important; /* iOS系统灰色 */
        }
        .btn {
            border-radius: 8px; /* 按钮圆角 */
            font-weight: 500;
            padding: 0.6rem 1rem;
            transition: all 0.2s ease-in-out; /* 添加过渡动画 */
        }
        .btn-primary {
            background-color: #007aff; /* iOS系统蓝色 */
            border-color: #007aff;
        }
        .btn-primary:hover {
            background-color: #006ee6; /* 稍暗的蓝色 */
            border-color: #006ee6;
        }
        .btn-info {
            background-color: #e5e5ea; /* 浅灰色背景，模拟iOS次要按钮 */
            border-color: #e5e5ea;
            color: #007aff; /* 蓝色文字 */
        }
        .btn-info:hover {
            background-color: #d1d1d6; /* 稍暗的灰色 */
            border-color: #d1d1d6;
        }
        .btn-success {
            background-color: #34c759; /* iOS系统绿色 */
            border-color: #34c759;
        }
        .btn-success:hover {
            background-color: #2da44e;
            border-color: #2da44e;
        }
        .response-box {
            /* 移除了背景色和边框，由alert类接管 */
            padding: 8px 10px; /* 调整填充，使其更紧凑 */
            border-radius: 8px; /* 配合整体圆角 */
            margin-top: 6px; /* 进一步减小响应框上间距 */
            white-space: pre-wrap;
            font-family: 'SFMono-Regular', Consolas, "Liberation Mono", Menlo, Courier, monospace; /* 更专业的等宽字体 */
            font-size: 0.85em;
            max-height: 200px;
            overflow-y: auto;
        }
        .alert-success {
            background-color: #f0f0f5; /* 非常浅的背景色 */
            color: #007aff; /* iOS蓝色文字 */
            border-color: #e0e0e5;
        }
        .alert-danger {
            background-color: #f0f0f5; /* 非常浅的背景色 */
            color: #ff3b30; /* iOS红色文字 */
            border-color: #e0e0e5;
        }
        .form-check-input:checked {
            background-color: #007aff;
            border-color: #007aff;
        }
        .form-check-input:focus {
            box-shadow: 0 0 0 0.25rem rgba(0, 122, 255, 0.25);
        }
        hr {
            border: none; /* 移除默认边框 */
            border-top: 1px solid #f0f0f0; /* 细分隔线 */
            margin: 0.5rem 0; /* 调整垂直间距，使其更紧凑 */
        }

        /* Custom toggle switch for iOS style - Placeholder */
        /* .form-switch .form-check-input { ... } */

        /* Connection Status Indicator Styles */
        .connection-status-circle {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #ced4da; /* Default grey */
            vertical-align: middle;
        }

        .connection-status-circle.disconnected {
            background-color: #ff3b30; /* iOS Red */
        }

        .connection-status-circle.connecting {
            background-color: #ffcc00; /* iOS Yellow/Orange */
        }

        .connection-status-circle.connected {
            background-color: #34c759; /* iOS Green */
        }

    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">设备升级管理软件</a>
        </div>
    </nav>

    <div class="container">
        <h1 class="mb-4 text-center text-primary">设备升级管理软件</h1>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">升级目标板</div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="deviceSelect" class="form-label">设备:</label>
                            <select class="form-select" id="deviceSelect" onchange="toggleCustomInput()">
                                <option value="***********77:3345">***********77:3345</option>
                                <option value="***********78:3347">***********78:3347</option>
                                <option value="***********79:3349">***********79:3349</option>
                                <option value="***********80:3350">***********80:3350</option>
                                <option value="15.50.179.66:3345">15.50.179.66:3345</option>
                                <option value="192.168.1.61:3341">192.168.1.61:3341</option>
                                <option value="************:3342">************:3342</option>
                                <option value="************:3343">************:3343</option>
                                <option value="************:3345">************:3345</option>
                                <option value="custom">自定义</option>
                            </select>
                        </div>
                        <div id="customDeviceInput" class="mb-3 d-none">
                            <label for="customIp" class="form-label">自定义 IP:</label>
                            <input type="text" class="form-control mb-2" id="customIp" placeholder="例如: ***********">
                            <label for="customPort" class="form-label">自定义端口:</label>
                            <input type="number" class="form-control" id="customPort" placeholder="例如: 3345">
                        </div>
                        <button type="button" onclick="connectSsh()" class="btn btn-primary w-100" id="connectSshBtn">
                            <span class="spinner-border spinner-border-sm me-2 d-none" role="status" aria-hidden="true"></span>
                            连接设备
                        </button>
                        <div class="mt-2 text-center">
                            <span id="connectionStatusIndicator" class="connection-status-circle disconnected"></span>
                            <span id="connectionStatusText" class="ms-2 text-muted">未连接</span>
                        </div>
                        <div class="response-box d-none" id="sshConnectResponse"></div>
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header">切换工作模式</div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" id="modeApp" name="configMode" value="app" checked onchange="toggleModeInputs()">
                                <label class="form-check-label" for="modeApp">App 模式</label>
                            </div>
                            <div id="appModeInputs" class="mb-3">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="appReportIp" class="form-label">App 模式上报 IP:</label>
                                        <input type="text" class="form-control mb-2" id="appReportIp" value="***********77" placeholder="例如: ***********">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="appPort" class="form-label">App 模式上报端口:</label>
                                        <input type="number" class="form-control" id="appPort" value="3345" placeholder="例如: 8080">
                                    </div>
                                </div>
                            </div>

                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" id="modeWeb" name="configMode" value="web" onchange="toggleModeInputs()">
                                <label class="form-check-label" for="modeWeb">Web 模式</label>
                            </div>
                            <div id="webModeInputs" class="mb-3 hidden">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="webReportIp" class="form-label">Web 模式上报 IP:</label>
                                        <input type="text" class="form-control mb-2" id="webReportIp" value="***********77" placeholder="例如: ***********">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="webPort" class="form-label">Web 模式上报端口:</label>
                                        <input type="number" class="form-control" id="webPort" value="8080" placeholder="例如: 8080">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="button" onclick="modifyConfigFile()" class="btn btn-primary w-100" id="modifyConfigFileBtn">
                            <span class="spinner-border spinner-border-sm me-2 d-none" role="status" aria-hidden="true"></span>
                            切换模式
                        </button>
                        <div class="response-box d-none" id="modifyConfigResponse"></div>
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header">设备操作</div>
                    <div class="card-body">
                        <button type="button" onclick="rebootStation()" class="btn btn-danger w-100" id="rebootBtn">
                            <span class="spinner-border spinner-border-sm me-2 d-none" role="status" aria-hidden="true"></span>
                            重启基站
                        </button>
                        <div class="response-box d-none" id="rebootResponse"></div>
                    </div>
                </div>
                <div class="card mt-2">
                    <div class="card-header">升级</div>
                    <div class="card-body">
                        <p>点击下方按钮，将自动完成升级操作。</p>
                        <button type="button" onclick="autoUpgrade()" class="btn btn-success w-100" id="autoUpgradeBtn">
                            <span class="spinner-border spinner-border-sm me-2 d-none" role="status" aria-hidden="true"></span>
                            一键升级
                        </button>
                        <div class="response-box d-none" id="upgradeResponse"></div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">升级验证：UDP Server 配置与通信</div>
                    <div class="card-body">
                        <form id="udpConfigForm">
                            <fieldset class="mb-4">
                                <legend class="h6 mb-3">本地监听配置</legend>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="udpListenIp" class="form-label">本地监听 IP:</label>
                                        <input type="text" class="form-control" id="udpListenIp" value="************" placeholder="例如: 0.0.0.0">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="udpListenPort" class="form-label">本地监听端口:</label>
                                        <input type="number" class="form-control" id="udpListenPort" value="3345" placeholder="例如: 12345">
                                    </div>
                                </div>
                            </fieldset>

                            <fieldset class="mb-4">
                                <legend class="h6 mb-3">基站通信配置</legend>
                                <div class="mb-2">
                                    <label class="form-label">快速选择预设设备：</label>
                                    <div id="presetBoards" class="d-flex flex-wrap gap-2 mb-2">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addBoardRow('***********77','3345')">***********77:3345</button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addBoardRow('***********78','3347')">***********78:3347</button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addBoardRow('***********79','3349')">***********79:3349</button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addBoardRow('***********80','3350')">***********80:3350</button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addBoardRow('15.50.179.66','3345')">15.50.179.66:3345</button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addBoardRow('192.168.1.61','3341')">192.168.1.61:3341</button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addBoardRow('************','3342')">************:3342</button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addBoardRow('************','3343')">************:3343</button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addBoardRow('************','3345')">************:3345</button>
                                    </div>
                                </div>
                                <div id="boardsContainer">
                                    <!-- 动态设备输入行插入点 -->
                                </div>
                                <button type="button" class="btn btn-outline-primary btn-sm mt-2" onclick="addBoardRow()">添加设备</button>
                                <small class="form-text text-muted mt-2 d-block">请填写基站设备IP和端口，端口默认为3345，需与基站实际配置一致。</small>
                            </fieldset>

                            <button type="button" onclick="startUdpServer()" class="btn btn-primary w-100 mb-3" id="startUdpServerBtn">
                                <span class="spinner-border spinner-border-sm me-2 d-none" role="status" aria-hidden="true"></span>
                                启动 UDP Server
                            </button>
                            <div id="udpStartResponse" class="response-box d-none"></div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    <script>
        // Helper function to show loading spinner on a button
        function showLoading(buttonId) {
            const button = document.getElementById(buttonId);
            const spinner = button.querySelector('.spinner-border');
            button.disabled = true;
            spinner.classList.remove('d-none');
        }

        // Helper function to hide loading spinner on a button
        function hideLoading(buttonId) {
            const button = document.getElementById(buttonId);
            const spinner = button.querySelector('.spinner-border');
            button.disabled = false;
            spinner.classList.add('d-none');
        }

        // Helper function to display response in a designated div
        function displayResponse(responseDivId, message, isError = false) {
            const responseDiv = document.getElementById(responseDivId);
            responseDiv.textContent = JSON.stringify(message, null, 2);
            responseDiv.classList.remove('d-none');
            responseDiv.classList.remove('alert-success', 'alert-danger');
            if (isError) {
                responseDiv.classList.add('alert', 'alert-danger');
            } else {
                responseDiv.classList.add('alert', 'alert-success');
            }
        }

        async function postData(url, data) {
            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data),
                });
                const result = await response.json();
                if (!response.ok) {
                    // If response is not OK (e.g., 4xx or 5xx status)
                    throw new Error(result.message || `HTTP error! status: ${response.status}`);
                }
                return result;
            } catch (error) {
                console.error('Error:', error);
                throw error; // Re-throw to be caught by the calling async function
            }
        }

        async function connectSsh() {
            const selectedValue = document.getElementById('deviceSelect').value;
            let host, port;

            if (selectedValue === 'custom') {
                host = document.getElementById('customIp').value;
                port = document.getElementById('customPort').value;

                if (!host || !port) {
                    displayResponse('sshConnectResponse', { message: '自定义IP和端口不能为空' }, true);
                    return;
                }
            } else {
                [host, port] = selectedValue.split(':');
            }

            const responseDiv = document.getElementById('sshConnectResponse');
            const statusIndicator = document.getElementById('connectionStatusIndicator');
            const statusText = document.getElementById('connectionStatusText');

            // Set status to connecting
            statusIndicator.classList.remove('disconnected', 'connected');
            statusIndicator.classList.add('connecting');
            statusText.textContent = '连接中...';
            responseDiv.classList.add('d-none'); // Hide previous response

            try {
                showLoading('connectSshBtn');
                const result = await postData('/api/ssh/connect', { host, port });
                
                // On successful connection
                statusIndicator.classList.remove('connecting', 'disconnected');
                statusIndicator.classList.add('connected');
                statusText.textContent = `已连接到 ${host}:${port}`;

                // Update frontend fields with data from backend
                if (result.appPort) {
                    document.getElementById('appPort').value = result.appPort;
                }
                if (result.webPort) {
                    document.getElementById('webPort').value = result.webPort;
                }
                if (result.appIp) {
                    document.getElementById('appReportIp').value = result.appIp;
                }
                if (result.webIp) {
                    document.getElementById('webReportIp').value = result.webIp;
                }

                // Set the current mode radio button
                if (result.currentMode === 'app') {
                    document.getElementById('modeApp').checked = true;
                } else if (result.currentMode === 'web') {
                    document.getElementById('modeWeb').checked = true;
                }
                // Trigger mode input visibility update
                toggleModeInputs();

                displayResponse('sshConnectResponse', result, false);
            } catch (error) {
                // On connection failure
                statusIndicator.classList.remove('connecting', 'connected');
                statusIndicator.classList.add('disconnected');
                statusText.textContent = '未连接';
            } finally {
                hideLoading('connectSshBtn');
            }
        }

        async function rebootStation() {
            const selectedValue = document.getElementById('deviceSelect').value;
            let targetIp;

            if (selectedValue === 'custom') {
                targetIp = document.getElementById('customIp').value;
            } else {
                [targetIp] = selectedValue.split(':');
            }

            if (!targetIp) {
                displayResponse('rebootResponse', { message: 'Could not determine target IP for reboot.' }, true);
                return;
            }

            const responseDiv = document.getElementById('rebootResponse');
            try {
                showLoading('rebootBtn');
                const result = await postData('/api/reboot-station', { targetIp });
                displayResponse('rebootResponse', result, false);
                // Also update connection status to disconnected as the board is rebooting
                document.getElementById('connectionStatusIndicator').classList.remove('connected', 'connecting');
                document.getElementById('connectionStatusIndicator').classList.add('disconnected');
                document.getElementById('connectionStatusText').textContent = 'Rebooting... Please reconnect shortly.';
            } catch (error) {
                displayResponse('rebootResponse', { message: `Reboot failed: ${error.message}` }, true);
            } finally {
                hideLoading('rebootBtn');
            }
        }

        async function autoUpgrade() {
            const responseDiv = document.getElementById('upgradeResponse');
            try {
                showLoading('autoUpgradeBtn');
                const result = await postData('/api/upgrade', {});
                displayResponse('upgradeResponse', result, false);
            } catch (error) {
                displayResponse('upgradeResponse', { message: `升级失败: ${error.message}` }, true);
            } finally {
                hideLoading('autoUpgradeBtn');
            }
        }

        async function modifyConfigFile() {
            const mode = document.querySelector('input[name="configMode"]:checked').value;
            let reportIp = null;
            let port = null;

            if (mode === 'web') {
                reportIp = document.getElementById('webReportIp').value;
                port = parseInt(document.getElementById('webPort').value, 10);
                if (!reportIp || isNaN(port)) {
                    displayResponse('modifyConfigResponse', { message: 'Web模式的上报IP和端口不能为空且端口必须是数字' }, true);
                    return;
                }
            } else if (mode === 'app') {
                reportIp = document.getElementById('appReportIp').value;
                port = parseInt(document.getElementById('appPort').value, 10);
                if (!reportIp || isNaN(port)) {
                    displayResponse('modifyConfigResponse', { message: 'App模式的上报IP和端口不能为空且端口必须是数字' }, true);
                    return;
                }
            }
            const responseDiv = document.getElementById('modifyConfigResponse');
            try {
                showLoading('modifyConfigFileBtn');
                const result = await postData('/api/board/modifyConfigFile', { mode, reportIp, port });
                displayResponse('modifyConfigResponse', result, false);
            } catch (error) {
                displayResponse('modifyConfigResponse', { message: `切换模式失败: ${error.message}` }, true);
            } finally {
                hideLoading('modifyConfigFileBtn');
            }
        }

        function createBoardRow(ip = '', port = '') {
            const row = document.createElement('div');
            row.className = 'row g-3 align-items-center mb-2 board-row';
            row.innerHTML = `
                <div class="col-md-5">
                    <input type="text" class="form-control board-ip" placeholder="IP，如: ***********01" value="${ip}">
                </div>
                <div class="col-md-5">
                    <input type="number" class="form-control board-port" placeholder="端口，如: 3345" value="${port}">
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeBoardRow(this)">删除</button>
                </div>
            `;
            return row;
        }

        function addBoardRow(ip = '', port = '') {
            const container = document.getElementById('boardsContainer');
            container.appendChild(createBoardRow(ip, port));
        }

        function removeBoardRow(btn) {
            btn.closest('.board-row').remove();
        }

        // 页面加载时默认添加一行
        window.addEventListener('DOMContentLoaded', () => {
            if (document.getElementById('boardsContainer').children.length === 0) {
                addBoardRow();
            }
        });

        function validateIp(ip) {
            // 简单IP校验
            return /^((25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(\.|$)){4}$/.test(ip);
        }

        function validatePort(port) {
            return Number.isInteger(port) && port >= 1 && port <= 65535;
        }

        async function startUdpServer() {
            const listenIp = document.getElementById('udpListenIp').value;
            const listenPort = parseInt(document.getElementById('udpListenPort').value, 10);
            const boards = [];
            let valid = true;
            let errorMsg = '';
            const ipSet = new Set();
            const portSet = new Set();
            document.querySelectorAll('#boardsContainer .board-row').forEach((row, idx) => {
                const ip = row.querySelector('.board-ip').value.trim();
                const port = parseInt(row.querySelector('.board-port').value, 10);
                if (!validateIp(ip)) {
                    valid = false;
                    errorMsg = `第${idx+1}行IP格式错误: ${ip}`;
                }
                if (!validatePort(port)) {
                    valid = false;
                    errorMsg = `第${idx+1}行端口格式错误: ${port}`;
                }
                const key = ip + ':' + port;
                if (ipSet.has(ip) && portSet.has(port)) {
                    valid = false;
                    errorMsg = `第${idx+1}行IP和端口与其他设备重复: ${ip}:${port}`;
                }
                ipSet.add(ip);
                portSet.add(port);
                boards.push({ ip, port });
            });
            if (boards.length === 0) {
                displayResponse('udpStartResponse', { message: '请至少添加一个设备' }, true);
                return;
            }
            if (!valid) {
                displayResponse('udpStartResponse', { message: errorMsg }, true);
                return;
            }
            try {
                showLoading('startUdpServerBtn');
                const result = await postData('/api/udp/start', { listenIp, listenPort, boards });
                displayResponse('udpStartResponse', result, false);
            } catch (error) {
                displayResponse('udpStartResponse', { message: `启动UDP服务器失败: ${error.message}` }, true);
            } finally {
                hideLoading('startUdpServerBtn');
            }
        }

        // Initialize UI state on page load
        document.addEventListener('DOMContentLoaded', () => {
            toggleModeInputs();
            // Add IDs to existing buttons for loading spinners
            // These IDs are already set in the HTML, no need to re-assign here
            // document.getElementById('connectSshBtn').id = 'connectSshBtn';
            // document.getElementById('autoUpgradeBtn').id = 'autoUpgradeBtn';
            // document.getElementById('modifyConfigFileBtn').id = 'modifyConfigFileBtn';
        });

        function toggleModeInputs() {
            const appModeInputs = document.getElementById('appModeInputs');
            const webModeInputs = document.getElementById('webModeInputs');
            const modeApp = document.getElementById('modeApp');

            if (modeApp.checked) {
                appModeInputs.classList.remove('d-none');
                webModeInputs.classList.add('d-none');
            } else {
                appModeInputs.classList.add('d-none');
                webModeInputs.classList.remove('d-none');
            }
        }

        function toggleCustomInput() {
            const deviceSelect = document.getElementById('deviceSelect');
            const customDeviceInputDiv = document.getElementById('customDeviceInput');
            const customIpInput = document.getElementById('customIp');
            const customPortInput = document.getElementById('customPort');

            if (deviceSelect.value === 'custom') {
                customDeviceInputDiv.classList.remove('d-none');
                customIpInput.value = ''; // Clear for custom input
                customPortInput.value = ''; // Clear for custom input
            } else {
                const [host, port] = deviceSelect.value.split(':');
                customIpInput.value = host;
                customPortInput.value = port;
                customDeviceInputDiv.classList.remove('d-none'); // Show custom input for modification
            }
        }

        // SSE Client setup
        const eventSource = new EventSource('/events');

        eventSource.onmessage = function(event) {
            const data = JSON.parse(event.data);
            if (data.status === 'success') {
                displayResponse('udpStartResponse', { message: data.text }, false);
            } else if (data.status === 'failure') {
                displayResponse('udpStartResponse', { message: data.text }, true);
            }
        };

        eventSource.onerror = function(err) {
            console.error('EventSource failed:', err);
            // You might want to display a connection error to the user
            // displayResponse('udpStartResponse', { message: '与服务器的实时连接中断，请刷新页面。' }, true);
        };

    </script>
</body>
</html> 