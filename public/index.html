<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基站智能管理系统</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Modern Professional UI Styling */
        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --secondary-color: #64748b;
            --success-color: #059669;
            --success-hover: #047857;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --danger-hover: #b91c1c;
            --info-color: #0891b2;
            --light-bg: #f8fafc;
            --card-bg: #ffffff;
            --border-color: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--text-primary);
            line-height: 1.6;
        }
        /* Header Styles */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow-sm);
        }

        .header h1 {
            color: var(--text-primary);
            font-weight: 700;
            font-size: 1.75rem;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .header .subtitle {
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 400;
            margin-top: 0.25rem;
        }

        /* Main Container */
        .main-container {
            padding: 2rem 0;
            max-width: 1400px;
            margin: 0 auto;
        }
        /* Card Styles */
        .card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .card-header i {
            color: var(--primary-color);
            font-size: 1.1rem;
        }

        .card-body {
            padding: 1.5rem;
        }

        .card-title {
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        /* Form Styles */
        .form-label {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }

        .form-control, .form-select {
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            background-color: #fff;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.1);
            outline: none;
        }

        .form-text {
            font-size: 0.75rem;
            color: var(--text-muted);
            margin-top: 0.25rem;
        }

        .input-group {
            margin-bottom: 1rem;
        }

        .input-group-text {
            background-color: var(--light-bg);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        /* Button Styles */
        .btn {
            border-radius: var(--radius-md);
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            border: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            cursor: pointer;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            background: linear-gradient(135deg, var(--primary-hover) 0%, #1e40af 100%);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, var(--success-hover) 100%);
            color: white;
        }

        .btn-success:hover:not(:disabled) {
            background: linear-gradient(135deg, var(--success-hover) 0%, #065f46 100%);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-hover) 100%);
            color: white;
        }

        .btn-danger:hover:not(:disabled) {
            background: linear-gradient(135deg, var(--danger-hover) 0%, #991b1b 100%);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #b45309 100%);
            color: white;
        }

        .btn-warning:hover:not(:disabled) {
            background: linear-gradient(135deg, #b45309 0%, #92400e 100%);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-outline-secondary {
            background: transparent;
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
        }

        .btn-outline-secondary:hover:not(:disabled) {
            background: var(--light-bg);
            border-color: var(--secondary-color);
            color: var(--text-primary);
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.75rem;
        }
        /* Alert and Response Styles */
        .response-box {
            border-radius: var(--radius-md);
            padding: 1rem;
            margin-top: 1rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.8rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            line-height: 1.4;
        }

        .alert-success {
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            border: 1px solid #a7f3d0;
            color: var(--success-color);
        }

        .alert-danger {
            background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
            border: 1px solid #fca5a5;
            color: var(--danger-color);
        }

        .alert-warning {
            background: linear-gradient(135deg, #fffbeb 0%, #fed7aa 100%);
            border: 1px solid #fdba74;
            color: var(--warning-color);
        }

        /* Status Indicators */
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            font-weight: 500;
        }

        .status-connected {
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            color: var(--success-color);
            border: 1px solid #a7f3d0;
        }

        .status-disconnected {
            background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
            color: var(--danger-color);
            border: 1px solid #fca5a5;
        }

        .status-connecting {
            background: linear-gradient(135deg, #fffbeb 0%, #fed7aa 100%);
            color: var(--warning-color);
            border: 1px solid #fdba74;
        }

        /* Power Control Styles */
        .power-control-section {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            margin-top: 1rem;
        }

        .power-slider {
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: #e2e8f0;
            outline: none;
            -webkit-appearance: none;
            margin: 1rem 0;
        }

        .power-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
            box-shadow: var(--shadow-md);
        }

        .power-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
            border: none;
            box-shadow: var(--shadow-md);
        }

        .power-value-display {
            background: var(--primary-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: var(--radius-md);
            font-weight: 600;
            text-align: center;
            min-width: 80px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
            }

            .card-body {
                padding: 1rem;
            }

            .btn {
                padding: 0.625rem 1.25rem;
                font-size: 0.8rem;
            }
        }

        /* Loading Animation */
        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }

        /* Utility Classes */
        .text-gradient {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--info-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col">
                    <h1>
                        <i class="fas fa-broadcast-tower"></i>
                        基站智能管理系统
                    </h1>
                    <div class="subtitle">专业的基站设备管理与控制平台</div>
                </div>
                <div class="col-auto">
                    <div class="status-indicator" id="systemStatus">
                        <i class="fas fa-circle text-success"></i>
                        系统运行正常
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="container-fluid main-container">

        <div class="row g-4">
            <!-- Device Connection Section -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-plug"></i>
                        设备连接管理
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="deviceSelect" class="form-label">
                                <i class="fas fa-server me-2"></i>选择目标设备
                            </label>
                            <select class="form-select" id="deviceSelect" onchange="toggleCustomInput()">
                                <option value="***********77:3345">***********77:3345</option>
                                <option value="***********78:3347">***********78:3347</option>
                                <option value="***********79:3349">***********79:3349</option>
                                <option value="***********80:3350">***********80:3350</option>
                                <option value="15.50.179.66:3345">15.50.179.66:3345</option>
                                <option value="192.168.1.61:3341">192.168.1.61:3341</option>
                                <option value="192.168.1.62:3342">192.168.1.62:3342</option>
                                <option value="************:3343">************:3343</option>
                                <option value="************:3345">************:3345</option>
                                <option value="custom">自定义设备</option>
                            </select>
                        </div>

                        <div id="customDeviceInput" class="mb-3 d-none">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="customIp" class="form-label">设备IP地址</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-network-wired"></i></span>
                                        <input type="text" class="form-control" id="customIp" placeholder="***********">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="customPort" class="form-label">端口号</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-hashtag"></i></span>
                                        <input type="number" class="form-control" id="customPort" placeholder="3345">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button type="button" onclick="connectSsh()" class="btn btn-primary w-100 mb-3" id="connectSshBtn">
                            <span class="spinner-border spinner-border-sm me-2 d-none" role="status" aria-hidden="true"></span>
                            <i class="fas fa-link me-2"></i>
                            连接设备
                        </button>

                        <div class="text-center mb-3">
                            <div class="status-indicator status-disconnected" id="connectionStatus">
                                <i class="fas fa-circle"></i>
                                <span id="connectionStatusText">未连接</span>
                            </div>
                        </div>

                        <div class="response-box d-none" id="sshConnectResponse"></div>
                    </div>
                </div>
                <!-- Mode Configuration Section -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-exchange-alt"></i>
                        工作模式配置
                    </div>
                    <div class="card-body">
                        <div class="mb-4">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="form-check p-3 border rounded">
                                        <input class="form-check-input" type="radio" id="modeApp" name="configMode" value="app" checked onchange="toggleModeInputs()">
                                        <label class="form-check-label fw-bold" for="modeApp">
                                            <i class="fas fa-mobile-alt me-2"></i>App 模式
                                        </label>
                                        <div class="text-muted small mt-1">移动应用程序模式</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check p-3 border rounded">
                                        <input class="form-check-input" type="radio" id="modeWeb" name="configMode" value="web" onchange="toggleModeInputs()">
                                        <label class="form-check-label fw-bold" for="modeWeb">
                                            <i class="fas fa-globe me-2"></i>Web 模式
                                        </label>
                                        <div class="text-muted small mt-1">网页浏览器模式</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="appModeInputs" class="mb-3">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-mobile-alt me-2"></i>App 模式配置
                            </h6>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="appReportIp" class="form-label">上报服务器IP</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-server"></i></span>
                                        <input type="text" class="form-control" id="appReportIp" value="***********77" placeholder="***********">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="appPort" class="form-label">上报端口</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-hashtag"></i></span>
                                        <input type="number" class="form-control" id="appPort" value="3345" placeholder="8080">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="webModeInputs" class="mb-3 d-none">
                            <h6 class="text-info mb-3">
                                <i class="fas fa-globe me-2"></i>Web 模式配置
                            </h6>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="webReportIp" class="form-label">上报服务器IP</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-server"></i></span>
                                        <input type="text" class="form-control" id="webReportIp" value="***********77" placeholder="***********">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="webPort" class="form-label">上报端口</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-hashtag"></i></span>
                                        <input type="number" class="form-control" id="webPort" value="8080" placeholder="8080">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button type="button" onclick="modifyConfigFile()" class="btn btn-primary w-100" id="modifyConfigFileBtn">
                            <span class="spinner-border spinner-border-sm me-2 d-none" role="status" aria-hidden="true"></span>
                            <i class="fas fa-save me-2"></i>
                            应用模式配置
                        </button>
                        <div class="response-box d-none" id="modifyConfigResponse"></div>
                    </div>
                </div>
                <!-- Device Operations Section -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-cogs"></i>
                        设备操作控制
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <button type="button" onclick="rebootStation()" class="btn btn-danger w-100" id="rebootBtn">
                                    <span class="spinner-border spinner-border-sm me-2 d-none" role="status" aria-hidden="true"></span>
                                    <i class="fas fa-power-off me-2"></i>
                                    重启基站
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button type="button" onclick="showPowerControl()" class="btn btn-warning w-100" id="powerControlBtn">
                                    <i class="fas fa-signal me-2"></i>
                                    功率控制
                                </button>
                            </div>
                        </div>

                        <!-- Power Control Section -->
                        <div class="power-control-section d-none" id="powerControlSection">
                            <h6 class="mb-3">
                                <i class="fas fa-sliders-h me-2"></i>
                                基站功率衰减控制
                            </h6>

                            <div class="row align-items-center mb-3">
                                <div class="col-md-8">
                                    <label for="powerSlider" class="form-label">功率衰减值 (dB)</label>
                                    <input type="range" class="power-slider" id="powerSlider"
                                           min="0" max="100" value="0" step="1"
                                           oninput="updatePowerValue(this.value)">
                                    <div class="d-flex justify-content-between text-muted small">
                                        <span>0 dB</span>
                                        <span>50 dB</span>
                                        <span>100 dB</span>
                                    </div>
                                </div>
                                <div class="col-md-4 text-center">
                                    <div class="power-value-display" id="powerValueDisplay">0 dB</div>
                                </div>
                            </div>

                            <div class="row g-2 mb-3">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <span class="input-group-text">精确值</span>
                                        <input type="number" class="form-control" id="powerInput"
                                               min="0" max="100" value="0" step="0.1"
                                               onchange="updatePowerSlider(this.value)">
                                        <span class="input-group-text">dB</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <select class="form-select" id="powerPreset" onchange="applyPowerPreset(this.value)">
                                        <option value="">选择预设值</option>
                                        <option value="0">最大功率 (0 dB)</option>
                                        <option value="10">高功率 (10 dB)</option>
                                        <option value="20">中功率 (20 dB)</option>
                                        <option value="30">低功率 (30 dB)</option>
                                        <option value="50">最低功率 (50 dB)</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row g-2">
                                <div class="col-md-6">
                                    <button type="button" onclick="setPowerAttenuation()" class="btn btn-success w-100" id="setPowerBtn">
                                        <span class="spinner-border spinner-border-sm me-2 d-none" role="status" aria-hidden="true"></span>
                                        <i class="fas fa-check me-2"></i>
                                        应用设置
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button type="button" onclick="hidePowerControl()" class="btn btn-outline-secondary w-100">
                                        <i class="fas fa-times me-2"></i>
                                        取消
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="response-box d-none" id="deviceOperationResponse"></div>
                    </div>
                </div>
                <!-- System Upgrade Section -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-upload"></i>
                        系统升级管理
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info d-flex align-items-center mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <div>
                                <strong>升级提示：</strong>升级过程将自动完成，请确保设备连接稳定。
                            </div>
                        </div>

                        <button type="button" onclick="autoUpgrade()" class="btn btn-success w-100" id="autoUpgradeBtn">
                            <span class="spinner-border spinner-border-sm me-2 d-none" role="status" aria-hidden="true"></span>
                            <i class="fas fa-rocket me-2"></i>
                            开始一键升级
                        </button>
                        <div class="response-box d-none" id="upgradeResponse"></div>
                    </div>
                </div>
            </div>

            <!-- UDP Server Configuration Section -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-network-wired"></i>
                        UDP 服务器配置
                    </div>
                    <div class="card-body">
                        <form id="udpConfigForm">
                            <div class="mb-4">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-cog me-2"></i>本地监听配置
                                </h6>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="udpListenIp" class="form-label">监听IP地址</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-network-wired"></i></span>
                                            <input type="text" class="form-control" id="udpListenIp" value="************" placeholder="0.0.0.0">
                                        </div>
                                        <div class="form-text">使用 0.0.0.0 监听所有网络接口</div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="udpListenPort" class="form-label">监听端口</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-hashtag"></i></span>
                                            <input type="number" class="form-control" id="udpListenPort" value="3345" placeholder="12345">
                                        </div>
                                        <div class="form-text">建议使用 1024-65535 范围内的端口</div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <h6 class="text-success mb-3">
                                    <i class="fas fa-broadcast-tower me-2"></i>基站设备配置
                                </h6>

                                <div class="mb-3">
                                    <label class="form-label">快速添加预设设备</label>
                                    <div id="presetBoards" class="d-flex flex-wrap gap-2 mb-3">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addBoardRow('***********77','3345')">
                                            <i class="fas fa-plus me-1"></i>177:3345
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addBoardRow('***********78','3347')">
                                            <i class="fas fa-plus me-1"></i>178:3347
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addBoardRow('***********79','3349')">
                                            <i class="fas fa-plus me-1"></i>179:3349
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addBoardRow('***********80','3350')">
                                            <i class="fas fa-plus me-1"></i>180:3350
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addBoardRow('15.50.179.66','3345')">
                                            <i class="fas fa-plus me-1"></i>66:3345
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addBoardRow('192.168.1.61','3341')">
                                            <i class="fas fa-plus me-1"></i>61:3341
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addBoardRow('192.168.1.62','3342')">
                                            <i class="fas fa-plus me-1"></i>62:3342
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addBoardRow('************','3343')">
                                            <i class="fas fa-plus me-1"></i>63:3343
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addBoardRow('************','3345')">
                                            <i class="fas fa-plus me-1"></i>53:3345
                                        </button>
                                    </div>
                                </div>

                                <div class="border rounded p-3 mb-3" style="background-color: #f8f9fa;">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">设备列表</h6>
                                        <button type="button" class="btn btn-primary btn-sm" onclick="addBoardRow()">
                                            <i class="fas fa-plus me-1"></i>添加设备
                                        </button>
                                    </div>
                                    <div id="boardsContainer">
                                        <!-- 动态设备输入行插入点 -->
                                    </div>
                                </div>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>配置说明：</strong>请填写基站设备的IP地址和端口号，端口默认为3345，需与基站实际配置保持一致。
                                </div>
                            </div>

                            <button type="button" onclick="startUdpServer()" class="btn btn-success w-100 mb-3" id="startUdpServerBtn">
                                <span class="spinner-border spinner-border-sm me-2 d-none" role="status" aria-hidden="true"></span>
                                <i class="fas fa-play me-2"></i>
                                启动 UDP 服务器
                            </button>
                            <div id="udpStartResponse" class="response-box d-none"></div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="text-center py-4 mt-5" style="background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px);">
        <div class="container">
            <p class="mb-0 text-white">
                <i class="fas fa-broadcast-tower me-2"></i>
                基站智能管理系统 &copy; 2024 - 专业的基站设备管理解决方案
            </p>
        </div>
    </footer>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    <script>
        // Helper function to show loading spinner on a button
        function showLoading(buttonId) {
            const button = document.getElementById(buttonId);
            const spinner = button.querySelector('.spinner-border');
            button.disabled = true;
            spinner.classList.remove('d-none');
        }

        // Helper function to hide loading spinner on a button
        function hideLoading(buttonId) {
            const button = document.getElementById(buttonId);
            const spinner = button.querySelector('.spinner-border');
            button.disabled = false;
            spinner.classList.add('d-none');
        }

        // Helper function to display response in a designated div
        function displayResponse(responseDivId, message, isError = false) {
            const responseDiv = document.getElementById(responseDivId);
            responseDiv.textContent = JSON.stringify(message, null, 2);
            responseDiv.classList.remove('d-none');
            responseDiv.classList.remove('alert-success', 'alert-danger', 'alert-warning');
            if (isError) {
                responseDiv.classList.add('alert', 'alert-danger');
            } else {
                responseDiv.classList.add('alert', 'alert-success');
            }
        }

        // Power Control Functions
        function showPowerControl() {
            const powerSection = document.getElementById('powerControlSection');
            powerSection.classList.remove('d-none');
            // Scroll to power control section
            powerSection.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }

        function hidePowerControl() {
            const powerSection = document.getElementById('powerControlSection');
            powerSection.classList.add('d-none');
        }

        function updatePowerValue(value) {
            const display = document.getElementById('powerValueDisplay');
            const input = document.getElementById('powerInput');
            display.textContent = value + ' dB';
            input.value = value;
        }

        function updatePowerSlider(value) {
            const slider = document.getElementById('powerSlider');
            const display = document.getElementById('powerValueDisplay');
            slider.value = value;
            display.textContent = value + ' dB';
        }

        function applyPowerPreset(value) {
            if (value) {
                updatePowerSlider(value);
            }
        }

        async function setPowerAttenuation() {
            const selectedValue = document.getElementById('deviceSelect').value;
            let targetIp;

            if (selectedValue === 'custom') {
                targetIp = document.getElementById('customIp').value;
            } else {
                [targetIp] = selectedValue.split(':');
            }

            if (!targetIp) {
                displayResponse('deviceOperationResponse', { message: '无法确定目标设备IP地址' }, true);
                return;
            }

            const powerValue = parseFloat(document.getElementById('powerInput').value);

            if (isNaN(powerValue) || powerValue < 0 || powerValue > 100) {
                displayResponse('deviceOperationResponse', { message: '功率衰减值必须在0-100dB范围内' }, true);
                return;
            }

            try {
                showLoading('setPowerBtn');
                const result = await postData('/api/set-power-attenuation', {
                    targetIp: targetIp,
                    attenuationDb: powerValue
                });
                displayResponse('deviceOperationResponse', result, false);

                // Hide power control section after successful operation
                setTimeout(() => {
                    hidePowerControl();
                }, 2000);

            } catch (error) {
                displayResponse('deviceOperationResponse', { message: `功率设置失败: ${error.message}` }, true);
            } finally {
                hideLoading('setPowerBtn');
            }
        }

        async function postData(url, data) {
            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data),
                });
                const result = await response.json();
                if (!response.ok) {
                    // If response is not OK (e.g., 4xx or 5xx status)
                    throw new Error(result.message || `HTTP error! status: ${response.status}`);
                }
                return result;
            } catch (error) {
                console.error('Error:', error);
                throw error; // Re-throw to be caught by the calling async function
            }
        }

        async function connectSsh() {
            const selectedValue = document.getElementById('deviceSelect').value;
            let host, port;

            if (selectedValue === 'custom') {
                host = document.getElementById('customIp').value;
                port = document.getElementById('customPort').value;

                if (!host || !port) {
                    displayResponse('sshConnectResponse', { message: '自定义IP和端口不能为空' }, true);
                    return;
                }
            } else {
                [host, port] = selectedValue.split(':');
            }

            const responseDiv = document.getElementById('sshConnectResponse');
            const connectionStatus = document.getElementById('connectionStatus');
            const statusText = document.getElementById('connectionStatusText');

            // Set status to connecting
            connectionStatus.className = 'status-indicator status-connecting';
            connectionStatus.innerHTML = '<i class="fas fa-circle"></i><span id="connectionStatusText">连接中...</span>';
            responseDiv.classList.add('d-none'); // Hide previous response

            try {
                showLoading('connectSshBtn');
                const result = await postData('/api/ssh/connect', { host, port });
                
                // On successful connection
                connectionStatus.className = 'status-indicator status-connected';
                connectionStatus.innerHTML = `<i class="fas fa-circle"></i><span>已连接到 ${host}:${port}</span>`;

                // Update frontend fields with data from backend
                if (result.appPort) {
                    document.getElementById('appPort').value = result.appPort;
                }
                if (result.webPort) {
                    document.getElementById('webPort').value = result.webPort;
                }
                if (result.appIp) {
                    document.getElementById('appReportIp').value = result.appIp;
                }
                if (result.webIp) {
                    document.getElementById('webReportIp').value = result.webIp;
                }

                // Set the current mode radio button
                if (result.currentMode === 'app') {
                    document.getElementById('modeApp').checked = true;
                } else if (result.currentMode === 'web') {
                    document.getElementById('modeWeb').checked = true;
                }
                // Trigger mode input visibility update
                toggleModeInputs();

                displayResponse('sshConnectResponse', result, false);
            } catch (error) {
                // On connection failure
                connectionStatus.className = 'status-indicator status-disconnected';
                connectionStatus.innerHTML = '<i class="fas fa-circle"></i><span>连接失败</span>';
                displayResponse('sshConnectResponse', { message: `连接失败: ${error.message}` }, true);
            } finally {
                hideLoading('connectSshBtn');
            }
        }

        async function rebootStation() {
            const selectedValue = document.getElementById('deviceSelect').value;
            let targetIp;

            if (selectedValue === 'custom') {
                targetIp = document.getElementById('customIp').value;
            } else {
                [targetIp] = selectedValue.split(':');
            }

            if (!targetIp) {
                displayResponse('rebootResponse', { message: 'Could not determine target IP for reboot.' }, true);
                return;
            }

            const responseDiv = document.getElementById('rebootResponse');
            try {
                showLoading('rebootBtn');
                const result = await postData('/api/reboot-station', { targetIp });
                displayResponse('deviceOperationResponse', result, false);
                // Also update connection status to disconnected as the board is rebooting
                const connectionStatus = document.getElementById('connectionStatus');
                connectionStatus.className = 'status-indicator status-connecting';
                connectionStatus.innerHTML = '<i class="fas fa-circle"></i><span>设备重启中，请稍后重新连接...</span>';
            } catch (error) {
                displayResponse('deviceOperationResponse', { message: `重启失败: ${error.message}` }, true);
            } finally {
                hideLoading('rebootBtn');
            }
        }

        async function autoUpgrade() {
            const responseDiv = document.getElementById('upgradeResponse');
            try {
                showLoading('autoUpgradeBtn');
                const result = await postData('/api/upgrade', {});
                displayResponse('upgradeResponse', result, false);
            } catch (error) {
                displayResponse('upgradeResponse', { message: `升级失败: ${error.message}` }, true);
            } finally {
                hideLoading('autoUpgradeBtn');
            }
        }

        async function modifyConfigFile() {
            const mode = document.querySelector('input[name="configMode"]:checked').value;
            let reportIp = null;
            let port = null;

            if (mode === 'web') {
                reportIp = document.getElementById('webReportIp').value;
                port = parseInt(document.getElementById('webPort').value, 10);
                if (!reportIp || isNaN(port)) {
                    displayResponse('modifyConfigResponse', { message: 'Web模式的上报IP和端口不能为空且端口必须是数字' }, true);
                    return;
                }
            } else if (mode === 'app') {
                reportIp = document.getElementById('appReportIp').value;
                port = parseInt(document.getElementById('appPort').value, 10);
                if (!reportIp || isNaN(port)) {
                    displayResponse('modifyConfigResponse', { message: 'App模式的上报IP和端口不能为空且端口必须是数字' }, true);
                    return;
                }
            }
            const responseDiv = document.getElementById('modifyConfigResponse');
            try {
                showLoading('modifyConfigFileBtn');
                const result = await postData('/api/board/modifyConfigFile', { mode, reportIp, port });
                displayResponse('modifyConfigResponse', result, false);
            } catch (error) {
                displayResponse('modifyConfigResponse', { message: `切换模式失败: ${error.message}` }, true);
            } finally {
                hideLoading('modifyConfigFileBtn');
            }
        }

        function createBoardRow(ip = '', port = '3345') {
            const row = document.createElement('div');
            row.className = 'row g-2 align-items-center mb-3 board-row p-3 border rounded';
            row.style.backgroundColor = '#ffffff';
            row.innerHTML = `
                <div class="col-md-5">
                    <label class="form-label small text-muted mb-1">设备IP地址</label>
                    <div class="input-group input-group-sm">
                        <span class="input-group-text"><i class="fas fa-network-wired"></i></span>
                        <input type="text" class="form-control board-ip" placeholder="***********01" value="${ip}" required>
                    </div>
                </div>
                <div class="col-md-5">
                    <label class="form-label small text-muted mb-1">端口号</label>
                    <div class="input-group input-group-sm">
                        <span class="input-group-text"><i class="fas fa-hashtag"></i></span>
                        <input type="number" class="form-control board-port" placeholder="3345" value="${port}" min="1" max="65535" required>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label small text-muted mb-1">&nbsp;</label>
                    <button type="button" class="btn btn-outline-danger btn-sm w-100" onclick="removeBoardRow(this)">
                        <i class="fas fa-trash me-1"></i>删除
                    </button>
                </div>
            `;
            return row;
        }

        function addBoardRow(ip = '', port = '') {
            const container = document.getElementById('boardsContainer');
            container.appendChild(createBoardRow(ip, port));
        }

        function removeBoardRow(btn) {
            btn.closest('.board-row').remove();
        }

        // 页面加载时默认添加一行
        window.addEventListener('DOMContentLoaded', () => {
            if (document.getElementById('boardsContainer').children.length === 0) {
                addBoardRow();
            }
        });

        function validateIp(ip) {
            // 简单IP校验
            return /^((25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(\.|$)){4}$/.test(ip);
        }

        function validatePort(port) {
            return Number.isInteger(port) && port >= 1 && port <= 65535;
        }

        async function startUdpServer() {
            const listenIp = document.getElementById('udpListenIp').value;
            const listenPort = parseInt(document.getElementById('udpListenPort').value, 10);
            const boards = [];
            let valid = true;
            let errorMsg = '';
            const ipSet = new Set();
            const portSet = new Set();
            document.querySelectorAll('#boardsContainer .board-row').forEach((row, idx) => {
                const ip = row.querySelector('.board-ip').value.trim();
                const portValue = row.querySelector('.board-port').value.trim();

                // Skip empty rows
                if (!ip && !portValue) {
                    return;
                }

                const port = parseInt(portValue, 10);

                if (!ip) {
                    valid = false;
                    errorMsg = `第${idx+1}行IP地址不能为空`;
                    return;
                }

                if (!portValue) {
                    valid = false;
                    errorMsg = `第${idx+1}行端口不能为空`;
                    return;
                }

                if (!validateIp(ip)) {
                    valid = false;
                    errorMsg = `第${idx+1}行IP格式错误: ${ip}`;
                    return;
                }

                if (isNaN(port) || !validatePort(port)) {
                    valid = false;
                    errorMsg = `第${idx+1}行端口格式错误: ${portValue}`;
                    return;
                }

                const key = ip + ':' + port;
                if (ipSet.has(key)) {
                    valid = false;
                    errorMsg = `第${idx+1}行设备与其他设备重复: ${ip}:${port}`;
                    return;
                }

                ipSet.add(key);
                boards.push({ ip, port });
            });
            // Allow starting UDP server without boards for testing
            if (boards.length === 0) {
                console.log('Starting UDP server without any boards configured');
            }
            if (!valid) {
                displayResponse('udpStartResponse', { message: errorMsg }, true);
                return;
            }
            try {
                showLoading('startUdpServerBtn');
                const result = await postData('/api/udp/start', { listenIp, listenPort, boards });
                displayResponse('udpStartResponse', result, false);
            } catch (error) {
                displayResponse('udpStartResponse', { message: `启动UDP服务器失败: ${error.message}` }, true);
            } finally {
                hideLoading('startUdpServerBtn');
            }
        }

        // Initialize UI state on page load
        document.addEventListener('DOMContentLoaded', () => {
            toggleModeInputs();
            // Add IDs to existing buttons for loading spinners
            // These IDs are already set in the HTML, no need to re-assign here
            // document.getElementById('connectSshBtn').id = 'connectSshBtn';
            // document.getElementById('autoUpgradeBtn').id = 'autoUpgradeBtn';
            // document.getElementById('modifyConfigFileBtn').id = 'modifyConfigFileBtn';
        });

        function toggleModeInputs() {
            const appModeInputs = document.getElementById('appModeInputs');
            const webModeInputs = document.getElementById('webModeInputs');
            const modeApp = document.getElementById('modeApp');

            if (modeApp.checked) {
                appModeInputs.classList.remove('d-none');
                webModeInputs.classList.add('d-none');
            } else {
                appModeInputs.classList.add('d-none');
                webModeInputs.classList.remove('d-none');
            }
        }

        function toggleCustomInput() {
            const deviceSelect = document.getElementById('deviceSelect');
            const customDeviceInputDiv = document.getElementById('customDeviceInput');
            const customIpInput = document.getElementById('customIp');
            const customPortInput = document.getElementById('customPort');

            if (deviceSelect.value === 'custom') {
                customDeviceInputDiv.classList.remove('d-none');
                customIpInput.value = ''; // Clear for custom input
                customPortInput.value = ''; // Clear for custom input
            } else {
                const [host, port] = deviceSelect.value.split(':');
                customIpInput.value = host;
                customPortInput.value = port;
                customDeviceInputDiv.classList.remove('d-none'); // Show custom input for modification
            }
        }

        // SSE Client setup
        const eventSource = new EventSource('/events');

        eventSource.onmessage = function(event) {
            const data = JSON.parse(event.data);
            if (data.status === 'success') {
                displayResponse('udpStartResponse', { message: data.text }, false);
            } else if (data.status === 'failure') {
                displayResponse('udpStartResponse', { message: data.text }, true);
            }
        };

        eventSource.onerror = function(err) {
            console.error('EventSource failed:', err);
            // You might want to display a connection error to the user
            // displayResponse('udpStartResponse', { message: '与服务器的实时连接中断，请刷新页面。' }, true);
        };

    </script>
</body>
</html> 