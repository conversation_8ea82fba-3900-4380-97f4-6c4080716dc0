const MSG_TYPE_HEARTBEAT_IND = 0xF010; // 心跳指示
const MSG_TYPE_HEARTBEAT_ACK = 0xF011;   // 心跳应答
const MSG_TYPE_AUTH_SERIAL_QUERY = 0xF111; // 授权串号查询
const MSG_TYPE_AUTH_SERIAL_QUERY_ACK = 0xF112; // 授权串号查询应答
const MSG_TYPE_AUTH_KEY_CFG = 0xF113; // 授权密钥配置
const MSG_TYPE_AUTH_KEY_CFG_ACK = 0xF114; // 授权密钥配置应答
const MSG_TYPE_SECONDARY_PLMNS_SET = 0xF115; // 辅助PLMN配置
const MSG_TYPE_SECONDARY_PLMNS_SET_ACK = 0xF116; // 辅助PLMN配置应答
const MSG_TYPE_BASE_INFO_QUERY = 0xF117; // 查询基站信息
const MSG_TYPE_BASE_INFO_QUERY_ACK = 0xF118; // 查询基站信息应答

function buildHeartbeatAck(sn) {
    const buffer = Buffer.alloc(32); // 32字节消息头，包含SN号
    buffer.writeUInt32LE(0x5555AAAA, 0); // 帧头
    buffer.writeUInt16LE(MSG_TYPE_HEARTBEAT_ACK, 4);     // 消息ID (心跳ACK)
    buffer.writeUInt16LE(32, 6);         // 消息长度 (32字节)
    buffer.writeUInt16LE(0, 8);          // frame
    buffer.writeUInt16LE(1, 10);         // SubSysCode（TransId=1，最高位0）
    if (sn && sn.length === 20) {
        sn.copy(buffer, 12); // 将SN号复制到偏移量12处
    }
    return buffer;
}

/**
 * 构建 O_FL_LMT_TO_ENB_AUTH_SERIAL_QUERY 消息
 * 消息体：无
 * @param {Buffer} sn - 来自心跳报文的20字节SN号
 * @returns {Buffer} 构建好的查询消息Buffer
 */
function buildAuthSerialQuery(sn) {
    const buffer = Buffer.alloc(32); // 32字节消息头，无消息体
    buffer.writeUInt32LE(0x5555AAAA, 0); // 帧头
    buffer.writeUInt16LE(MSG_TYPE_AUTH_SERIAL_QUERY, 4); // 消息ID
    buffer.writeUInt16LE(32, 6);         // 消息长度 (32字节)
    buffer.writeUInt16LE(0, 8);          // frame
    buffer.writeUInt16LE(1, 10);         // SubSysCode（TransId=1，最高位0）
    if (sn && sn.length === 20) {
        sn.copy(buffer, 12); // 将SN号复制到偏移量12处
    }
    return buffer;
}

/**
 * 构建 O_FL_LMT_TO_ENB_AUTH_KEY_CFG 消息
 * @param {Buffer} sn - 来自心跳报文的20字节SN号
 * @param {string} authCode - 授权码，需要填充至64字节（ASCII字符串，null终止）
 * @returns {Buffer} 构建好的配置消息Buffer
 */
function buildAuthKeyCfg(sn, authCode) {
    const totalLength = 32 + 68; // 消息头32字节 + 消息体68字节 = 100字节
    const buffer = Buffer.alloc(totalLength); // Buffer.alloc 会自动用0填充
    buffer.writeUInt32LE(0x5555AAAA, 0); // 帧头
    buffer.writeUInt16LE(MSG_TYPE_AUTH_KEY_CFG, 4); // 消息ID
    buffer.writeUInt16LE(totalLength, 6); // 消息长度
    buffer.writeUInt16LE(0, 8);          // frame
    buffer.writeUInt16LE(1, 10);         // SubSysCode（TransId=1，最高位0）

    if (sn && sn.length === 20) {
        sn.copy(buffer, 12); // 将SN号复制到偏移量12处
    }

    // 授权码部分 (从偏移量32开始，共64字节)
    const authCodeBuffer = Buffer.from(authCode, 'ascii');
    // 确保写入的长度不超过64字节
    authCodeBuffer.copy(buffer, 32, 0, Math.min(authCodeBuffer.length, 64));
    // 剩余部分自动由 Buffer.alloc 填充的0作为null终止符

    return buffer;
}

/**
 * 构建 O_FL_LMT_TO_ENB_SECONDARY_PLMNS_SET 消息
 * @param {Buffer} sn - 来自心跳报文的20字节SN号
 * @param {Array<Object>} plmns - PLMN 对象的数组，每个对象包含 { mcc: string, mnc: string }
 * @returns {Buffer} 构建好的配置消息Buffer
 */
function buildSecondaryPlmnsSet(sn, plmns) {
    const plmnNum = Math.min(plmns.length, 10); // 最多支持10个PLMN
    const plmnsBodyLength = plmnNum * 8; // 每个PLMN 8字节
    const totalLength = 32 + 1 + plmnsBodyLength; // 消息头32字节 + PLMN数量1字节 + PLMNs列表

    const buffer = Buffer.alloc(totalLength);
    buffer.writeUInt32LE(0x5555AAAA, 0); // 帧头
    buffer.writeUInt16LE(MSG_TYPE_SECONDARY_PLMNS_SET, 4); // 消息ID
    buffer.writeUInt16LE(totalLength, 6); // 消息长度
    buffer.writeUInt16LE(0, 8);          // frame
    buffer.writeUInt16LE(1, 10);         // SubSysCode（TransId=1，最高位0）

    if (sn && sn.length === 20) {
        sn.copy(buffer, 12); // 将SN号复制到偏移量12处
    }

    buffer.writeUInt8(plmnNum, 32); // PLMN数量 (从偏移量32开始)

    let offset = 33; // PLMNs列表的起始偏移量

    for (let i = 0; i < plmnNum; i++) {
        const plmn = plmns[i];
        const plmnStr = `${plmn.mcc}${plmn.mnc}`;
        const plmnBuffer = Buffer.from(plmnStr, 'ascii');
        
        plmnBuffer.copy(buffer, offset, 0, Math.min(plmnBuffer.length, 7)); // 写入PLMN，最多7字节
        // 剩余的字节会自动用0填充（null终止）
        offset += 8; // 移动到下一个PLMN的起始位置
    }

    return buffer;
}

/**
 * 构建查询基站信息消息 (O_FL_LMT_TO_ENB_BASE_INFO_QUERY)
 * @param {Buffer} sn - 单板SN号 (20字节)
 * @returns {Buffer} 完整的消息Buffer
 */
function buildBaseInfoQuery(sn) {
    const totalLen = 32; // 消息头长度
    const buffer = Buffer.alloc(totalLen);

    // 帧头: 0x5555AAAA
    buffer.writeUInt32LE(0x5555AAAA, 0);
    // 消息类型: 0xF117
    buffer.writeUInt16LE(MSG_TYPE_BASE_INFO_QUERY, 4);
    // 消息长度: 消息头长度 (32字节)
    buffer.writeUInt16LE(totalLen, 6);
    // Frame: 0x0000
    buffer.writeUInt16LE(0x0000, 8);
    // SubSysCode: 0x0000
    buffer.writeUInt16LE(0x0000, 10);
    // SN (20字节)
    sn.copy(buffer, 12, 0, 20);

    return buffer;
}

module.exports = {
    buildHeartbeatAck,
    buildAuthSerialQuery,
    buildAuthKeyCfg,
    buildSecondaryPlmnsSet,
    buildBaseInfoQuery,
    MSG_TYPE_HEARTBEAT_IND,
    MSG_TYPE_HEARTBEAT_ACK,
    MSG_TYPE_AUTH_SERIAL_QUERY,
    MSG_TYPE_AUTH_SERIAL_QUERY_ACK,
    MSG_TYPE_AUTH_KEY_CFG,
    MSG_TYPE_AUTH_KEY_CFG_ACK,
    MSG_TYPE_SECONDARY_PLMNS_SET,
    MSG_TYPE_SECONDARY_PLMNS_SET_ACK,
    MSG_TYPE_BASE_INFO_QUERY,
    MSG_TYPE_BASE_INFO_QUERY_ACK,
}; 