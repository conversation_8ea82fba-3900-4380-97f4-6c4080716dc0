// test_mcp_client.js
const { connect, StringCodec } = require('nats');
const { v4: uuidv4 } = require('uuid');

const sc = StringCodec();

async function runTest() {
  let natsConnection;
  try {
    console.log('[Test Client] Connecting to NATS...');
    natsConnection = await connect({ servers: "nats://113.45.77.38:4222" });
    console.log('[Test Client] Connected.');

    // 1. 请求工具定义 (使用 request API)
    console.log('\n--- Step 1: Requesting LLM Tools ---');
    const toolsMsg = await natsConnection.request('mcp.get_llm_tools', '', { timeout: 5000 }); // 增加超时时间
    const tools = JSON.parse(sc.decode(toolsMsg.data));
    console.log('[Test Client] Received tools definition:');
    console.log(JSON.stringify(tools, null, 2));
    console.log('--- Step 1 PASSED ---\n');

    // 2. 模拟调用一个工具 (query_base_info)
    console.log('--- Step 2: Invoking a Tool (query_base_info) ---');
    const correlationId = uuidv4();

    const toolCallRequest = {
      tool_name: 'query_base_info',
      tool_args: { u32EnbBaseInfoType: 3 }, // 查询SN号
      correlation_id: correlationId,
      target_ip: '*************', // [新增] 指定目标基站IP
    };

    // [优化] 详细打印发送的请求
    console.log(`[Test Client] Sending Request:`);
    console.log(`  Tool Name: ${toolCallRequest.tool_name}`);
    console.log(`  Correlation ID: ${toolCallRequest.correlation_id}`);
    console.log(`  Arguments: ${JSON.stringify(toolCallRequest.tool_args, null, 2)}`);

    // 订阅 llm.tool_results 主题，等待本次请求的响应
    const sub = natsConnection.subscribe('llm.tool_results');
    console.log(`[Test Client] Subscribed to 'llm.tool_results' to wait for response with ID: ${correlationId}`);

    natsConnection.publish('llm.invoke_tool', sc.encode(JSON.stringify(toolCallRequest)));
    console.log(`[Test Client] Published tool call to MCP Server.`);
    
    console.log('[Test Client] Waiting for tool result from MCP Server...');
    
    let receivedResponse = false;
    const timeoutPromise = new Promise((resolve) => setTimeout(() => resolve('timeout'), 10000)); // 10秒超时
    
    // 使用 Promise.race 来实现超时
    const result = await Promise.race([
        (async () => {
            for await (const m of sub) {
                const response = JSON.parse(sc.decode(m.data));
                if (response.correlation_id === correlationId) {
                    // [优化] 详细打印接收的响应
                    console.log(`\n[Test Client] Received matching tool result for ID ${response.correlation_id}:`);
                    console.log(`  Tool Output:`);
                    for (const key in response.tool_output) {
                        console.log(`    ${key}: ${JSON.stringify(response.tool_output[key])}`);
                    }
                    receivedResponse = true;
                    return 'response_received';
                } else {
                    console.log(`[Test Client] Received unmatched response for ID ${response.correlation_id}. Still waiting for ${correlationId}.`);
                }
            }
            return 'subscription_ended'; // 如果订阅意外结束
        })(),
        timeoutPromise
    ]);

    if (result === 'timeout') {
        console.error('--- Step 2 FAILED: Did not receive a matching response within the 10-second timeout. ---');
    } else if (receivedResponse) {
        console.log('--- Step 2 PASSED ---');
    } else {
        console.error('--- Step 2 FAILED: Subscription ended without receiving a matching response. ---');
    }

    console.log('\n[Test Client] End-to-end test client finished.');

  } catch (err) {
    console.error('[Test Client] Test failed:', err);
  } finally {
    if (natsConnection) {
      await natsConnection.close();
      console.log('\n[Test Client] NATS connection closed.');
    }
  }
}

runTest();