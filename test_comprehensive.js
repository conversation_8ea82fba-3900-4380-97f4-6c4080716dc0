// test_comprehensive.js - 综合测试脚本：基站状态查询 + 功率配置
const { connect, StringCodec } = require('nats');
const { v4: uuidv4 } = require('uuid');

const sc = StringCodec();

async function runComprehensiveTest() {
  let natsConnection;
  try {
    console.log('[Comprehensive Test] 连接到NATS服务器...');
    natsConnection = await connect({ servers: "nats://113.45.77.38:4222" });
    console.log('[Comprehensive Test] NATS连接成功。');

    console.log('\n🚀 开始综合测试：基站状态监控 + 智能功率配置');
    console.log('=' .repeat(60));

    // 1. 获取工具列表
    console.log('\n=== 步骤1: 获取可用工具 ===');
    const toolsMsg = await natsConnection.request('mcp.get_llm_tools', '', { timeout: 5000 });
    const tools = JSON.parse(sc.decode(toolsMsg.data));
    
    console.log('可用工具:');
    tools.forEach(tool => {
      console.log(`  📋 ${tool.name}: ${tool.description}`);
    });

    // 2. 查询基站状态
    console.log('\n=== 步骤2: 查询基站状态 ===');
    const baseStations = await queryAllBaseStations(natsConnection);
    
    if (!baseStations || baseStations.length === 0) {
      console.log('⚠️ 未发现任何基站，请确保基站已连接并发送心跳报文');
      return;
    }

    // 3. 基于状态进行智能配置
    console.log('\n=== 步骤3: 基于状态进行智能功率配置 ===');
    await performIntelligentPowerConfiguration(natsConnection, baseStations);

    // 4. 再次查询状态验证配置效果
    console.log('\n=== 步骤4: 验证配置效果 ===');
    await verifyConfigurationResults(natsConnection, baseStations);

    console.log('\n✅ 综合测试完成！');
    console.log('=' .repeat(60));

  } catch (err) {
    console.error('[Comprehensive Test] 测试失败:', err);
  } finally {
    if (natsConnection) {
      await natsConnection.close();
      console.log('\n[Comprehensive Test] NATS连接已关闭。');
    }
  }
}

/**
 * 查询所有基站状态
 */
async function queryAllBaseStations(natsConnection) {
  const correlationId = uuidv4();
  
  const toolCallRequest = {
    tool_name: 'query_base_station_status',
    tool_args: { target_ip: '' },
    correlation_id: correlationId,
    target_ip: 'virtual'
  };

  console.log('🔍 正在查询所有基站状态...');
  
  const sub = natsConnection.subscribe('llm.tool_results');
  natsConnection.publish('llm.invoke_tool', sc.encode(JSON.stringify(toolCallRequest)));
  
  const result = await waitForResponse(sub, correlationId, 5000);
  
  if (result.success && result.response.tool_output.success) {
    const stations = result.response.tool_output.data;
    console.log(`📡 发现 ${stations.length} 个基站:`);
    
    stations.forEach((station, index) => {
      console.log(`\n  🏢 基站 ${index + 1}: ${station.基站信息.IP地址}`);
      console.log(`     状态: ${station.小区状态.状态描述}`);
      console.log(`     频点: UL=${station.频率信息.上行频点}, DL=${station.频率信息.下行频点}`);
      console.log(`     PCI: ${station.小区状态.物理小区ID}, 带宽: ${station.频率信息.带宽描述}`);
      console.log(`     数据新鲜度: ${station.技术参数.数据新鲜度}`);
    });
    
    return stations;
  } else {
    console.log('❌ 查询基站状态失败');
    return [];
  }
}

/**
 * 基于基站状态进行智能功率配置
 */
async function performIntelligentPowerConfiguration(natsConnection, baseStations) {
  console.log('🧠 开始智能功率配置分析...');
  
  for (let i = 0; i < baseStations.length; i++) {
    const station = baseStations[i];
    const ip = station.基站信息.IP地址;
    const cellState = station.小区状态.状态码;
    const pci = station.小区状态.物理小区ID;
    
    console.log(`\n🔧 配置基站 ${ip}:`);
    
    // 根据小区状态决定配置策略
    let powerConfig = null;
    let rxGainConfig = null;
    
    switch (cellState) {
      case 0: // 小区IDLE态
        console.log('  📊 检测到小区处于IDLE态，应用标准功率配置');
        powerConfig = { Pwr1Derease: 20, IsSave: 1, Res: [0, 0, 0] }; // 5dB衰减
        rxGainConfig = { Rxgain: 50, RxGainSaveFlag: 1, RxOrSnfFlag: 0, Res: [0, 0] };
        break;
        
      case 3: // 小区激活态
        console.log('  📊 检测到小区处于激活态，应用优化功率配置');
        powerConfig = { Pwr1Derease: 16, IsSave: 1, Res: [0, 0, 0] }; // 4dB衰减
        rxGainConfig = { Rxgain: 60, RxGainSaveFlag: 1, RxOrSnfFlag: 0, Res: [0, 0] };
        break;
        
      case 1: // 扫频/同步进行中
      case 6: // 同步中
        console.log('  📊 检测到小区正在同步，应用增强功率配置');
        powerConfig = { Pwr1Derease: 12, IsSave: 1, Res: [0, 0, 0] }; // 3dB衰减
        rxGainConfig = { Rxgain: 70, RxGainSaveFlag: 1, RxOrSnfFlag: 0, Res: [0, 0] };
        break;
        
      default:
        console.log('  📊 未知小区状态，应用默认功率配置');
        powerConfig = { Pwr1Derease: 24, IsSave: 1, Res: [0, 0, 0] }; // 6dB衰减
        rxGainConfig = { Rxgain: 55, RxGainSaveFlag: 1, RxOrSnfFlag: 0, Res: [0, 0] };
    }
    
    // 根据PCI进行微调
    if (pci > 0) {
      const pciAdjustment = (pci % 10) - 5; // -5到+4的调整
      powerConfig.Pwr1Derease += pciAdjustment;
      console.log(`  🎯 基于PCI(${pci})进行微调: ${pciAdjustment > 0 ? '+' : ''}${pciAdjustment}`);
    }
    
    // 执行发射功率配置
    try {
      await configureTxPower(natsConnection, ip, powerConfig);
      console.log(`  ✅ 发射功率配置成功: ${powerConfig.Pwr1Derease * 0.25}dB衰减`);
    } catch (error) {
      console.log(`  ❌ 发射功率配置失败: ${error.message}`);
    }
    
    // 等待一段时间再配置接收增益
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 执行接收增益配置
    try {
      await configureRxGain(natsConnection, ip, rxGainConfig);
      console.log(`  ✅ 接收增益配置成功: ${rxGainConfig.Rxgain}dB`);
    } catch (error) {
      console.log(`  ❌ 接收增益配置失败: ${error.message}`);
    }
    
    // 基站间配置间隔
    if (i < baseStations.length - 1) {
      console.log('  ⏳ 等待2秒后配置下一个基站...');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
}

/**
 * 配置发射功率
 */
async function configureTxPower(natsConnection, ip, config) {
  const correlationId = uuidv4();
  const toolCallRequest = {
    tool_name: 'configure_tx_power_attenuation',
    tool_args: config,
    correlation_id: correlationId,
    target_ip: ip
  };

  const sub = natsConnection.subscribe('llm.tool_results');
  natsConnection.publish('llm.invoke_tool', sc.encode(JSON.stringify(toolCallRequest)));

  const result = await waitForResponse(sub, correlationId, 8000);
  if (!result.success) {
    throw new Error(result.error);
  }

  return result.response;
}

/**
 * 配置接收增益
 */
async function configureRxGain(natsConnection, ip, config) {
  const correlationId = uuidv4();
  const toolCallRequest = {
    tool_name: 'configure_rx_gain',
    tool_args: config,
    correlation_id: correlationId,
    target_ip: ip
  };

  const sub = natsConnection.subscribe('llm.tool_results');
  natsConnection.publish('llm.invoke_tool', sc.encode(JSON.stringify(toolCallRequest)));

  const result = await waitForResponse(sub, correlationId, 8000);
  if (!result.success) {
    throw new Error(result.error);
  }

  return result.response;
}

/**
 * 配置发射功率
 */
async function configureTxPower(natsConnection, ip, config) {
  const correlationId = uuidv4();
  const toolCallRequest = {
    tool_name: 'configure_tx_power_attenuation',
    tool_args: config,
    correlation_id: correlationId,
    target_ip: ip
  };

  const sub = natsConnection.subscribe('llm.tool_results');
  natsConnection.publish('llm.invoke_tool', sc.encode(JSON.stringify(toolCallRequest)));
  
  const result = await waitForResponse(sub, correlationId, 8000);
  if (!result.success) {
    throw new Error(result.error);
  }
  
  return result.response;
}

/**
 * 配置接收增益
 */
async function configureRxGain(natsConnection, ip, config) {
  const correlationId = uuidv4();
  const toolCallRequest = {
    tool_name: 'configure_rx_gain',
    tool_args: config,
    correlation_id: correlationId,
    target_ip: ip
  };

  const sub = natsConnection.subscribe('llm.tool_results');
  natsConnection.publish('llm.invoke_tool', sc.encode(JSON.stringify(toolCallRequest)));
  
  const result = await waitForResponse(sub, correlationId, 8000);
  if (!result.success) {
    throw new Error(result.error);
  }
  
  return result.response;
}

/**
 * 验证配置结果
 */
async function verifyConfigurationResults(natsConnection, originalStations) {
  console.log('🔍 正在验证配置效果...');

  // 等待一段时间让配置生效
  await new Promise(resolve => setTimeout(resolve, 3000));

  const updatedStations = await queryAllBaseStations(natsConnection);

  if (updatedStations && updatedStations.length > 0) {
    console.log('\n📊 配置前后对比:');

    originalStations.forEach((original, index) => {
      const updated = updatedStations.find(s => s.基站信息.IP地址 === original.基站信息.IP地址);
      if (updated) {
        console.log(`\n  🏢 基站 ${original.基站信息.IP地址}:`);
        console.log(`     状态变化: ${original.小区状态.状态描述} → ${updated.小区状态.状态描述}`);
        console.log(`     数据更新: ${original.技术参数.数据新鲜度} → ${updated.技术参数.数据新鲜度}`);
      }
    });
  }
}

/**
 * 等待响应
 */
async function waitForResponse(subscription, correlationId, timeoutMs) {
  return new Promise((resolve) => {
    const timeout = setTimeout(() => {
      resolve({ success: false, error: '响应超时' });
    }, timeoutMs);

    (async () => {
      for await (const m of subscription) {
        try {
          const response = JSON.parse(sc.decode(m.data));
          if (response.correlation_id === correlationId) {
            clearTimeout(timeout);
            resolve({ success: true, response });
            break;
          }
        } catch (parseError) {
          console.error('[Comprehensive Test] 解析响应失败:', parseError);
        }
      }
    })();
  });
}

// 运行测试
if (require.main === module) {
  runComprehensiveTest();
}

/**
 * 等待响应
 */
async function waitForResponse(subscription, correlationId, timeoutMs) {
  return new Promise((resolve) => {
    const timeout = setTimeout(() => {
      resolve({ success: false, error: '响应超时' });
    }, timeoutMs);

    (async () => {
      for await (const m of subscription) {
        try {
          const response = JSON.parse(sc.decode(m.data));
          if (response.correlation_id === correlationId) {
            clearTimeout(timeout);
            resolve({ success: true, response });
            break;
          }
        } catch (parseError) {
          console.error('[Comprehensive Test] 解析响应失败:', parseError);
        }
      }
    })();
  });
}

// 运行测试
if (require.main === module) {
  runComprehensiveTest();
}

module.exports = { runComprehensiveTest };
