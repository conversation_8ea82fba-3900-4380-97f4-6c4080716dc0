# 基站状态监控功能说明

## 🎯 功能概述

MCP Server现在具备了从UDP Server心跳报文中解析基站状态信息的能力，可以实时获取基站的IP、端口、SN、状态、频点、干扰、PCI等关键参数。

## 📋 支持的基站信息

### 基站基础信息
- **IP地址**: 基站的网络地址
- **端口**: UDP通信端口
- **SN号**: 基站序列号（如果心跳报文携带）
- **是否携带SN**: 标识心跳报文是否包含SN信息
- **心跳次数**: 累计接收的心跳报文数量
- **最后心跳时间**: 最近一次心跳的时间戳

### 小区状态信息
- **状态码**: 数字状态码（0-6）
- **状态描述**: 中文状态说明
  - 0: 小区IDLE态
  - 1: 扫频/同步进行中
  - 2: 小区激活中
  - 3: 小区激活态
  - 4: 小区去激活中
  - 5: 同步成功，REM处于ON状态
  - 6: 同步中
- **PLMN**: 公共陆地移动网络标识
- **物理小区ID (PCI)**: 物理层小区标识
- **跟踪区码 (TAC)**: 跟踪区域代码

### 频率信息
- **频段 (Band)**: LTE频段
- **上行频点 (ulEarfcn)**: 上行E-UTRA绝对射频信道号
- **下行频点 (dlEarfcn)**: 下行E-UTRA绝对射频信道号
- **系统带宽**: 数字带宽值
- **带宽描述**: 带宽的文字描述（5M/10M/15M/20M）

### 技术参数
- **更新时间戳**: 数据更新的时间戳
- **数据新鲜度**: 距离最后更新的时间间隔

## 🔧 新增工具

### `query_base_station_status`
查询基站的实时状态信息

**参数:**
- `target_ip` (可选): 指定基站IP地址，留空则返回所有基站状态

**返回格式:**
```json
{
  "success": true,
  "data": [
    {
      "基站信息": {
        "IP地址": "*************",
        "端口": 8080,
        "SN号": "1234567890abcdef",
        "是否携带SN": "是",
        "心跳次数": 15,
        "最后心跳时间": "2024-01-01T12:00:00.000Z"
      },
      "小区状态": {
        "状态码": 3,
        "状态描述": "小区激活态",
        "PLMN": "46000",
        "物理小区ID": 123,
        "跟踪区码": 456
      },
      "频率信息": {
        "频段": 3,
        "上行频点": 18000,
        "下行频点": 1800,
        "系统带宽": 100,
        "带宽描述": "20M"
      },
      "技术参数": {
        "更新时间戳": 1704110400000,
        "数据新鲜度": "5秒前"
      }
    }
  ],
  "total": 1,
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## 🚀 使用方法

### 1. 基本状态查询

```javascript
// 查询所有基站状态
const toolCallRequest = {
  tool_name: 'query_base_station_status',
  tool_args: {
    target_ip: '' // 空字符串表示查询所有基站
  },
  correlation_id: 'unique-id',
  target_ip: 'virtual' // 虚拟工具不需要真实IP
};

// 查询特定基站状态
const specificQuery = {
  tool_name: 'query_base_station_status',
  tool_args: {
    target_ip: '*************' // 指定基站IP
  },
  correlation_id: 'unique-id',
  target_ip: 'virtual'
};
```

### 2. 智能功率配置

基于基站状态自动调整功率参数：

```javascript
// 根据小区状态智能配置
switch (cellState) {
  case 0: // IDLE态 - 标准功率
    powerConfig = { Pwr1Derease: 20, IsSave: 1, Res: [0, 0, 0] };
    break;
  case 3: // 激活态 - 优化功率
    powerConfig = { Pwr1Derease: 16, IsSave: 1, Res: [0, 0, 0] };
    break;
  case 1: // 同步中 - 增强功率
  case 6:
    powerConfig = { Pwr1Derease: 12, IsSave: 1, Res: [0, 0, 0] };
    break;
}
```

## 🧪 测试脚本

### 基础测试
```bash
# 测试基站状态查询功能
node test_base_station_status.js
```

### 综合测试
```bash
# 综合测试：状态监控 + 智能配置
node test_comprehensive.js
```

### 多基站测试
```bash
# 测试多基站并行操作
node test_multi_device.js
```

## 📊 系统架构

```
┌─────────────┐    心跳报文    ┌─────────────┐    NATS消息    ┌─────────────┐
│   基站设备   │ ──────────→ │ UDP Server  │ ──────────→ │ MCP Server  │
│             │              │             │              │             │
│ - IP/Port   │              │ - 解析心跳   │              │ - 状态缓存   │
│ - SN号      │              │ - 提取参数   │              │ - 工具调用   │
│ - 频点/PCI  │              │ - 发布状态   │              │ - 智能配置   │
└─────────────┘              └─────────────┘              └─────────────┘
                                     │                             │
                                     ▼                             ▼
                              base_station.status_update    llm.tool_results
                                     │                             │
                                     └─────────── NATS ───────────┘
```

## 🔄 数据流程

1. **心跳接收**: UDP Server接收基站心跳报文
2. **信息解析**: 从心跳报文中提取基站状态信息
3. **状态发布**: 通过NATS发布基站状态更新
4. **状态缓存**: MCP Server接收并缓存基站状态
5. **工具调用**: LLM通过工具查询基站状态
6. **智能配置**: 基于状态信息进行智能功率配置

## ⚠️ 注意事项

1. **数据依赖**: 基站状态信息依赖于心跳报文，确保基站正常发送心跳
2. **时效性**: 状态信息的新鲜度取决于心跳频率
3. **网络连接**: 需要确保NATS服务器连接正常
4. **虚拟工具**: `query_base_station_status`是虚拟工具，不发送UDP消息
5. **多基站支持**: 系统完全支持多基站环境，通过IP地址区分不同设备

## 🎉 功能特点

- ✅ **实时监控**: 基于心跳报文的实时状态更新
- ✅ **多基站支持**: 同时监控多个基站设备
- ✅ **智能配置**: 基于状态的自动功率优化
- ✅ **数据完整**: 涵盖基站的所有关键参数
- ✅ **易于集成**: 标准的MCP工具接口
- ✅ **高可靠性**: 完善的错误处理和超时机制
