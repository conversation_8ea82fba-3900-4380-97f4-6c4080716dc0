const ENCRYPTION_KEY = Buffer.from([0x5A, 0x3C, 0x7E, 0x19, 0x2F, 0x6D, 0x4B, 0x8A]);

function generateSeed(transId) {
    const seed = Buffer.alloc(8);
    for (let i = 0; i < 8; i++) {
        seed[i] = ENCRYPTION_KEY[i] ^
            ((transId >> (i % 4)) & 0xFF) ^
            ((transId << (7 - (i % 8))) & 0xFF);
    }
    return seed;
}

/**
 * 加密消息
 * @param {Buffer} buffer - 原始消息Buffer
 * @param {boolean} [hasSN=false] - 消息是否包含SN号 (决定头部偏移量)
 * @returns {Buffer} - 加密后的消息Buffer
 */
function encryptMessage(buffer, hasSN = false) {
    const headerOffset = hasSN ? 32 : 12; // 根据是否带SN号确定头部偏移
    if (buffer.length < headerOffset) return buffer; // 消息太短，无法加密

    const encrypted = Buffer.from(buffer);
    const transId = Math.floor(Math.random() * 0x7FFF) + 1; // 生成随机TransId
    
    // 将TransId写入SubSysCode字段 (偏移量10和11)
    encrypted[10] = transId & 0xFF;
    encrypted[11] = (transId >> 8) & 0x7F; // 保持最高位为0 (传输完成标志)

    const seed = generateSeed(transId);
    
    // 从正确的偏移量开始XOR加密
    for (let i = headerOffset; i < encrypted.length; i++) {
        encrypted[i] ^= seed[i % seed.length];
    }
    return encrypted;
}

/**
 * 解密消息
 * @param {Buffer} buffer - 加密的消息Buffer
 * @param {boolean} [hasSN=false] - 消息是否包含SN号 (决定头部偏移量)
 * @returns {Buffer} - 解密后的消息Buffer
 */
function decryptMessage(buffer, hasSN = false) {
    const headerOffset = hasSN ? 32 : 12; // 根据是否带SN号确定头部偏移
    if (buffer.length < headerOffset) {
        // console.log('WARN: 消息太短，无法解密。'); // 移除通用警告，由调用者处理
        return buffer;
    }
    
    const decrypted = Buffer.from(buffer);
    // 从SubSysCode字段提取TransId
    const transId = (decrypted[10] & 0xFF) | ((decrypted[11] & 0x7F) << 8);
    
    // 如果TransId为0，则消息未加密 (根据协议文档)
    if (transId === 0) {
        return decrypted;
    }

    const seed = generateSeed(transId);
    
    // 从正确的偏移量开始XOR解密
    for (let i = headerOffset; i < decrypted.length; i++) {
        decrypted[i] ^= seed[i % seed.length];
    }
    return decrypted;
}

module.exports = { encryptMessage, decryptMessage };