{"name": "device-manager", "version": "1.0.0", "description": "Node.js project to control OpenWrt embedded board via SSH and UDP.", "main": "index.js", "scripts": {"start": "node index.js", "start:all": "concurrently --prefix \"[{name}]\" --names \"MAIN,AUTH,MCP\" --prefix-colors \"cyan,magenta,yellow\" \"node index.js\" \"node auth_server/app.js\" \"node mcp_server/app.js\"", "start:main": "node index.js", "start:auth": "node auth_server/app.js", "start:mcp": "node mcp_server/app.js", "build": "mkdir -p dist && pkg . --output dist/device-manager", "postbuild": "cp -R public dist/ && [ -d views ] && cp -R views dist/ || true && [ -f 3.txt ] && cp 3.txt dist/ || true", "test:e2e": "AUTO_TEST_MODE=true concurrently \"node index.js\" \"node auth_server/app.js\" \"node mcp_server/app.js\" \"node test/test_mcp_client.js\"", "test:status": "node test/test_base_station_status.js", "test:comprehensive": "node test/test_comprehensive.js", "test:natural": "node test/test_natural_language_demo.js"}, "keywords": ["openwrt", "ssh", "udp", "embedded"], "author": "", "license": "ISC", "dependencies": {"ejs": "^3.1.10", "express": "^4.21.2", "markdown-it": "^14.1.0", "nats": "^2.29.3", "node-ssh": "^13.1.5", "sqlite3": "^5.1.7", "uuid": "^11.1.0"}, "devDependencies": {"concurrently": "^9.2.0", "pkg": "^5.8.1"}, "bin": "index.js", "pkg": {"targets": ["node18-win-x64", "node18-linux-x64", "node18-macos-x64"], "scripts": ["index.js", "ssh_client.js", "udp_server.js", "protocol_pack.js", "protocol_crypto.js"], "excludes": ["auth_server/**/*", "mcp_server/**/*"]}}