{"name": "device-manager", "version": "1.0.0", "description": "Node.js project to control OpenWrt embedded board via SSH and UDP.", "main": "index.js", "scripts": {"start": "node index.js", "build": "mkdir -p dist && pkg . --output dist/device-manager", "postbuild": "cp -R public dist/ && [ -d views ] && cp -R views dist/ || true && [ -f 3.txt ] && cp 3.txt dist/ || true", "test:e2e": "AUTO_TEST_MODE=true concurrently \"node index.js\" \"node auth_server/app.js\" \"node mcp_server/app.js\" \"node test_mcp_client.js\""}, "keywords": ["openwrt", "ssh", "udp", "embedded"], "author": "", "license": "ISC", "dependencies": {"ejs": "^3.1.10", "express": "^4.21.2", "markdown-it": "^14.1.0", "nats": "^2.29.3", "node-ssh": "^13.1.5", "sqlite3": "^5.1.7", "uuid": "^11.1.0"}, "devDependencies": {"concurrently": "^9.2.0", "pkg": "^5.8.1"}, "bin": "index.js", "pkg": {"targets": ["node18-win-x64", "node18-linux-x64", "node18-macos-x64"], "scripts": ["index.js", "ssh_client.js", "udp_server.js", "protocol_pack.js", "protocol_crypto.js"], "excludes": ["auth_server/**/*", "mcp_server/**/*"]}}