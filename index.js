const express = require('express');
const path = require('path');
const fs = require('fs'); // Import fs.promises for async file operations
const os = require('os'); // Import os for temporary directory
const child_process = require('child_process');



// Disable console logs when running as a packaged executable
if (process.env.PKG_EXEC) {
    console.log = function() {};
    console.warn = function() {};
    console.info = function() {};
    // Keep console.error to still log critical errors
}

const { SshClient } = require('./ssh_client');
const { UdpServer } = require('./udp_server');
const protocolDefs = require('./mcp_server/protocol/definitions');
const protocolBuilder = require('./mcp_server/protocol/builder');

const app = express();""
const PORT = process.env.PORT || 3000;

// Serve static files from the 'public' directory (for frontend later)
app.use(express.static(path.join(process.cwd(), 'public')));

// Basic API for future configuration (e.g., board IP, port)
app.use(express.json());

// Initialize SSH client (configuration will come from frontend/env vars)
const sshClient = new SshClient();

// Initialize UDP server (configuration will come from frontend/env vars)
const udpServer = new UdpServer();

// SSE (Server-Sent Events) Setup
const clients = [];

// Function to send upgrade completion message to all connected SSE clients
function sendUpgradeCompletion(message) {
    clients.forEach(client => {
        client.write(`data: ${JSON.stringify(message)}\n\n`);
    });
}

// Pass the SSE sender function to the UdpServer instance
udpServer.setUpgradeCompletionSender(sendUpgradeCompletion);

app.get('/events', (req, res) => {
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.flushHeaders(); // Flush the headers to establish the SSE connection immediately

    clients.push(res);

    req.on('close', () => {
        // Remove client when connection closes
        clients.splice(clients.indexOf(res), 1);
    });
});

// SSH API Endpoints
app.post('/api/ssh/connect', async (req, res) => {
    const { host } = req.body;
    const success = await sshClient.connect({ host });
    if (success) {
        const filePath = '/etc/userReservedCfg.txt';
        try {
            const command = `head -n 5 ${filePath}`;
            // console.log(`Executing: ${command} after successful SSH connection`);
            const result = await sshClient.execCommand(command);

            if (result.code === 0) {
                const configContent = result.stdout;
                const lines = configContent.split('\n');

                let appPort = null;
                let webPort = null;
                let appIp = null;
                let webIp = null;

                // 解析第一行获取端口值
                if (lines.length >= 1) {
                    const firstLine = lines[0];
                    const portMatch = firstLine.match(/=(.*)/);
                    if (portMatch && portMatch[1]) {
                        appPort = portMatch[1].trim();
                        webPort = portMatch[1].trim(); // 假设App和Web模式的上报端口相同
                    }
                }

                // 解析第二行获取IP值
                if (lines.length >= 2) {
                    const secondLine = lines[1];
                    const ipMatch = secondLine.match(/=(.*)/);
                    if (ipMatch && ipMatch[1]) {
                        appIp = ipMatch[1].trim();
                        webIp = ipMatch[1].trim(); // 假设App和Web模式的上报IP相同
                    }
                }

                // 解析第五行获取当前模式
                let currentMode = null;
                if (lines.length >= 5) {
                    const fifthLine = lines[4];
                    const modeMatch = fifthLine.match(/=(.*)/);
                    if (modeMatch && modeMatch[1]) {
                        const modeFlag = parseInt(modeMatch[1].trim(), 10);
                        if (modeFlag === 0) {
                            currentMode = 'app';
                        } else if (modeFlag === 1) {
                            currentMode = 'web';
                        }
                    }
                }

                res.status(200).send({
                    message: 'dev connected successfully',
                    // configContent: configContent, // 包含原始文件内容，方便调试
                    appPort: appPort,
                    webPort: webPort,
                    appIp: appIp,
                    webIp: webIp,
                    currentMode: currentMode // 返回当前模式
                });
            } else {
                console.error(`Error reading config file after  connect: ${result.stderr}`);
                res.status(500).send({ message: 'connected, but failed to read config infor.', configReadError: result.stderr, stdout: result.stdout, stderr: result.stderr, exitCode: result.exitCode });
            }
        } catch (error) {
            console.error('Error reading config infor after  connect endpoint:', error);
            res.status(500).send({ message: `dev connected, but server error during config file reading: ${error.message}` });
        }
    } else {
        res.status(500).send({ message: 'dev connection failed.' });
    }
});

app.post('/api/ssh/exec', async (req, res) => {
    const { command } = req.body;
    console.log(`Attempting to execute command: '${command}'`);
    try {
        const result = await sshClient.execCommand(command);
        console.log(' Exec Command Result:', { stdout: result.stdout, stderr: result.stderr, exitCode: result.exitCode });
        if (result.exitCode === 0) {
            res.status(200).send({ message: 'Command executed successfully.', stdout: result.stdout, stderr: result.stderr });
        } else {
            res.status(500).send({ message: 'Command execution failed.', stdout: result.stdout, stderr: result.stderr, exitCode: result.exitCode });
        }
    } catch (error) {
        console.error('Error in /api/ssh/exec endpoint:', error.message);
        res.status(500).send({ message: 'Command execution failed due to server error.', error: error.message });
    }
});

app.post('/api/ssh/upload', async (req, res) => {
    const { localPath, remotePath } = req.body;
    // In a real application, you'd handle file uploads more securely (e.g., multer)
    // For now, assume localPath refers to a file accessible by the server.
    const success = await sshClient.uploadFile(localPath, remotePath);
    if (success) {
        res.status(200).send({ message: 'File uploaded successfully.' });
    } else {
        res.status(500).send({ message: 'File upload failed.' });
    }
});

app.post('/api/ssh/chmod', async (req, res) => {
    const { remotePath, permissions } = req.body;
    const result = await sshClient.changeFilePermissions(remotePath, permissions);
    if (result.exitCode === 0) {
        res.status(200).send({ message: 'File permissions changed successfully.', stdout: result.stdout, stderr: result.stderr });
    } else {
        res.status(500).send({ message: 'Failed to change file permissions.', stdout: result.stdout, stderr: result.stderr, exitCode: result.exitCode });
    }
});

// New API Endpoint for modifying config file
app.post('/api/board/modifyConfigFile', async (req, res) => {
    const { mode, webPort, reportIp } = req.body;
    const filePath = '/etc/userReservedCfg.txt'; // 后端写死
    if (!mode) {
        return res.status(400).send({ message: 'Missing mode.' });
    }
    if (mode === 'web' && !webPort) {
        return res.status(400).send({ message: 'Web mode requires webPort.' });
    }
    if (reportIp) { // 检查是否提供了 reportIp
        const commandLine2 = `sed -i '2s/=.*/= ${reportIp}/' ${filePath}`;
        console.log(`Executing: ${commandLine2}`);
        let result2 = await sshClient.execCommand(commandLine2);
        if (result2.code !== 0) {
            console.error(`Error executing command for line 2 (reportIp): ${result2.stderr}`);
            return res.status(500).send({ message: `Failed to set report IP: ${result2.stderr}`, stdout: result2.stdout, stderr: result2.stderr });
        }
    }

    try {
        let portValue;
        if (mode === 'app') {
            portValue = 3345;
        } else if (mode === 'web') {
            portValue = webPort;
        }

        // Command to modify the first line (port number)
        const commandLine1 = `sed -i '1s/=.*/= ${portValue}/' ${filePath}`;
        console.log(`Executing: ${commandLine1}`);
        let result1 = await sshClient.execCommand(commandLine1);
        // if (result1.exitCode !== 0) {
        //     console.error(`Error executing command for line 1: ${result1.stderr}`);
        //     return res.status(500).send({ message: `Failed to set port for ${mode} mode: ${result1.stderr}`, stdout: result1.stdout, stderr: result1.stderr });
        // }

        // Command to modify the fifth line (WR_TAG_MAIN_CONTROL_PANEL_MODE_FLAG)
        let modeFlagValue;
        if (mode === 'app') {
            modeFlagValue = 0;
        } else if (mode === 'web') {
            modeFlagValue = 1;
        }
        const commandLine5 = `sed -i '5s/=.*/= ${modeFlagValue}/' ${filePath}`;
        console.log(`Executing: ${commandLine5}`);
        let result5 = await sshClient.execCommand(commandLine5);
        // if (result5.exitCode !== 0) {
        //     console.error(`Error executing command for line 5: ${result5.stderr}`);
        //     return res.status(500).send({ message: `Failed to set mode flag for ${mode} mode: ${result5.stderr}`, stdout: result5.stdout, stderr: result5.stderr });
        // }
        
        res.status(200).send({ message: `File modified to ${mode} mode successfully using sed.` });

    } catch (error) {
        console.error('Error in /api/board/modifyConfigFile endpoint:', error);
        res.status(500).send({ message: `Server error during file modification: ${error.message}` });
    }
});

// New API Endpoint for reading config file
app.get('/api/board/readConfigFile', async (req, res) => {
    const filePath = '/etc/userReservedCfg.txt'; // 后端写死
    try {
        const command = `head -n 5 ${filePath}`;
        console.log(`Executing: ${command}`);
        const result = await sshClient.execCommand(command);

        if (result.exitCode === 0) {
            res.status(200).send({ message: 'Config file read successfully.', content: result.stdout });
        } else {
            console.error(`Error reading config file: ${result.stderr}`);
            res.status(500).send({ message: `Failed to read config file: ${result.stderr}`, stdout: result.stdout, stderr: result.stderr, exitCode: result.exitCode });
        }
    } catch (error) {
        console.error('Error in /api/board/readConfigFile endpoint:', error);
        res.status(500).send({ message: `Server error during config file reading: ${error.message}` });
    }
});

// UDP API Endpoints
app.post('/api/udp/start', (req, res) => {
    const { listenIp, listenPort, boards, boardIp, boardPort } = req.body;

    // Validate required parameters
    if (!listenIp || !listenPort) {
        return res.status(400).send({ message: 'Missing listenIp or listenPort parameters.' });
    }

    // Handle boards array
    if (boards && Array.isArray(boards)) {
        if (boards.length > 0) {
            console.log('[UDP] 启动参数 boards:', boards);
            udpServer.start(listenIp, listenPort, boards);
        } else {
            console.log('[UDP] 启动UDP服务器，无基站设备配置');
            udpServer.start(listenIp, listenPort, []);
        }
    } else if (boardIp && boardPort) {
        // Legacy single board support
        console.log('[UDP] 启动参数 boardIp/boardPort:', boardIp, boardPort);
        udpServer.start(listenIp, listenPort, [{ ip: boardIp, port: boardPort }]);
    } else {
        // No boards specified, start with empty array
        console.log('[UDP] 启动UDP服务器，无基站设备配置');
        udpServer.start(listenIp, listenPort, []);
    }

    res.status(200).send({ message: 'UDP server started (or already running).' });
});

app.post('/api/udp/stop', (req, res) => {
    udpServer.stop();
    res.status(200).send({ message: 'UDP server stopped.' });
});

app.post('/api/udp/send', (req, res) => {
    const { message, targetIp } = req.body;
    if (!message || !targetIp) {
        return res.status(400).send({ message: 'Missing UDP message or targetIp.' });
    }
    udpServer.sendMessageToBoard(message, targetIp);
    res.status(200).send({ message: 'UDP message sent (or attempted).' });
});

// [NEW] API Endpoints for dynamic device management
app.post('/api/udp/device', (req, res) => {
    const { ip, port } = req.body;
    if (!ip || !port) {
        return res.status(400).send({ message: 'Missing ip or port for the device.' });
    }
    const success = udpServer.addDevice({ ip, port });
    if (success) {
        res.status(200).send({ message: `Device ${ip} added/updated.` });
    } else {
        res.status(500).send({ message: 'Failed to add device.' });
    }
});

app.delete('/api/udp/device/:ip', (req, res) => {
    const { ip } = req.params;
    if (!ip) {
        return res.status(400).send({ message: 'Missing device IP in URL.' });
    }
    const success = udpServer.removeDevice(ip);
    if (success) {
        res.status(200).send({ message: `Device ${ip} removed.` });
    } else {
        res.status(404).send({ message: `Device ${ip} not found.` });
    }
});


// Example route for testing - remove later
app.get('/', (req, res) => {
    res.send('OpenWrt Board Controller Backend Running!');
});

app.post('/api/upgrade', async (req, res) => {
    const files = [
        { local: '1.txt', remote: '/usr/sbin/fdd_rsys.gz' },
        { local: '2.txt', remote: '/usr/sbin/tdd_rsys.gz' }
    ];
    try {
        // for (const file of files) {
        //     // 上传文件
        //     const uploadSuccess = await sshClient.uploadFile(file.local, file.remote);
        //     if (!uploadSuccess) {
        //         return res.status(500).send({ message: `文件 ${file.local} 上传失败` });
        //     }
        //     // 修改权限
        //     const chmodResult = await sshClient.changeFilePermissions(file.remote, '777');
        //     if (chmodResult.exitCode !== 0) {
        //         return res.status(500).send({ message: `文件 ${file.remote} 上传成功，但设置权限失败`, stdout: chmodResult.stdout, stderr: chmodResult.stderr });
        //     }
        // }
        // 检查3.txt是否存在
        if (fs.existsSync('3.txt')) {
            const uploadSuccess = await sshClient.uploadFile('3.txt', '/tmp/BaiStation128W_TDD_FDD_R006B005SPC014_ETWS_20250613.IMG');
            if (!uploadSuccess) {
                return res.status(500).send({ message: '文件 3.txt 上传失败' });
            }
            // const chmodResult = await sshClient.changeFilePermissions('/tmp/3.txt', '777');
            // if (chmodResult.exitCode !== 0) {
            //     return res.status(500).send({ message: '文件 /tmp/3.txt 上传成功，但设置权限失败', stdout: chmodResult.stdout, stderr: chmodResult.stderr });
            // }
            // 执行 imageUpgrade 命令
            const upgradeResult = await sshClient.execCommand('ImageUpgrade /tmp/BaiStation128W_TDD_FDD_R006B005SPC014_ETWS_20250613.IMG');
            if( upgradeResult.code === 0  ){
                return res.status(200).send({ message: '升级成功' });
            }else{
                return res.status(500).send({ message: '升级命令执行失败', stdout: upgradeResult.stdout, stderr: upgradeResult.stderr });
            }
        }
        res.status(200).send({ message: '升级成功，所有文件已上传并设置为777权限，3.txt已处理（如存在）' });
    } catch (error) {
        res.status(500).send({ message: '升级失败', error: error.message });
    }
});

app.post('/api/reboot-station', async (req, res) => {
    const { targetIp } = req.body;
    if (!targetIp) {
        return res.status(400).send({ message: 'Missing targetIp for reboot command.' });
    }

    try {
        // The command name must match the key in definitions.js
        const commandName = 'configure_reboot';
        const args = { SelfActiveCfg: 0 }; // 0: auto-activate cell after reboot

        // Build the protocol message buffer
        const messageBuffer = protocolBuilder.build(commandName, args);

        // Send the message via the UDP server
        udpServer.sendMessageToBoard(messageBuffer, targetIp);

        console.log(`Sent reboot command (0xF00B) to ${targetIp}`);
        res.status(200).send({ message: `Reboot command sent to ${targetIp}. Please wait a moment for the device to restart.` });

    } catch (error) {
        console.error('Error building or sending reboot command:', error);
        res.status(500).send({ message: 'Failed to send reboot command.', error: error.message });
    }
});

// Power attenuation control API
app.post('/api/set-power-attenuation', async (req, res) => {
    const { targetIp, attenuationDb } = req.body;

    if (!targetIp) {
        return res.status(400).send({ message: 'Missing targetIp for power attenuation command.' });
    }

    if (attenuationDb === undefined || attenuationDb === null) {
        return res.status(400).send({ message: 'Missing attenuationDb parameter.' });
    }

    // Validate attenuation value range (0-100 dB)
    if (attenuationDb < 0 || attenuationDb > 100) {
        return res.status(400).send({ message: 'Attenuation value must be between 0 and 100 dB.' });
    }

    try {
        // The command name must match the key in definitions.js
        const commandName = 'configure_tx_power_attenuation';
        const args = {
            Pwr1Derease: Math.round(attenuationDb * 4), // Convert to 0.25dB units as expected by protocol
            IsSave: 1, // Save configuration to persist after reboot
            Res: [0, 0, 0] // Reserved bytes
        };

        // Build the protocol message buffer
        const messageBuffer = protocolBuilder.build(commandName, args);

        // Send the message via the UDP server
        udpServer.sendMessageToBoard(messageBuffer, targetIp);

        console.log(`Sent power attenuation command to ${targetIp}: ${attenuationDb} dB (Pwr1Derease: ${args.Pwr1Derease})`);
        res.status(200).send({
            message: `功率衰减已设置为 ${attenuationDb} dB，目标设备：${targetIp}`,
            targetIp: targetIp,
            attenuationDb: attenuationDb,
            actualValue: args.Pwr1Derease
        });

    } catch (error) {
        console.error('Error building or sending power attenuation command:', error);
        res.status(500).send({ message: '功率衰减设置失败', error: error.message });
    }
});

// Query current power attenuation API
app.post('/api/query-power-attenuation', async (req, res) => {
    const { targetIp } = req.body;

    if (!targetIp) {
        return res.status(400).send({ message: 'Missing targetIp for power query command.' });
    }

    try {
        // Use the more comprehensive power query command
        const commandName = 'query_rx_gain_power_decrease';
        const args = {}; // No parameters needed

        // Build the protocol message buffer
        const messageBuffer = protocolBuilder.build(commandName, args);

        // Send the message via the UDP server
        udpServer.sendMessageToBoard(messageBuffer, targetIp);

        console.log(`Sent power query command (0xF031) to ${targetIp}`);
        res.status(200).send({
            message: `功率查询命令已发送到 ${targetIp}，请等待响应`,
            targetIp: targetIp,
            queryType: 'rx_gain_power_decrease'
        });

    } catch (error) {
        console.error('Error building or sending power query command:', error);
        res.status(500).send({ message: '功率查询失败', error: error.message });
    }
});

// [新增] 检查端口是否被占用并杀掉占用进程
async function ensurePortFree(port) {
    return new Promise((resolve, reject) => {
        // lsof -i :port | grep LISTEN | awk '{print $2}'
        child_process.exec(`lsof -i :${port} -sTCP:LISTEN -t`, (err, stdout, stderr) => {
            if (err) {
                // 没有进程占用端口
                return resolve();
            }
            const pids = stdout.split('\n').filter(Boolean);
            if (pids.length === 0) return resolve();
            // 杀掉所有占用端口的进程
            let killed = 0;
            pids.forEach(pid => {
                try {
                    process.kill(pid, 'SIGKILL');
                    killed++;
                } catch (e) {
                    // 可能没有权限杀死进程
                    console.error(`无法杀死进程 ${pid}:`, e.message);
                }
            });
            setTimeout(resolve, 500); // 等待进程释放端口
        });
    });
}

// 启动服务前确保端口空闲
(async () => {
    await ensurePortFree(PORT);
    app.listen(PORT, () => {
        console.log(`Server is running on port ${PORT}`);
    });
})();

// [新增代码] 自动化测试模式
if (process.env.AUTO_TEST_MODE === 'true') {
    console.log('[Auto Test Mode] Enabled. Starting UDP server automatically.');
    const listenIp = '************';
    const listenPort = 3345;
    const boardIp = '*************';
    const boardPort = 3345;
    
    // 延迟一小段时间确保HTTP服务器完全启动
    setTimeout(() => {
        udpServer.start(listenIp, listenPort, boardIp, boardPort);
        console.log(`[Auto Test Mode] UDP Server started. Listening on ${listenIp}:${listenPort}, targeting board ${boardIp}:${boardPort}.`);
    }, 2000);
} 